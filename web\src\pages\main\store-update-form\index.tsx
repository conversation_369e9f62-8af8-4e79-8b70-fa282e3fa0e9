import React, { useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import {
  Card,
  Form,
  Button,
  Upload,
  Message,
  Space,
  Modal,
  Input,
  Select
} from '@arco-design/web-react';
import { 
  IconArrowLeft 
} from '@arco-design/web-react/icon';

import api from '@/utils/api';
import consts from '@/const';
import useLocale from '@/utils/useLocale';
import useStorage from '@/utils/useStorage';

import locale from './locale';
import styles from './style/index.module.less';

const API_CONTEXT = '/api/v1';
const API_UPLOAD_TASKS = API_CONTEXT + '/upload-tasks';
const CHUNK_SIZE = 10 * 1024 * 1024; // 分块大小，例如10MB

const { useForm } = Form;

function UpdateForm() {
  const history = useHistory();
  const location = useLocation();
  const t = useLocale(locale);
  const [loading, setLoading] = useState(false);
  const [ parentHost ] = useStorage(consts.KEY_AIS_SERVER_PARENT_HOST);
  const [ serverUrl ] = useStorage(consts.KEY_AIS_SERVER_PARENT_API_SERVER);
  const [ patches, setPatches ] = useState([]);
  const isServer = parentHost === '' || parentHost === 'localhost' || parentHost === '127.0.0.1';
  const [form] = useForm();

  const isAcceptFile = (file, accept) => {
    if (accept && file) {
      const accepts = Array.isArray(accept)
        ? accept
        : accept
            .split(',')
            .map((x) => x.trim())
            .filter((x) => x);
      const fileExtension = file.name.indexOf('.') > -1 ? file.name.split('.').pop() : '';
      return accepts.some((type) => {
        const text = type && type.toLowerCase();
        const fileType = (file.type || '').toLowerCase();
        if (text === fileType) {
          // 类似excel文件这种
          // 比如application/vnd.ms-excel和application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
          // 本身就带有.字符的，不能走下面的.zip等文件扩展名判断处理
          // 所以优先对比input的accept类型和文件对象的type值
          return true;
        }
        if (new RegExp('\/\*').test(text)) {
          // application/* 这种通配的形式处理
          const regExp = new RegExp('\/.*$')
          return fileType.replace(regExp, '') === text.replace(regExp, '');
        }
        if (new RegExp('\..*').test(text)) {
          // .zip 后缀名
          return text === `.${fileExtension && fileExtension.toLowerCase()}`;
        }
        return false;
      });
    }
    return !!file;
  }

  const doAdd = async (data, callback) => {
    api(API_UPLOAD_TASKS, {
      method: 'post',
      data: {
        packageName: data.name,
        packageVersion: data.version,
      }
    }).then((res) => {
      const resData = res && res.data;
      if (resData && resData.code === 0) {
        callback && callback(resData.data);
      } else {
        Message.error(t['form.updated.fail']);
      }
    }).catch(() => {
      Message.error(t['form.updated.fail']);
    })
  }

  const doUploadChunk = async (data, callback) => {
    if (data.chunkContent != null) {
      api(API_UPLOAD_TASKS + "/" + data.id + "/chunks", {
        method: 'post',
        data: {
          id: data.id,
          chunkIndex: data.chunkIndex,
          chunkSize: data.chunkSize,
          chunkContent: data.chunkContent,
          totalChunks: data.totalChunks,
          totalSize: data.totalSize,
        },
        timeout: 60 * 1000, // 60s
      }).then((res) => {
        const resData = res && res.data;
        if (resData && resData.code === 0) {
          callback && callback(resData.data);
        } else {
          callback && callback(resData.data);
        }
      }).catch(() => {
        callback && callback({ state: "failed" });
      });
    }
  }

  const doUpload = async (data, file) => {
    setLoading(true);
    doAdd(data, (res) => {
      const id = res.id;
      const totalChunks = Math.ceil(data.totalSize / CHUNK_SIZE);
      for (let i = 0; i < totalChunks; i++) {
        const chunkIndex = i;
        const start = i * CHUNK_SIZE;
        const end = Math.min(data.totalSize, start + CHUNK_SIZE);
        const chunk = file.slice(start, end);
        const reader = new FileReader();
        reader.onloadend = function () {
          let chunkContent = reader.result;
          if (chunkContent) {
            // 移除内容中开头的"data:*/*;base64,"这部分
            chunkContent = String(chunkContent).replace(/^data:.+;base64,/, '');
            doUploadChunk({
              id: id,
              chunkIndex,
              chunkContent,
              chunkSize: end - start,
              totalChunks,
              totalSize: data.totalSize,
            }, (data) => {
              if (data && data.state) {
                if (data.state === "finished") {
                  doUpdate({ id, packageId: data.packageId });
                }
              }
            }).catch(() => {
              Message.error(t['form.updated.fail']);
              setLoading(false);
            });
          }
        }
        reader.readAsDataURL(chunk);
      }
    });
  }

  const doUpdate = async (data) => {
    setLoading(true);
    api(`/api/v1/tasks`, {
      method: 'post',
      data: {
        id: data.id,
        name: "update",
        packageId: data.packageId,
        patchName: data.patchName,
      }
    }).then((res) => {
      const resData = res && res.data;
      if (!resData && !resData.data) {
        return;
      }
      if (resData.data.state === 'failed') {
        Message.error(t['form.updated.fail']);
      } else {
        Message.success(t['form.updated.success']);
        history.push({ pathname: '/main/store' });
      }
    }).catch(() => {
      Message.error(t['form.updated.fail']);
    }).finally(() => {
      setLoading(false);
    });
  }

  const fetchPatches = (id) => {
    api(`${API_CONTEXT}/packages/${id}/patches`, {
      serverUrl: serverUrl,
    }).then((res) => {
      const resData = res && res.data;
      if (resData && resData.code === 0) {
        console.log(resData.data);
        setPatches(resData.data);
      }
    });
  }

  const handleSubmit = async () => {
    try {
      await form.validate();
      const fields = form.getFields();
      const data = {
        name: null,
        version: null,
        description: null,
        size: null,
        location: null,
        timestamp: null,
        chunkIndex: null,
        chunkContent: null,
        chunkSize: null,
        totalChunks: null,
        totalSize: null,
      };
      if (fields.attachment && fields.attachment.length > 0) {
        const file = fields.attachment[0];
        const newFile = file.originFile;
        if (fields.name && fields.name.length > 0) {
          data.name = fields.name;
        } else {
          data.name = newFile.name.split('.')[0];
        }
        if (fields.version && fields.version.length > 0) {
          data.version = fields.version;
        } else {
          data.version = String(newFile.lastModified);
        }
        data.totalSize = newFile.size;
        Modal.confirm({
          title: t['form.confirm.title'],
          content: t['form.confirm.content'],
          onOk: () => {
            doUpload(data, newFile);
          },
        });
      } else if (fields.patch) {
        Modal.confirm({
          title: t['form.confirm.title'],
          content: t['form.confirm.content'],
          onOk: () => {
            doUpdate({
              packageId: fields.id,
              patchName: fields.patch 
            });
          },
        });
      } else {
        Message.error(t['form.validation.bothEmpty.error']);
      }
    } catch (_) {}
  };

  useEffect(() => {
    if (location.state) {
      setLoading(true);
      const filedsValue = location.state;
      form.setFieldsValue(filedsValue);
      setLoading(false);
      console.log(filedsValue);
      if (!isServer) {
        fetchPatches(filedsValue.id);
      }
    } else {
      history.push({ pathname: '/main/store' });
    }
  }, []);

  return (
    <div className={styles.container}>
      <Card>
        <Link to='/main/store'>
          <Button 
            loading={loading}
            type="primary" 
            icon={<IconArrowLeft />}>
              {t['form.operations.back']}
          </Button>
        </Link>
        <div className={styles.wrapper}>
          <Form form={form} className={styles.form}>
            <Form.Item noStyle>
              <Form.Item
                label={t['form.name.label']}
                disabled
                required
                field="name"
                rules={[
                  {
                    required: true,
                    message: t['form.name.required'],
                  },
                  {
                    validator: (value: string, callback) => {
                      // 允许字母、数字、-、_，长度不超过20
                      if (!/^[a-zA-Z0-9-_]{1,20}$/g.test(value)) {
                      // if (!/^[a-zA-Z0-9]{1,20}$/g.test(value)) {
                        callback(t['form.name.placeholder']);
                      }
                    },
                  },
                ]}
              >
                <Input
                  placeholder={t['form.name.placeholder']}
                />
              </Form.Item>
              <Form.Item
                label={t['form.attachment.label']}
                required={isServer}
                field="attachment"
                rules={[
                  {
                    required: isServer,
                    message: t['form.attachment.required'],
                  },
                ]}
              >
                <Upload
                  drag
                  accept='.zip'
                  autoUpload={false}
                  limit={1}
                  onDrop={(e) => {
                    const uploadFile = e.dataTransfer.files[0]
                    if (isAcceptFile(uploadFile, ['application/zip', '.zip'])) {
                      return;
                    } else {
                      Message.info(t['form.attachment.support-error.tips']);
                    }
                  }}
                  tip={t['form.attachment.support.tips']}
                />
              </Form.Item>
              {
                !isServer && (
                  <Form.Item
                    label={t['form.patch.label']}
                    field="patch"
                  >
                    <Select 
                      placeholder={t['form.patch.placeholder']}
                      allowClear
                    >
                      {
                        patches.map((item, index) => {
                          return (
                            <Select.Option key={index} value={item}>
                              {item}
                            </Select.Option>
                          )
                        })
                      }
                    </Select>
                  </Form.Item>
                )
              }
            </Form.Item>
            <Form.Item label=" ">
              <Space>
                <Button
                    loading={loading}
                    type="primary" size="large" onClick={handleSubmit}
                  >
                    {t['form.operations.submit']}
                  </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  );
}

export default UpdateForm;
