package domain

type Network struct {
	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Family   string `protobuf:"bytes,2,opt,name=family,proto3" json:"family,omitempty"`
	Address  string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	Mac      string `protobuf:"bytes,4,opt,name=mac,proto3" json:"mac,omitempty"`
	Internal bool   `protobuf:"varint,5,opt,name=internal,proto3" json:"internal,omitempty"`
}
