@echo off
chcp 65001 >nul 2>&1

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2", "", "runas", 1 >> "%temp%\getadmin.vbs"

    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

set C_TARGET=%~1
set C_HANDLE_EXE=%~2

if not exist "%C_TARGET%" (
    echo false
    exit /B 1
)

for /F "tokens=3 delims=: " %%A in ('%C_HANDLE_EXE% -accepteula %C_TARGET% ^| findstr /R /C:"pid: [0-9]*" ^| findstr /R /C:"type: File"') do (
    set PID=%%A
    taskkill /PID !PID! /F
)
@REM 判断是文件还是文件夹
if exist "%C_TARGET%\*" (
    rd /S /Q "%C_TARGET%"
) else (
    del /F /Q "%C_TARGET%"
)
echo true