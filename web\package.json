{"name": "ccms", "version": "1.1.0", "description": "ccms", "scripts": {"start": "vite", "dev": "vite", "preview": "vite preview", "build": "vite build", "eslint": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix --cache", "stylelint": "stylelint 'src/**/*.less' 'src/**/*.css' --fix --cache"}, "dependencies": {"@arco-design/color": "^0.4.0", "@arco-design/web-react": "^2.63.0", "@arco-plugins/vite-react": "^1.3.3", "@arco-themes/react-arco-pro": "^0.0.7", "@loadable/component": "^5.16.4", "@turf/turf": "^7.0.0", "arco-design-pro": "^2.8.1", "axios": "^1.7.2", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "paho-mqtt": "^1.1.0", "query-string": "^9.0.0", "react": "^17.0.2", "react-color": "^2.18.1", "react-dom": "^17.0.2", "react-redux": "^7.2.6", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "redux": "^5.0.1"}, "devDependencies": {"@arco-design/webpack-plugin": "^1.7.0", "@arco-plugins/vite-plugin-svgr": "^0.7.2", "@svgr/webpack": "^5.5.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-react": "^1.1.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "less": "^4.2.0", "less-loader": "12.2.0", "postcss-less": "6", "prettier": "^3.3.2", "pretty-quick": "^4.0.0", "stylelint": "^16.6.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^36.0.0", "typescript": "^5.5.2", "vite": "^2.6.14"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --cache"], "*.{css, less}": ["stylelint --fix"]}}