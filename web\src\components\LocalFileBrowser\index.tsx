import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Message,
  Breadcrumb,
  Typography,
  Spin,
  Input,
  Card
} from '@arco-design/web-react';
import {
  IconFolder,
  IconFile,
  IconArrowUp,
  IconDesktop,
  IconRefresh,
  IconSearch
} from '@arco-design/web-react/icon';
import api from '@/utils/api';
import styles from './style/index.module.less';

const { Text } = Typography;

interface FileInfo {
  name: string;
  path: string;
  is_dir: boolean;
  size: number;
  mod_time: string;
  readable: boolean;
}

interface DriveInfo {
  letter: string;
  path: string;
  label: string;
  drive_type: string;
  available: boolean;
}

interface LocalFileBrowserProps {
  visible: boolean;
  onCancel: () => void;
  onSelect: (filePath: string, fileName: string) => void;
  title?: string;
}

const LocalFileBrowser: React.FC<LocalFileBrowserProps> = ({
  visible,
  onCancel,
  onSelect,
  title = '选择本地文件'
}) => {
  const [loading, setLoading] = useState(false);
  const [currentPath, setCurrentPath] = useState('');
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [drives, setDrives] = useState<DriveInfo[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showDrives, setShowDrives] = useState(true);

  // 获取系统驱动器列表
  const fetchDrives = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/v1/system-drives');
      if (response.data.code === 0) {
        setDrives(response.data.drives || []);
      } else {
        Message.error('获取驱动器列表失败');
      }
    } catch (error) {
      console.error('获取驱动器失败:', error);
      Message.error('获取驱动器列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 浏览指定路径的文件
  const browseFiles = async (path: string) => {
    try {
      setLoading(true);
      const response = await api.get('/api/v1/browse-files', {
        params: { path }
      });
      if (response.data.code === 0) {
        setFiles(response.data.files || []);
        setCurrentPath(response.data.current_path || path);
        setShowDrives(false);
      } else {
        Message.error('浏览文件失败');
      }
    } catch (error) {
      console.error('浏览文件失败:', error);
      Message.error('浏览文件失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取驱动器列表
  useEffect(() => {
    if (visible) {
      fetchDrives();
      setCurrentPath('');
      setFiles([]);
      setSelectedFile(null);
      setSearchKeyword('');
      setShowDrives(true);
    }
  }, [visible]);

  // 处理驱动器选择
  const handleDriveSelect = (drive: DriveInfo) => {
    browseFiles(drive.path);
  };

  // 处理文件/文件夹双击
  const handleFileDoubleClick = (file: FileInfo) => {
    if (file.is_dir) {
      browseFiles(file.path);
    } else {
      setSelectedFile(file);
    }
  };

  // 处理文件选择
  const handleFileSelect = (file: FileInfo) => {
    if (!file.is_dir) {
      setSelectedFile(file);
    }
  };

  // 返回上级目录
  const goToParent = () => {
    if (currentPath) {
      const parentPath = currentPath.split(/[\\/]/).slice(0, -1).join('\\');
      if (parentPath.length <= 3) { // 到达根目录
        setShowDrives(true);
        setFiles([]);
        setCurrentPath('');
      } else {
        browseFiles(parentPath);
      }
    }
  };

  // 刷新当前目录
  const refreshCurrent = () => {
    if (showDrives) {
      fetchDrives();
    } else {
      browseFiles(currentPath);
    }
  };

  // 确认选择文件
  const handleConfirm = () => {
    if (selectedFile && !selectedFile.is_dir) {
      onSelect(selectedFile.path, selectedFile.name);
    } else {
      Message.warning('请选择一个文件');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 过滤文件
  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 驱动器表格列定义
  const driveColumns = [
    {
      title: '驱动器',
      dataIndex: 'letter',
      render: (letter: string, record: DriveInfo) => (
        <Space>
          <IconDesktop />
          <Text>{record.label}</Text>
        </Space>
      )
    },
    {
      title: '路径',
      dataIndex: 'path'
    },
    {
      title: '类型',
      dataIndex: 'drive_type'
    }
  ];

  // 文件表格列定义
  const fileColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      render: (name: string, record: FileInfo) => (
        <Space>
          {record.is_dir ? <IconFolder /> : <IconFile />}
          <Text>{name}</Text>
        </Space>
      )
    },
    {
      title: '大小',
      dataIndex: 'size',
      render: (size: number, record: FileInfo) => 
        record.is_dir ? '-' : formatFileSize(size)
    },
    {
      title: '修改时间',
      dataIndex: 'mod_time'
    }
  ];

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      okText="选择"
      cancelText="取消"
      width={800}
      okButtonProps={{ disabled: !selectedFile || selectedFile.is_dir }}
    >
      <div className={styles.container}>
        {/* 工具栏 */}
        <Card className={styles.toolbar}>
          <Space>
            <Button 
              icon={<IconArrowUp />} 
              onClick={goToParent}
              disabled={showDrives}
            >
              上级目录
            </Button>
            <Button 
              icon={<IconRefresh />} 
              onClick={refreshCurrent}
            >
              刷新
            </Button>
            <Button 
              icon={<IconDesktop />} 
              onClick={() => {
                setShowDrives(true);
                setFiles([]);
                setCurrentPath('');
              }}
            >
              驱动器
            </Button>
          </Space>
          
          <Input
            placeholder="搜索文件..."
            prefix={<IconSearch />}
            value={searchKeyword}
            onChange={setSearchKeyword}
            style={{ width: 200, marginLeft: 16 }}
          />
        </Card>

        {/* 路径导航 */}
        {!showDrives && currentPath && (
          <Card className={styles.breadcrumb}>
            <Breadcrumb>
              <Breadcrumb.Item>当前路径</Breadcrumb.Item>
              <Breadcrumb.Item>{currentPath}</Breadcrumb.Item>
            </Breadcrumb>
          </Card>
        )}

        {/* 文件列表 */}
        <Card className={styles.fileList}>
          <Spin loading={loading}>
            {showDrives ? (
              <Table
                columns={driveColumns}
                data={drives}
                pagination={false}
                rowKey="path"
                onRow={(record) => ({
                  onClick: () => handleDriveSelect(record),
                  style: { cursor: 'pointer' }
                })}
              />
            ) : (
              <Table
                columns={fileColumns}
                data={filteredFiles}
                pagination={false}
                rowKey="path"
                rowSelection={{
                  type: 'radio',
                  selectedRowKeys: selectedFile ? [selectedFile.path] : [],
                  onChange: (selectedRowKeys, selectedRows) => {
                    if (selectedRows.length > 0) {
                      handleFileSelect(selectedRows[0]);
                    }
                  }
                }}
                onRow={(record) => ({
                  onDoubleClick: () => handleFileDoubleClick(record),
                  style: { cursor: 'pointer' }
                })}
              />
            )}
          </Spin>
        </Card>

        {/* 选中文件信息 */}
        {selectedFile && (
          <Card className={styles.selectedInfo}>
            <Text>已选择: {selectedFile.name}</Text>
            <br />
            <Text type="secondary">路径: {selectedFile.path}</Text>
            <br />
            <Text type="secondary">大小: {formatFileSize(selectedFile.size)}</Text>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default LocalFileBrowser;