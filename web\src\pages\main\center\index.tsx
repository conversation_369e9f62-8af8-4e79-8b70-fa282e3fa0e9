import Paho from 'paho-mqtt';
import React, { useEffect, useState } from 'react';

import { Card, Empty, Grid, Message, Spin } from '@arco-design/web-react';

import { useMQTT } from '@/context';

import api from '@/utils/api';
import consts from '@/const';
import useLocale from '@/utils/useLocale';
import useStorage from '@/utils/useStorage';

import styles from './style/index.module.less';
import CardBlock from './card-block';
import locale from './locale';

const { Row, Col } = Grid;

export default function Center() {
  const t = useLocale(locale);
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [ parentHost ] = useStorage(consts.KEY_AIS_SERVER_PARENT_HOST);
  const { id, mqtt } = useMQTT();

  const controlClient = (action) => {
    if (action && action.to) {
      try {
        action.from = id;
        const msg = new Paho.Message(JSON.stringify(action))
        msg.destinationName = consts.TOPIC_X_CLIENT_CMD.replace('*', action.to);
        msg.qos = 2;
        mqtt.publish(msg);
      } catch (error) {
        console.error(error);
        Message.error(t['center.controlClient.error'] + ": " + error);
      }
    } else {
      Message.error(t['center.controlClient.error'] + ": " + t['center.controlClient.error.noTarget']);
    }
  };

  const fetchData = () => {
    setLoading(true);
    const isLocal = parentHost === '' || parentHost === 'localhost' || parentHost === '127.0.0.1';
    const params = {}
    if (!isLocal) {
      params['isLocal'] = 1;
    }
    api('/api/v1/clients', { data: params }).then((res) => {
      const resData = res && res.data;
      if ((!resData && !resData.data) || resData.data.length === 0) {
        return;
      }
      setData(resData.data);
    }).finally(() => setLoading(false));
  };
  useEffect(() => {
    if (parentHost == null || parentHost === '') {
      return;
    }
    fetchData();
    // mqtt.onHeartbeat = () => {
    //   fetchData();
    // }
    return () => {
      // mqtt.onHeartbeat = null;
    };
  }, []);

  return (
    <Card>
      <Row gutter={24} className={styles['card-content']}>
        {
          loading ? (
            <div style={{ width: '100%', textAlign: 'center' }}>
              <Spin dot size={38} />
            </div>
          ) : (data == null || data.length === 0) ? (
            <Empty />
          ) : data.map((item, index) => (
            <Col xs={24} sm={12} md={8} lg={8} xl={8} xxl={8} key={index}>
              <CardBlock card={item} loading={loading} control={controlClient} />
            </Col>
          ))
        }
      </Row>
    </Card>
  );
}
