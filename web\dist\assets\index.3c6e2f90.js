import{u as a,r as e,b as t,j as s,i as o,k as n,l as i}from"./index.e8bac691.js";import{C as r}from"./index.a97db34b.js";import{T as c}from"./index.acd49a0a.js";import{i as d,S as l}from"./search-form.a79a6439.js";import{getColumns as m}from"./constants.03964fc5.js";import"./index.dde5f5a0.js";function p(){const p=a(d),f=async(a,e)=>{if("delete"===e)n.confirm({title:p["patch.tables.main.modal.delete.title"],content:p["patch.tables.main.modal.delete.content"],onOk:()=>{g(!0),i("/api/v1/patches",{method:"delete",data:{name:a.name,packageName:a.packageName}}).then((()=>{o.success(p["patch.tables.main.modal.delete.operation.success"]),y()})).catch((()=>{o.error(p["patch.tables.main.modal.delete.operation.fail"])})).finally((()=>{g(!1)}))}});else o.warning(p["patch.tables.main.operation.no-support"])},u=e.exports.useMemo((()=>m(p,f)),[p]),[h,x]=e.exports.useState([]),[b,g]=e.exports.useState(!0),[j,S]=e.exports.useState({});function y(){g(!0),i("/api/v1/patches",{data:{...j}}).then((a=>{const e=a&&a.data;(e||e.data||e.data)&&x(e.data)})).finally((()=>{g(!1)}))}return e.exports.useEffect((()=>{y()}),[JSON.stringify(j)]),t(r,{children:[s(l,{loading:b,onSearch:function(a){S(a)}}),s(c,{rowKey:"id",loading:b,columns:u,data:h,pagination:!1})]})}export{p as default};
