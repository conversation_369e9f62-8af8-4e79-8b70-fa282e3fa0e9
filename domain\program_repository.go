package domain

import "fmt"

func (t *Program) GetById() error {
	err := FindOneByID(t.Id, t)
	if err != nil {
		return err
	}
	return nil
}

func (t *Program) GetByName() error {
	if t.Name == "" {
		return fmt.<PERSON>rrorf("name or version is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	return db.Where("name = ?", t.Name).First(&t).Error
}

func (t *Program) UpdateState(state string) error {
	if t.Id == 0 {
		return fmt.Errorf("id is empty")
	}
	t.State = state
	return Save(t)
}

func (t *Program) Count() (int64, error) {
	return Count(t)
}

func (t *Program) CountByName() (int64, error) {
	if t.Name == "" {
		return 0, fmt.Errorf("name is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return 0, err
	}
	var count int64
	query := db.Model(t).Where("name = ?", t.Name)
	err = query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (t *Program) List() ([]*Program, error) {
	var ts []*Program
	err := FindAll(&ts)
	if err != nil {
		return nil, err
	}
	return ts, nil
}

func (t *Program) ListByName() ([]*Program, error) {
	if t.Name == "" {
		return nil, fmt.Errorf("name is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return nil, err
	}
	var ts []*Program
	query := db.Where("name = ?", t.Name)
	err = query.Find(&ts).Error
	if err != nil {
		return nil, err
	}
	return ts, nil
}
