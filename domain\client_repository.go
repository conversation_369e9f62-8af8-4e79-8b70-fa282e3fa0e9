package domain

import (
	"fmt"
	"time"
)

func UpdateClientsStateToOnlineByIds(ids []string) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Model(&Client{}).
		Where("id IN (?)", ids).
		Update("state", ClientState.Online).Error
	if err != nil {
		return err
	}
	return nil
}

func UpdateClientsStateToOfflineByNotIn(ids []string) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Model(&Client{}).
		Where("is_local = 0").
		Where("id NOT IN (?)", ids).
		Update("state", ClientState.Offline).Error
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) Add() error {
	if c.CreatedAt == (time.Time{}) {
		c.CreatedAt = time.Now()
	}
	if c.UpdatedAt == (time.Time{}) {
		c.UpdatedAt = time.Now()
	}
	return Add(c)
}

func (c *Client) DeleteByID() error {
	return DeleteByID(c.Id, c)
}

func (c *Client) GetById() error {
	err := FindOneByID(c.Id, c)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) List() ([]*Client, error) {
	db, err := GetDBInstance()
	if err != nil {
		return nil, err
	}
	var clients []*Client
	if c.IsLocal == 1 {
		db = db.Where("is_local = ?", c.IsLocal)
	}
	err = db.Find(&clients).Order("updated_at DESC").Error
	if err != nil {
		return nil, err
	}
	return clients, nil
}

func (c *Client) Online() error {
	c.State = ClientState.Online
	return c.Update()
}

func (c *Client) Offline() error {
	c.State = ClientState.Offline
	return c.Update()
}

func (c *Client) Update() error {
	if c.Id == "" {
		return fmt.Errorf("id is required")
	}
	if c.UpdatedAt == (time.Time{}) {
		c.UpdatedAt = time.Now()
	}
	return c.Save()
}

func (c *Client) Save() error {
	return Save(c)
}
