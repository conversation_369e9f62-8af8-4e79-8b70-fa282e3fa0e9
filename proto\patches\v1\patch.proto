syntax = "proto3";

package patches.v1;

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service PatchService {
  rpc ListPatches(Patch) returns (ListPatchesResponse) {
    option (google.api.http) = {
      get: "/api/v1/patches"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List patches"
      description: "List patches on the server."
      tags: "Patches"
    };
  }

  rpc DeletePatch(Patch) returns (CommonResponse) {
    option (google.api.http) = {
      delete: "/api/v1/patches"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete patch"
      description: "Delete a patch from the server."
      tags: "Patches"
    };
  }
}

message CommonResponse {
  string message = 1;
  int32 code = 2;
}

message ListPatchesResponse {
  string message = 1;
  int32 code = 2;
  repeated Patch data = 3;
}

message Patch {
  int32 id = 1;
  string name = 2;
  string package_name = 3;
  string uri = 4;
}