import{r as e,c as t,b as i,j as r,d as a,C as n,a as s,w as l,R as o}from"./index.fe31dd41.js";import{I as c}from"./index.d78a069d.js";var d=e.exports.forwardRef((function(e,n){var s,l,o=e.style,d=e.className,m=e.prefixCls,p=e.index,u=void 0===p?1:p,f=e.current,v=void 0===f?1:f,h=e.status,y=e.title,x=e.description,g=e.icon,N=e.nextStepError,b=e.type,C=e.customDot,z=e.labelPlacement,S=e.disabled,w=e.onClick,P=e.onChange,j=e.direction,k=e.id,D=e.lineless;h?l=h:(v<u&&(l="wait"),v===u&&(l="process"),v>u&&(l="finish"));var E=t(m+"-item",m+"-item-"+l,((s={})[m+"-item-custom"]=!!g,s[m+"-item-next-error"]=N,s[m+"-item-disabled"]=S,s[m+"-item-active"]=u===v,s),d),O=function(e){if("dot"===b)return null;var t=u;return g?t=g:"finish"===e?t=r(c,{}):"error"===e&&(t=r(a,{})),r("div",{className:m+"-icon",children:t})}(l),R=r("div",{className:m+"-item-icon",children:O}),T=C?C(R,{index:u,status:l,title:y,description:x}):R;return i("div",{ref:n,className:E,style:o,onClick:function(e){S||(P&&v!==u&&P(u,k),w&&w(u,k,e))},children:[!D&&("vertical"===z||"vertical"===j)&&r("div",{className:m+"-item-tail"}),"arrow"!==b&&T,i("div",{className:m+"-item-content",children:[r("div",{className:m+"-item-title",children:y}),x&&r("div",{className:m+"-item-description",children:x})]})]})}));d.displayName="Step";var m=d,p=globalThis&&globalThis.__assign||function(){return p=Object.assign||function(e){for(var t,i=1,r=arguments.length;i<r;i++)for(var a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},p.apply(this,arguments)},u={current:1,type:"default",size:"default",direction:"horizontal",labelPlacement:"horizontal"};var f=e.exports.forwardRef((function(i,a){var c,d=e.exports.useContext(n),m=d.getPrefixCls,f=d.componentConfig,v=d.rtl,h=s(i,u,null==f?void 0:f.Steps),y=h.className,x=h.style,g=h.children,N=h.current,b=void 0===N?1:N,C=h.status,z=h.onChange,S=h.type,w=h.size,P=h.direction,j=h.labelPlacement,k=h.customDot,D=h.lineless,E=m("steps"),O=j;"dot"===S&&(O="vertical"===P?"horizontal":"vertical"),"navigation"===S&&(O="horizontal");var R=P;"navigation"!==S&&"arrow"!==S||(R="horizontal");var T=t(E,E+"-"+R,E+"-label-"+O,E+"-size-"+w,((c={})[E+"-change-onclick"]="function"==typeof z,c[E+"-mode-"+S]="default"!==S,c[E+"-lineless"]=D,c[E+"-rtl"]=v,c),y);return r("div",{...p({ref:a,style:x,className:T},l(h)),children:o.Children.toArray(g).filter((function(e){return e&&e.type&&"Step"===e.type.displayName})).map((function(e,t){if(t+=1,e){var i=p({prefixCls:E,type:S,index:t,current:b,status:b===t?C:void 0,customDot:k,labelPlacement:O,direction:R,onChange:z,lineless:D},e.props);return"error"===C&&b===t+1&&(i.nextStepError=!0),o.cloneElement(e,i)}return null}))})}));f.displayName="Steps",f.Step=m;var v=f;export{v as S};
