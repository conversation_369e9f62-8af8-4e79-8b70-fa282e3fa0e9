export interface Service {
  id?: string;
  name?: string;
  version?: string;
  description?: string;
  type?: string;
  paths?: {
    root?: string;
    config?: string;
    log?: string;
  };
  commands?: {
    start?: string;
    stop?: string;
    restart?: string;
  };
  status?: string;
  upgrade?: {
    id?: string;
    name?: string;
    version?: string;
    description?: string;
    status?: string;
  };
}

export interface Client {
  id?: string;
  name?: string;
  grpcPort?: number;
  gatewayPort?: number;
  ssl ?: number;
  osType?: string;
  networks?: string;
  uptime?: string;
  version?: string;
  state?: string;
  isLocal?: number;
}

export interface Package {
  name?: string;
  version?: string;
  description?: string;
  type?: string;
  state?: string;
  installed?: number;
  upgradable?: number;
}