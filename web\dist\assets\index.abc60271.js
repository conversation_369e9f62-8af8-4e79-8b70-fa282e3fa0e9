import{u as t,r as e,o as r,p as a,q as s,j as o,S as n,E as l,G as c,P as i,i as d,l as f}from"./index.bbeb3af6.js";import{C as m}from"./index.cf9faf12.js";import{l as p,s as u,C as h}from"./card-block.271c3961.js";const{Row:x,Col:g}=c;function C(){const c=t(p),[C,E]=e.exports.useState(!0),[S,_]=e.exports.useState([]),[b]=r(a.KEY_AIS_SERVER_PARENT_HOST),{id:N,mqtt:T}=s(),j=t=>{if(t&&t.to)try{t.from=N;const e=new i.Message(JSON.stringify(t));e.destinationName=a.TOPIC_X_CLIENT_CMD.replace("*",t.to),e.qos=2,T.publish(e)}catch(e){console.error(e),d.error(c["center.controlClient.error"]+": "+e)}else d.error(c["center.controlClient.error"]+": "+c["center.controlClient.error.noTarget"])};return e.exports.useEffect((()=>{if(null!=b&&""!==b)return(()=>{E(!0);const t={};""===b||"localhost"===b||"127.0.0.1"===b||(t.isLocal=1),f("/api/v1/clients",{data:t}).then((t=>{const e=t&&t.data;(e||e.data)&&0!==e.data.length&&_(e.data)})).finally((()=>E(!1)))})(),()=>{}}),[]),o(m,{children:o(x,{gutter:24,className:u["card-content"],children:C?o("div",{style:{width:"100%",textAlign:"center"},children:o(n,{dot:!0,size:38})}):null==S||0===S.length?o(l,{}):S.map(((t,e)=>o(g,{xs:24,sm:12,md:8,lg:8,xl:8,xxl:8,children:o(h,{card:t,loading:C,control:j})},e)))})})}export{C as default};
