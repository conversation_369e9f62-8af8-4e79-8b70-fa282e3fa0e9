@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: Helper function for robust file copy with robocopy and logging
:: Usage: call :ROBOCOPY_FILE <SourceFilePath> <DestinationFilePath> <LogFile>
:ROBOCOPY_FILE
    set "SOURCE_FILE=%~1"
    set "DEST_FILE=%~2"
    set "LOG_OUTPUT_FILE=%~3"

    echo [INFO] Attempting to copy file: "%SOURCE_FILE%" to "%DEST_FILE%" >> "%LOG_OUTPUT_FILE%"
    echo [INFO] Attempting to copy file: "%SOURCE_FILE%" to "%DEST_FILE%"

    :: Extract destination directory and filename for robocopy
    for %%A in ("%DEST_FILE%") do set "DEST_DIR=%%~dpA"
    for %%A in ("%DEST_FILE%") do set "DEST_FILENAME=%%~nxA"

    :: Perform robocopy
    robocopy "%~dp1" "%DEST_DIR%" "%~nx1" /NFL /NDL /NJH /NJS /NC /NS /NP /LOG+:"%LOG_OUTPUT_FILE%" /R:3 /W:5
    set "ROBOCOPY_EXIT_CODE=!ERRORLEVEL!"

    :: Robocopy exit codes:
    :: 0 - No errors, no files copied.
    :: 1 - All files copied successfully.
    :: 2 - Some extra files or mismatched files were detected.
    :: 3 - Some files could not be copied (and retries exhausted).
    :: 4 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected.
    :: 5 - Some files could not be copied (and retries exhausted) and all files copied successfully.
    :: 6 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully.
    :: 7 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully and some extra files or mismatched files were detected.
    :: 8 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully and some extra files or mismatched files were detected and some files could not be copied (and retries exhausted).

    if !ROBOCOPY_EXIT_CODE! LEQ 8 (
        echo [SUCCESS] File copy completed with robocopy exit code: !ROBOCOPY_EXIT_CODE! >> "%LOG_OUTPUT_FILE%"
        echo [SUCCESS] File copy completed with robocopy exit code: !ROBOCOPY_EXIT_CODE!
        exit /b 0
    ) else (
        echo [ERROR] File copy failed with robocopy exit code: !ROBOCOPY_EXIT_CODE! >> "%LOG_OUTPUT_FILE%"
        echo [ERROR] File copy failed with robocopy exit code: !ROBOCOPY_EXIT_CODE!
        exit /b 1
    )
goto :EOF

:: Set color and title
title AIS Server Upgrader
color 0B

:: Initialize variables
set "UPGRADE_SUCCESS=0"
set "START_TIME=%TIME%"
set "LOG_FILE=%~dp0upgrade.log"

:: Create log file
echo ================================================ > "%LOG_FILE%"
echo AIS Server Upgrade Log >> "%LOG_FILE%"
echo Start time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

echo.
echo ================================================
echo           AIS Server Upgrader
echo ================================================
echo Start time: %DATE% %TIME%
echo.

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
echo [INFO] Checking administrator privileges... >> "%LOG_FILE%"
echo [INFO] Checking administrator privileges...
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    echo [WARNING] Administrator privileges required, requesting elevation... >> "%LOG_FILE%"
    echo [WARNING] Administrator privileges required, requesting elevation...
    goto UACPrompt
) else ( 
    echo [SUCCESS] Administrator privileges obtained >> "%LOG_FILE%"
    echo [SUCCESS] Administrator privileges obtained
    goto gotAdmin 
)

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2 %~3 %~4 %~5 %~6 %~7", "", "runas", 1 >> "%temp%\getadmin.vbs"

    if exist "%temp%\getadmin.vbs" ( "%temp%\getadmin.vbs" )
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

:: Parse command line parameters
echo [INFO] Parsing upgrade parameters... >> "%LOG_FILE%"
echo [INFO] Parsing upgrade parameters...

set "C_NAME=%~1"
if "%C_NAME%"=="" (
    set "C_NAME=ais-server"
)
set "C_VERSION=%~2"
if "%C_VERSION%"=="" (
    set "C_VERSION=1.0.0"
)
set "C_OS_TYPE=%~3"
if "%C_OS_TYPE%"=="" (
    set "C_OS_TYPE=windows"
)
set "C_OS_ARCH=%~4"
if "%C_OS_ARCH%"=="" (
    set "C_OS_ARCH=amd64"
)
set "C_PKG_DIR=%~5"
if "%C_PKG_DIR%"=="" (
    set "C_PKG_DIR=%~dp0"
)

set "C_PROGRAM_EXE=%C_PKG_DIR%\%C_NAME%.exe"

echo [PARAM] Service name: %C_NAME% >> "%LOG_FILE%"
echo [PARAM] Version: %C_VERSION% >> "%LOG_FILE%"
echo [PARAM] Operating system: %C_OS_TYPE% >> "%LOG_FILE%"
echo [PARAM] Architecture: %C_OS_ARCH% >> "%LOG_FILE%"
echo [PARAM] Program directory: %C_PKG_DIR% >> "%LOG_FILE%"
echo [PARAM] New program file: %C_PROGRAM_EXE% >> "%LOG_FILE%"

echo [PARAM] Service name: %C_NAME%
echo [PARAM] Version: %C_VERSION%
echo [PARAM] Program directory: %C_PKG_DIR%
echo.

:: Check if new program file exists
echo [INFO] Checking new program file... >> "%LOG_FILE%"
echo [INFO] Checking new program file: %C_PROGRAM_EXE%
if not exist "%C_PROGRAM_EXE%" (
    echo [ERROR] New program file not found: %C_PROGRAM_EXE% >> "%LOG_FILE%"
    echo [ERROR] New program file not found: %C_PROGRAM_EXE%
    echo.
    goto :upgrade_failed
)
echo [SUCCESS] New program file check passed >> "%LOG_FILE%"
echo [SUCCESS] New program file check passed
echo.

:: Get service binary path
echo [INFO] Querying service installation path... >> "%LOG_FILE%"
echo [INFO] Querying service installation path...
for /f "tokens=2,* delims=:" %%i in ('sc qc "%C_NAME%" ^| findstr /i "BINARY_PATH_NAME"') do (
    set "C_TAR_PROGRAM_EXE=%%i:%%j"
)
:: Remove leading spaces from path
set "C_TAR_PROGRAM_EXE=!C_TAR_PROGRAM_EXE:~1!"
echo [INFO] Target program path: !C_TAR_PROGRAM_EXE! >> "%LOG_FILE%"
echo [INFO] Target program path: !C_TAR_PROGRAM_EXE!

if not exist "!C_TAR_PROGRAM_EXE!" (
    echo [ERROR] Target program file not found: !C_TAR_PROGRAM_EXE! >> "%LOG_FILE%"
    echo [ERROR] Target program file not found: !C_TAR_PROGRAM_EXE!
    echo.
    goto :upgrade_failed
)
echo [SUCCESS] Target program file check passed >> "%LOG_FILE%"
echo [SUCCESS] Target program file check passed
echo.

:: Stop service
echo [INFO] Stopping service... >> "%LOG_FILE%"
echo [INFO] Stopping service...
echo Executing command: "!C_TAR_PROGRAM_EXE!" stop
"!C_TAR_PROGRAM_EXE!" stop
set "STOP_RESULT=%ERRORLEVEL%"
echo [RESULT] Stop command return code: %STOP_RESULT% >> "%LOG_FILE%"

if %STOP_RESULT% NEQ 0 (
    echo [WARNING] Service stop failed, return code: %STOP_RESULT% >> "%LOG_FILE%"
    echo [WARNING] Service stop failed, return code: %STOP_RESULT%
    echo [WARNING] Service may already be stopped, continuing upgrade...
) else (
    echo [SUCCESS] Service stop completed >> "%LOG_FILE%"
    echo [SUCCESS] Service stop completed
)
echo.

:: Backup original program file
echo [INFO] Backing up original program file... >> "%LOG_FILE%"
echo [INFO] Backing up original program file...
set "BACKUP_FILE=!C_TAR_PROGRAM_EXE!.backup.%DATE:~0,4%%DATE:~5,2%%DATE:~8,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
set "BACKUP_FILE=!BACKUP_FILE: =0!"
copy "!C_TAR_PROGRAM_EXE!" "!BACKUP_FILE!" >nul
if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Original program backed up to: !BACKUP_FILE! >> "%LOG_FILE%"
    echo [SUCCESS] Original program backed up to: !BACKUP_FILE!
) else (
    echo [WARNING] Backup failed, continuing upgrade... >> "%LOG_FILE%"
    echo [WARNING] Backup failed, continuing upgrade...
)
echo.

:: Copy new program file to overwrite old program file
echo [INFO] Updating program file... >> "%LOG_FILE%"
echo [INFO] Updating program file...
echo Copying: "%C_PROGRAM_EXE%" -> "!C_TAR_PROGRAM_EXE!"
call :ROBOCOPY_FILE "%C_PROGRAM_EXE%" "!C_TAR_PROGRAM_EXE!" "%LOG_FILE%"
set "COPY_RESULT=%ERRORLEVEL%"
echo [RESULT] Copy command return code: %COPY_RESULT% >> "%LOG_FILE%"

if %COPY_RESULT% NEQ 0 (
    echo [ERROR] Program file update failed, return code: %COPY_RESULT% >> "%LOG_FILE%"
    echo [ERROR] Program file update failed, return code: %COPY_RESULT%
    echo [INFO] Attempting to restore backup file...
    if exist "!BACKUP_FILE!" (
        copy "!BACKUP_FILE!" "!C_TAR_PROGRAM_EXE!" >nul
        echo [INFO] Original program file restored
    )
    goto :upgrade_failed
)
echo [SUCCESS] Program file update completed >> "%LOG_FILE%"
echo [SUCCESS] Program file update completed
echo.

:: Start service
echo [INFO] Starting service... >> "%LOG_FILE%"
echo [INFO] Starting service...
echo Executing command: "!C_TAR_PROGRAM_EXE!" start
"!C_TAR_PROGRAM_EXE!" start
set "START_RESULT=%ERRORLEVEL%"
echo [RESULT] Start command return code: %START_RESULT% >> "%LOG_FILE%"

if %START_RESULT% NEQ 0 (
    echo [ERROR] Service start failed, return code: %START_RESULT% >> "%LOG_FILE%"
    echo [ERROR] Service start failed, return code: %START_RESULT%
    echo [INFO] Attempting to restore backup file...
    if exist "!BACKUP_FILE!" (
        copy "!BACKUP_FILE!" "!C_TAR_PROGRAM_EXE!" >nul
        echo [INFO] Original program file restored, please start service manually
    )
    goto :upgrade_failed
)
echo [SUCCESS] Service start completed >> "%LOG_FILE%"
echo [SUCCESS] Service start completed
echo.

:: Update script files
echo [INFO] Updating script files... >> "%LOG_FILE%"
echo [INFO] Updating script files...
set "C_PROGRAM_BAT_INSTALL=%C_PKG_DIR%\windows.install.bat"
set "C_PROGRAM_BAT_UNINSTALL=%C_PKG_DIR%\windows.uninstall.bat"
set "C_PROGRAM_BAT_UPGRADE=%C_PKG_DIR%\windows.upgrade.bat"
:: Get target directory path from target program path
for %%i in ("!C_TAR_PROGRAM_EXE!") do set "C_TAR_DIR=%%~dpi"
echo [INFO] Target script directory: !C_TAR_DIR! >> "%LOG_FILE%"
echo [INFO] Target script directory: !C_TAR_DIR!

if exist "%C_PROGRAM_BAT_INSTALL%" (
    call :ROBOCOPY_FILE "%C_PROGRAM_BAT_INSTALL%" "!C_TAR_DIR!windows.install.bat" "%LOG_FILE%"
    if !ERRORLEVEL! EQU 0 (
        echo [SUCCESS] Updated installation script >> "%LOG_FILE%"
        echo [SUCCESS] Updated installation script
    ) else (
        echo [ERROR] Failed to update installation script >> "%LOG_FILE%"
        echo [ERROR] Failed to update installation script
    )
)
if exist "%C_PROGRAM_BAT_UNINSTALL%" (
    call :ROBOCOPY_FILE "%C_PROGRAM_BAT_UNINSTALL%" "!C_TAR_DIR!windows.uninstall.bat" "%LOG_FILE%"
    if !ERRORLEVEL! EQU 0 (
        echo [SUCCESS] Updated uninstallation script >> "%LOG_FILE%"
        echo [SUCCESS] Updated uninstallation script
    ) else (
        echo [ERROR] Failed to update uninstallation script >> "%LOG_FILE%"
        echo [ERROR] Failed to update uninstallation script
    )
)
if exist "%C_PROGRAM_BAT_UPGRADE%" (
    call :ROBOCOPY_FILE "%C_PROGRAM_BAT_UPGRADE%" "!C_TAR_DIR!windows.upgrade.bat" "%LOG_FILE%"
    if !ERRORLEVEL! EQU 0 (
        echo [SUCCESS] Updated upgrade script >> "%LOG_FILE%"
        echo [SUCCESS] Updated upgrade script
    ) else (
        echo [ERROR] Failed to update upgrade script >> "%LOG_FILE%"
        echo [ERROR] Failed to update upgrade script
    )
)
echo.

:: 升级完全成功
set "UPGRADE_SUCCESS=1"
goto :upgrade_success

:upgrade_failed
echo.
echo ================================================
echo [FAILED] AIS Server upgrade failed!
echo ================================================
echo Failure time: %DATE% %TIME% >> "%LOG_FILE%"
echo [FAILED] Error occurred during upgrade >> "%LOG_FILE%"
color 0C
goto :cleanup

:upgrade_success
echo.
echo ================================================
echo [SUCCESS] AIS Server upgrade completed!
echo ================================================
echo Service name: %C_NAME%
echo New version: %C_VERSION%
echo Program path: !C_TAR_PROGRAM_EXE!
echo.
echo Service successfully upgraded and restarted
if exist "!BACKUP_FILE!" (
    echo Original program backup: !BACKUP_FILE!
)
echo Completion time: %DATE% %TIME% >> "%LOG_FILE%"
echo [SUCCESS] Upgrade and restart fully completed >> "%LOG_FILE%"
color 0A
goto :cleanup

:cleanup
echo.
echo Log file: %LOG_FILE%
echo End time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"
echo.

if %UPGRADE_SUCCESS% EQU 1 (
    echo Upgrade successful! Window will close automatically in 5 seconds...
    timeout /t 5 /nobreak >nul
) else (
    echo Upgrade not fully successful, window will close automatically in 60 seconds...
    echo Please check the log file for detailed information: %LOG_FILE%
    timeout /t 60 /nobreak >nul
)
exit %UPGRADE_SUCCESS%