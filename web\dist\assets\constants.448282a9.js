import{r as e,al as t,j as n,am as r,c as l,C as o,a as s,b as a,y as i,m as c,s as u,B as d,T as p}from"./index.bbeb3af6.js";var m=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,l,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(a){l={error:a}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(l)throw l.error}}return s};function y(o){var s,a=o.prefixCls,i=o.maxCount,c=o.count,u=o.className,d=o.style,p=m(e.exports.useState(!1),2),y=p[0],x=p[1],f=c!==t(c);return n(r,{classNames:"badge-zoom",in:c>0,timeout:300,appear:!0,mountOnEnter:!0,unmountOnExit:!0,onEntered:function(){x(!0)},children:n("span",{className:u,style:d,children:n("span",{className:l((s={},s[a+"-number-text"]=y&&f,s)),children:i&&c>i?i+"+":c},c)})})}var x=globalThis&&globalThis.__assign||function(){return x=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var l in t=arguments[n])Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l]);return e},x.apply(this,arguments)},f=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(r=Object.getOwnPropertySymbols(e);l<r.length;l++)t.indexOf(r[l])<0&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]])}return n},h=globalThis&&globalThis.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,l,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(a){l={error:a}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(l)throw l.error}}return s},g=["red","orangered","orange","gold","lime","green","cyan","arcoblue","purple","pinkpurple","magenta","gray"],b={count:0,maxCount:99};var C=e.exports.forwardRef((function(t,c){var u,d=e.exports.useContext(o),p=d.getPrefixCls,m=d.componentConfig,C=d.rtl,v=s(t,b,null==m?void 0:m.Badge),N=v.count,O=v.text,k=v.className,z=v.dotClassName,w=v.dot,T=v.maxCount,I=v.color,S=v.dotStyle,j=v.offset,_=v.style,E=v.status,P=v.children,B=f(v,["count","text","className","dotClassName","dot","maxCount","color","dotStyle","offset","style","status","children"]),A=p("badge"),R=x({},S||{}),$=h(j||[],2),L=$[0],q=$[1];return L&&(R.marginRight=-L),q&&(R.marginTop=q),a("span",{...x({className:l(A,(u={},u[A+"-status"]=E,u[A+"-no-children"]=!P,u[A+"-rtl"]=C,u),k),ref:c,style:_},B),children:[P,function(){var e,t;if(i(N))return n("span",{className:l(A+"-custom-dot",z),style:R,children:N});var o=!I||g.indexOf(I)>-1?{}:{backgroundColor:I};return!O||I||E?E||I&&"number"==typeof N&&N<=0?a("span",{className:A+"-status-wrapper",children:[n("span",{className:l(A+"-status-dot",(e={},e[A+"-status-"+E]=E,e[A+"-color-"+I]=I,e),z),style:x(x({},o),R)}),O&&n("span",{className:A+"-status-text",children:O})]}):(w||I)&&"number"==typeof N&&N>0?n(r,{classNames:"badge-zoom",in:w||!!I,timeout:200,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:n("span",{className:l(A+"-dot",(t={},t[A+"-color-"+I]=I,t),z),style:x(x({},o),R)})}):n(y,{prefixCls:A,className:l(A+"-number",z),style:x(x({},o),R),maxCount:T,count:N}):n("span",{className:l(A+"-text",z),style:R,children:O})}()]})}));C.displayName="Badge";var v=C;const{Text:N}=p,O=(e,t)=>e.support&&-1!==e.support.indexOf(t),k=e=>"downloading"===e.state,z=e=>1===e.downloaded,w=e=>"installing"===e.state,T=e=>1===e.installed,I=e=>"running"===e.state,S=e=>!k(e)&&!z(e),j=e=>!(k(e)||S(e)||w(e)||T(e)),_=e=>!k(e)&&!S(e)&&!w(e)&&!j(e)&&T(e),E=e=>_(e)&&O(e,"start")&&!I(e),P=e=>_(e)&&O(e,"stop")&&I(e),B=e=>!T(e)&&(!O(e,"stop")||O(e,"stop")&&!I(e)),A=e=>T(e)&&O(e,"backup")&&I(e),R=e=>T(e)&&O(e,"restore")&&(e=>1===e.backed)(e),$=e=>T(e)&&I(e),L=e=>z(e)&&!I(e),q=e=>T(e)&&I(e);function D(e,t){return[{title:e["store.columns.id"],dataIndex:"id",render:e=>n(N,{copyable:!0,children:e})},{title:e["store.columns.name"],dataIndex:"name"},{title:e["store.columns.version"],dataIndex:"version"},{title:e["store.columns.type"],dataIndex:"type",render:t=>e[`store.columns.types.${t}`]},{title:e["store.columns.description"],dataIndex:"description"},{title:e["store.columns.state"],dataIndex:"state",render:(t,r)=>{switch(r.state){case"running":return n(c,{color:"green",size:"small",children:e["store.columns.state.running"]});case"exited":return n(c,{color:"red",size:"small",children:e["store.columns.state.exited"]});default:return n(c,{color:"gray",size:"small",children:e[`store.columns.state.${r.state}`]||r.state})}}},{title:e["store.columns.installed"],dataIndex:"installed",render:t=>n(v,1===t?{status:"success",text:e["store.const.answer.yes"]}:{status:"error",text:e["store.const.answer.no"]})},{title:e["store.columns.updatedAt"],dataIndex:"updatedAt"},{title:e["store.columns.operation"],dataIndex:"operation",headerCellStyle:{paddingLeft:"15px"},render:(r,l)=>a(u,{children:[$(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"update"),children:e["store.columns.operation.update"]}):null,L(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"upgrade"),children:e["store.columns.operation.upgrade"]}):null,E(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"start"),children:e["store.columns.operation.start"]}):P(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"stop"),children:e["store.columns.operation.stop"]}):null,j(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"install"),children:e["store.columns.operation.install"]}):_(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"uninstall"),children:e["store.columns.operation.uninstall"]}):null,S(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"download"),children:e["store.columns.operation.download"]}):null,A(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"backup"),children:e["store.columns.operation.backup"]}):null,R(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"restore"),children:e["store.columns.operation.restore"]}):null,B(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"delete"),children:e["store.columns.operation.delete"]}):null,q(l)?n(d,{type:"text",size:"small",onClick:()=>t(l,"export"),children:e["store.columns.operation.export"]}):null]})}]}export{D as getColumns};
