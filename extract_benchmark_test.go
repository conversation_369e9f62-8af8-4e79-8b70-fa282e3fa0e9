package main

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/lib"
)

// BenchmarkExtractMethods 对比不同解压缩方法的性能
func BenchmarkExtractMethods(b *testing.B) {
	// 创建测试目录
	testDir := "./test_extract"
	defer os.RemoveAll(testDir)

	// 这里需要一个测试用的压缩文件
	// 在实际使用时，请替换为您的测试文件路径
	testFile := "./test_data/large_package.zip"
	
	// 检查测试文件是否存在
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		b.<PERSON>("测试文件不存在: %s", testFile)
		return
	}

	b.Run("StandardUnzip", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			outputDir := filepath.Join(testDir, fmt.Sprintf("standard_%d", i))
			os.MkdirAll(outputDir, 0755)
			
			start := time.Now()
			err := lib.Unzip(testFile, outputDir)
			duration := time.Since(start)
			
			if err != nil {
				b.<PERSON>("标准解压缩失败: %v", err)
			}
			
			b.Logf("标准解压缩耗时: %v", duration)
			os.RemoveAll(outputDir)
		}
	})

	b.Run("FastUnzip", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			outputDir := filepath.Join(testDir, fmt.Sprintf("fast_%d", i))
			os.MkdirAll(outputDir, 0755)
			
			start := time.Now()
			err := lib.UnzipFast(testFile, outputDir)
			duration := time.Since(start)
			
			if err != nil {
				b.Errorf("高性能解压缩失败: %v", err)
			}
			
			b.Logf("高性能解压缩耗时: %v", duration)
			os.RemoveAll(outputDir)
		}
	})

	b.Run("AutoUnzip", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			outputDir := filepath.Join(testDir, fmt.Sprintf("auto_%d", i))
			os.MkdirAll(outputDir, 0755)
			
			start := time.Now()
			err := lib.UnzipAuto(testFile, outputDir)
			duration := time.Since(start)
			
			if err != nil {
				b.Errorf("自动解压缩失败: %v", err)
			}
			
			b.Logf("自动解压缩耗时: %v", duration)
			os.RemoveAll(outputDir)
		}
	})
}

// TestExtractPerformance 测试解压缩性能
func TestExtractPerformance(t *testing.T) {
	testFile := "./test_data/large_package.zip"
	
	// 检查测试文件是否存在
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Skipf("测试文件不存在: %s，请创建测试数据", testFile)
		return
	}

	testDir := "./test_performance"
	defer os.RemoveAll(testDir)

	// 测试标准解压缩
	t.Run("StandardExtract", func(t *testing.T) {
		outputDir := filepath.Join(testDir, "standard")
		os.MkdirAll(outputDir, 0755)
		
		start := time.Now()
		err := lib.Unzip(testFile, outputDir)
		duration := time.Since(start)
		
		if err != nil {
			t.Errorf("标准解压缩失败: %v", err)
			return
		}
		
		t.Logf("标准解压缩完成，耗时: %v", duration)
	})

	// 测试高性能解压缩
	t.Run("FastExtract", func(t *testing.T) {
		outputDir := filepath.Join(testDir, "fast")
		os.MkdirAll(outputDir, 0755)
		
		start := time.Now()
		err := lib.UnzipFast(testFile, outputDir)
		duration := time.Since(start)
		
		if err != nil {
			t.Errorf("高性能解压缩失败: %v", err)
			return
		}
		
		t.Logf("高性能解压缩完成，耗时: %v", duration)
	})

	// 测试队列解压缩
	t.Run("QueueExtract", func(t *testing.T) {
		outputDir := filepath.Join(testDir, "queue")
		os.MkdirAll(outputDir, 0755)
		
		files := []string{testFile}
		
		start := time.Now()
		err := lib.UnzipWithQueue(files, outputDir)
		duration := time.Since(start)
		
		if err != nil {
			t.Errorf("队列解压缩失败: %v", err)
			return
		}
		
		t.Logf("队列解压缩完成，耗时: %v", duration)
	})
}

// TestExtractFormats 测试不同格式的解压缩
func TestExtractFormats(t *testing.T) {
	testCases := []struct {
		name     string
		file     string
		expected bool
	}{
		{"ZIP文件", "./test_data/test.zip", true},
		{"7Z文件", "./test_data/test.7z", true},
		{"RAR文件", "./test_data/test.rar", true},
		{"TAR文件", "./test_data/test.tar", true},
		{"GZ文件", "./test_data/test.tar.gz", true},
	}

	testDir := "./test_formats"
	defer os.RemoveAll(testDir)

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 检查测试文件是否存在
			if _, err := os.Stat(tc.file); os.IsNotExist(err) {
				t.Skipf("测试文件不存在: %s", tc.file)
				return
			}

			outputDir := filepath.Join(testDir, tc.name)
			os.MkdirAll(outputDir, 0755)
			
			start := time.Now()
			err := lib.UnzipFast(tc.file, outputDir)
			duration := time.Since(start)
			
			if tc.expected && err != nil {
				t.Errorf("%s 解压缩失败: %v", tc.name, err)
				return
			}
			
			if !tc.expected && err == nil {
				t.Errorf("%s 应该解压缩失败，但成功了", tc.name)
				return
			}
			
			if tc.expected {
				t.Logf("%s 解压缩成功，耗时: %v", tc.name, duration)
			}
		})
	}
}