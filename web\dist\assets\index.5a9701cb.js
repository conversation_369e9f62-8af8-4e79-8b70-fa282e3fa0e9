import{ap as o,u as l,r as a,b as e,j as t,s as r,B as c,aL as p,aK as s,af as m,Q as i,T as n,i as d,l as I}from"./index.6227d37e.js";import{C as h}from"./index.a7402227.js";import{P as f}from"./index.e34aed29.js";import{I as g,b as u}from"./index.c12dce75.js";import"./index.182aa2de.js";const y={"en-US":{"menu.main.store.localImport":"Local Import","localImport.title":"Local File Import","localImport.description":"Import application packages directly from local files, bypassing network upload for faster deployment.","localImport.form.packageName":"Package Name","localImport.form.packageName.placeholder":"Enter package name, e.g., ais-cell","localImport.form.localPath":"Local File Path","localImport.form.localPath.placeholder":"Enter the absolute path of the compressed file, e.g., D:\\Programs\\20250815-Vietnam-Foxconn\\ais-cell.zip","localImport.form.localPath.required":"Please enter the local file path","localImport.form.localPath.zipOnly":"Please enter a .zip format compressed file path","localImport.button.import":"Start Import","localImport.button.importing":"Importing...","localImport.button.reset":"Reset","localImport.button.back":"Back","localImport.success":"Local import successful","localImport.failed":"Local import failed","localImport.progress.title":"Import Progress","localImport.progress.importing":"Importing file...","localImport.progress.completed":"Import completed","localImport.progress.extracting":"Extracting files...","localImport.progress.copying":"Copying files...","localImport.progress.processing":"Processing package...","localImport.info.alert":"Local file import allows you to directly enter the absolute path of local compressed files for import, bypassing network upload. The system will automatically parse version information from the compressed package, suitable for large files or local development environments.","localImport.task.created":"Import task created successfully","localImport.task.failed":"Failed to create import task","localImport.monitor.title":"Local File Import Progress"},"zh-CN":{"menu.main.store.localImport":"本地导入","localImport.title":"本地文件导入","localImport.description":"直接从本地文件导入应用包，跳过网络上传过程，实现快速部署。","localImport.form.packageName":"包名称","localImport.form.packageName.placeholder":"请输入包名称，如：ais-cell","localImport.form.localPath":"本地文件路径","localImport.form.localPath.placeholder":"请输入压缩文件的绝对路径，如：D:\\最新程序\\20250815-越南富士康\\ais-cell.zip","localImport.form.localPath.required":"请输入本地文件路径","localImport.form.localPath.zipOnly":"请输入.zip格式的压缩文件路径","localImport.button.import":"开始导入","localImport.button.importing":"导入中...","localImport.button.reset":"重置","localImport.button.back":"返回","localImport.success":"本地导入成功","localImport.failed":"本地导入失败","localImport.progress.title":"导入进度","localImport.progress.importing":"正在导入文件...","localImport.progress.completed":"导入完成","localImport.progress.extracting":"正在解压文件...","localImport.progress.copying":"正在复制文件...","localImport.progress.processing":"正在处理包...","localImport.info.alert":"本地文件导入功能允许您直接输入本地压缩文件的绝对路径进行导入，跳过网络上传过程。系统将自动从压缩包中解析版本信息，适用于大文件或本地开发环境。","localImport.task.created":"导入任务创建成功","localImport.task.failed":"创建导入任务失败","localImport.monitor.title":"本地文件导入进度"}};var k="_container_5t9y8_1",b="_header_5t9y8_5",P="_content_5t9y8_11",x="_fileInfo_5t9y8_22";const{useForm:N}=m,{Title:_,Text:v}=n;function z(){const n=o(),z=l(y),[C,L]=a.exports.useState(!1),[w]=N(),[S,F]=a.exports.useState(null),[j,T]=a.exports.useState(!1),[q,E]=a.exports.useState(null);return e("div",{className:k,children:[e(h,{children:[t("div",{className:b,children:e(r,{children:[t(c,{type:"text",icon:t(g,{}),onClick:()=>{n.push("/main/store")},children:z["localImport.button.back"]}),t(_,{heading:4,children:z["localImport.title"]})]})}),t(p,{}),e("div",{className:P,children:[t(s,{type:"info",content:z["localImport.info.alert"],style:{marginBottom:24}}),e(m,{form:w,layout:"vertical",onSubmit:async o=>{if(S)try{L(!0);const l=await I("/api/v1/local-import-tasks",{method:"post",data:{package_name:o.packageName,package_version:"auto",local_path:S.path}});if(0!==l.data.code)throw new Error(l.data.message||"创建任务失败");{const o=l.data.data.id;E(o),T(!0)}}catch(l){console.error("导入失败:",l),d.error(l.message||"导入失败")}finally{L(!1)}else d.error("请选择要导入的文件")},disabled:C,children:[t(m.Item,{label:z["localImport.form.packageName"],field:"packageName",rules:[{required:!0,message:z["localImport.form.packageName.placeholder"]}],children:t(i,{placeholder:z["localImport.form.packageName.placeholder"]})}),t(m.Item,{label:z["localImport.form.localPath"],field:"localPath",rules:[{required:!0,message:z["localImport.form.localPath.required"]},{validator:(o,l)=>{o&&!o.toLowerCase().endsWith(".zip")&&l(z["localImport.form.localPath.zipOnly"])}}],children:t(i,{placeholder:z["localImport.form.localPath.placeholder"],onChange:o=>{F(o?{path:o,name:o.split("\\").pop()||o}:null)},disabled:C})}),S&&t(h,{className:x,children:e(r,{children:[t(u,{style:{color:"var(--color-primary-6)"}}),e("div",{children:[t(v,{strong:!0,children:S.name}),t("br",{}),t(v,{type:"secondary",style:{fontSize:12},children:S.path})]})]})}),t(m.Item,{children:e(r,{children:[t(c,{type:"primary",htmlType:"submit",loading:C,disabled:!S||C,children:z["localImport.button.import"]}),t(c,{onClick:()=>{w.resetFields(),F(null),T(!1),E(null)},disabled:C,children:z["localImport.button.reset"]})]})})]})]})]}),t(f,{visible:j,taskId:q,title:z["localImport.monitor.title"],onComplete:(o,l)=>{o?(d.success(z["localImport.success"]),setTimeout((()=>{n.push({pathname:"/main/store"})}),1e3)):d.error(l||z["localImport.failed"])},onCancel:()=>{T(!1),E(null)}})]})}export{z as default};
