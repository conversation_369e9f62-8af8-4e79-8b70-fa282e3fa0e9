import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Steps,
  Form,
  Input,
  Button,
  Space,
  Card,
  Result,
  Upload,
  Message,
  Switch,
  Modal,
  Skeleton,
  Select,
} from '@arco-design/web-react';
import { IconArrowLeft } from '@arco-design/web-react/icon';

import useLocale from '@/utils/useLocale';

import locale from './locale';
import styles from './style/index.module.less';
import api from '@/utils/api';
import useStorage from '@/utils/useStorage';

const CHUNK_SIZE = 10 * 1024 * 1024; // 分块大小，例如10MB

const PACKAGE_TYPES = [
  'sdk',
  'svc',
  'file'
];

const isAcceptFile = (file, accept) => {
  if (accept && file) {
    const accepts = Array.isArray(accept)
      ? accept
      : accept
          .split(',')
          .map((x) => x.trim())
          .filter((x) => x);
    const fileExtension = file.name.indexOf('.') > -1 ? file.name.split('.').pop() : '';
    return accepts.some((type) => {
      const text = type && type.toLowerCase();
      const fileType = (file.type || '').toLowerCase();
      if (text === fileType) {
        // 类似excel文件这种
        // 比如application/vnd.ms-excel和application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
        // 本身就带有.字符的，不能走下面的.zip等文件扩展名判断处理
        // 所以优先对比input的accept类型和文件对象的type值
        return true;
      }
      if (new RegExp('\/\*').test(text)) {
        // application/* 这种通配的形式处理
        const regExp = new RegExp('\/.*$')
        return fileType.replace(regExp, '') === text.replace(regExp, '');
      }
      if (new RegExp('\..*').test(text)) {
        // .zip、.jar、.war 等后缀名
        return text === `.${fileExtension && fileExtension.toLowerCase()}`;
      }
      return false;
    });
  }
  return !!file;
}

function StepForm() {
  const [params] = useStorage('store-step-form');
  const location = useLocation();
  const t = useLocale(locale);
  const [current, setCurrent] = useState(1);

  const [loading, setLoading] = useState(false);
  const [oldFileSize, setOldFileSize] = useState(0);
  const [oldFileTimestamp, setOldFileTimestamp] = useState(0);
  // const [upgradable, setUpgradable] = useState(false);
  const [edit, setEdit] = useState(false);
  const [form] = Form.useForm();

  const reCreateForm = () => {
    form.resetFields();
    setEdit(false);
    setCurrent(1);
  };

  const doAdd = async (data, callback) => {
    api("/api/v1/packages", {
      method: 'post',
      data: {
        name: data.name,
        version: data.version,
        description: data.description,
        type: data.type,
        size: data.size
      }
    }).then((res) => {
      const resData = res && res.data;
      if (resData && resData.code === 0) {
        callback && callback(resData.data);
      } else {
        Message.error(t['stepForm.created.fail']);
      }
    }).catch(() => {
      Message.error(t['stepForm.created.fail']);
    })
  }

  const doUploadChunk = async (data, callback) => {
    if (data.chunkContent != null) {
      api("/api/v1/packages/" + data.id + "/chunks", {
        method: 'post',
        data: {
          id: data.id,
          chunkIndex: data.chunkIndex,
          chunkContent: data.chunkContent,
          chunkSize: CHUNK_SIZE,
          totalChunks: data.totalChunks,
        },
        timeout: 60 * 1000, // 60s
      }).then((res) => {
        const resData = res && res.data;
        if (resData && resData.code === 0) {
          callback && callback({
            index: data.chunkIndex,
            uploaded: true,
          });
        } else {
          callback && callback({
            index: data.chunkIndex,
            uploaded: false,
          });
        }
      }).catch(() => {
        callback && callback({
          index: data.chunkIndex,
          uploaded: false,
        });
      });
    }
  }

  const doUpload = async (data, file) => {
    setLoading(true);
    doAdd(data, (res) => {
      const id = res.id;
      const uploadedChunk = [];
      const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
      for (let i = 0; i < totalChunks; i++) {
        const chunkIndex = i;
        const start = i * CHUNK_SIZE;
        const end = Math.min(file.size, start + CHUNK_SIZE);
        const chunk = file.slice(start, end);
        const reader = new FileReader();
        reader.onloadend = function () {
          let chunkContent = reader.result;
          if (chunkContent) {
            // 移除内容中开头的"data:*/*;base64,"这部分
            chunkContent = String(chunkContent).replace(/^data:.+;base64,/, '');
            doUploadChunk({
              id: id,
              chunkIndex,
              chunkContent,
              totalChunks,
            }, (res) => {
              uploadedChunk.push(res);
              if (uploadedChunk.length === totalChunks) {
                api("/api/v1/packages/" + id + "/chunks", {
                  method: 'post',
                  data: {
                    id: id,
                    totalChunks,
                    merged: 1
                  }
                }).then((res) => {
                  const resData = res && res.data;
                  if (resData && resData.code === 0) {
                    Message.success(t['stepForm.created.success']);
                    setCurrent(current + 1);
                  } else {
                    Message.error(t['stepForm.created.fail']);
                  }
                }).catch(() => {
                  Message.error(t['stepForm.created.fail']);
                }).finally(() => {
                  setLoading(false);
                });
              }
            });
          }
        }
        reader.readAsDataURL(chunk);
      }
    });
  }

  const toNext = async () => {
    try {
      await form.validate();
      if (current === 2) {
        const fields = form.getFields();
        const data = {
          name: fields.name,
          version: fields.version,
          description: fields.description,
          size: null,
          location: null,
          timestamp: null,
          chunkIndex: null,
          chunkContent: null,
          chunkSize: null,
          totalChunks: null,
        };
        // console.log('fields.attachment:', fields.attachment);
        // console.log(fields.attachment && fields.attachment.length > 0);
        if (fields.attachment && fields.attachment.length > 0) {
          const file = fields.attachment[0];
          const newFile = file.originFile;
          data.size = newFile.size;
          data.timestamp = newFile.lastModified;
          if (oldFileTimestamp === data.timestamp)
          {
            Message.error(t['stepForm.created.fail.already-published']);
            return;
          }
          const newFileSize = newFile.size;
          // 求差值比率
          const rate = Math.abs(newFileSize - oldFileSize) / oldFileSize;
          // console.log('newFileSize:', newFileSize, 'oldFileSize:', oldFileSize, 'rate:', rate);
          // 比较文件名和文件大小
          if (edit && rate > 0.1)
          {
            Modal.confirm({
              title: t['stepForm.publish.confirm.title'],
              content: t['stepForm.publish.confirm.content'],
              onOk: () => {
                doUpload(data, newFile);
              },
            });
          }
          else {
            doUpload(data, newFile);
          }
        }
      }
      else {
        setCurrent(current + 1);
      }
    } catch (_) {}
  };

  const loadingNode = (rows = 1) => {
    return (
      <Skeleton
        text={{
          rows,
          width: new Array(rows).fill('100%'),
        }}
        animation
      />
    );
  };

  useEffect(() => {
    if (location.state || params) {
      setLoading(true);
      setEdit(true);
      const filedsValue = location.state || JSON.parse(params);
      setOldFileSize(filedsValue.size);
      setOldFileTimestamp(filedsValue.timestamp);
      // setUpgradable(filedsValue.upgradable === 1 ? true : false);
      form.setFieldsValue(filedsValue);
      setLoading(false);
    }
  }, []);
  
  return (
    <div className={styles.container}>
      <Card>
        <Link to='/main/store'>
          <Button 
            loading={loading}
            type="primary" 
            icon={<IconArrowLeft />}>
              {t['stepForm.operations.back']}
          </Button>
        </Link>
        <div className={styles.wrapper}>
          <Steps current={current} lineless>
            <Steps.Step
              title={t['stepForm.title.basicInfo']}
              description={t['stepForm.desc.basicInfo']}
            />
            <Steps.Step
              title={t['stepForm.title.attachment']}
              description={t['stepForm.desc.attachment']}
            />
            <Steps.Step
              title={t['stepForm.title.created']}
              description={t['stepForm.desc.created']}
            />
          </Steps>
          <Form form={form} className={styles.form}>
            {current === 1 && (
              <Form.Item noStyle>
                <Form.Item
                  label={t['stepForm.basicInfo.name']}
                  disabled={edit}
                  required
                  field="name"
                  rules={[
                    {
                      required: true,
                      message: t['stepForm.basicInfo.name.required'],
                    },
                    {
                      validator: (value: string, callback) => {
                        // 允许字母、数字、-、_，长度不超过20
                        if (!/^[a-zA-Z0-9-_]{1,20}$/g.test(value)) {
                        // if (!/^[a-zA-Z0-9]{1,20}$/g.test(value)) {
                          callback(t['stepForm.basicInfo.name.placeholder']);
                        }
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={t['stepForm.basicInfo.name.placeholder']}
                  />
                </Form.Item>
                <Form.Item
                  label={t['stepForm.basicInfo.version']}
                  required
                  field="version"
                  rules={[
                    {
                      required: true,
                      message: t['stepForm.basicInfo.version.required'],
                    },
                    // {
                    //   validator: (value: string, callback) => {
                    //     if (!/^[0-9.]{1,20}$/g.test(value)) {
                    //       callback(t['stepForm.basicInfo.version.placeholder']);
                    //     }
                    //   },
                    // },
                  ]}
                >
                  <Input
                    placeholder={t['stepForm.basicInfo.version.placeholder']}
                  />
                </Form.Item>
                <Form.Item
                  label={t['stepForm.basicInfo.type']}
                  required
                  field="type"
                  rules={[
                    {
                      required: true,
                      message: t['stepForm.basicInfo.type.required'],
                    },
                  ]}
                >
                  <Select placeholder={t['stepForm.basicInfo.type.placeholder']}>
                    {PACKAGE_TYPES.map((type) => (
                      <Select.Option key={type} value={type}>
                        {t[`stepForm.basicInfo.types.${type}`]}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  label={t['stepForm.basicInfo.description']}
                  field="description"
                >
                  <Input.TextArea
                    placeholder={t['stepForm.basicInfo.description.placeholder']}
                  />
                </Form.Item>
                {/* <Form.Item
                  label={t['stepForm.basicInfo.upgradable']}
                  field="upgradable"
                >
                  {loading ? (
                    loadingNode()
                  ) : (
                    <Switch 
                      checkedText='ON' 
                      uncheckedText='OFF'
                      defaultChecked={upgradable}
                    />
                  )}
                </Form.Item>  */}
              </Form.Item>
            )}
            {current === 2 && (
              <Form.Item noStyle>
                <Form.Item
                  label={t['stepForm.attachment.label']}
                  required
                  field="attachment"
                  rules={[
                    {
                      required: true,
                      message: t['stepForm.attachment.required'],
                    },
                  ]}
                >
                  <Upload
                    drag
                    accept='.zip,.jar,.war'
                    autoUpload={false}
                    limit={1}
                    onDrop={(e) => {
                      const uploadFile = e.dataTransfer.files[0]
                      if (isAcceptFile(uploadFile, ['application/zip', '.zip'])) {
                        return;
                      } else {
                        Message.info(t['stepForm.attachment.support-error.tips']);
                      }
                    }}
                    tip={t['stepForm.attachment.support.tips']}
                  />
                </Form.Item>
              </Form.Item>
            )}
            {current !== 3 ? (
              <Form.Item label=" ">
                <Space>
                  {current === 2 && (
                    <Button
                      loading={loading}
                      size="large"
                      onClick={() => setCurrent(current - 1)}
                    >
                      {t['stepForm.prev']}
                    </Button>
                  )}
                  {current !== 3 && (
                    <Button
                      loading={loading}
                      type="primary" size="large" onClick={toNext}
                    >
                      {t['stepForm.next']}
                    </Button>
                  )}
                </Space>
              </Form.Item>
            ) : (
              <Form.Item noStyle>
                <Result
                  status="success"
                  title={t['stepForm.created.success.title']}
                  subTitle={t['stepForm.created.success.desc']}
                  // extra={[
                  //   <Button 
                  //     loading={loading}
                  //     key="again" type="primary" onClick={reCreateForm}
                  //   >
                  //     {t['stepForm.created.success.again']}
                  //   </Button>,
                  // ]}
                />
              </Form.Item>
            )}
          </Form>
        </div>
      </Card>
    </div>
  );
}

export default StepForm;
