import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import {
  Card,
  Form,
  Button,
  Upload,
  Message,
  Space,
  Modal,
  Input
} from '@arco-design/web-react';
import { 
  IconArrowLeft 
} from '@arco-design/web-react/icon';

import api from '@/utils/api';
import useLocale from '@/utils/useLocale';
import ProgressMonitor from '@/components/ProgressMonitor';

import locale from './locale';
import styles from './style/index.module.less';

const API_CONTEXT = '/api/v1';
const API_UPLOAD_PACKAGE_TASKS = API_CONTEXT + '/upload-package-tasks';
const CHUNK_SIZE = 100 * 1024 * 1024; // 分块大小，例如100MB

const { useForm } = Form;

function ImportForm() {
  const history = useHistory();
  const t = useLocale(locale);
  const [loading, setLoading] = useState(false);
  const [form] = useForm();
  const [showProgress, setShowProgress] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);

  const isAcceptFile = (file, accept) => {
    if (accept && file) {
      const accepts = Array.isArray(accept)
        ? accept
        : accept
            .split(',')
            .map((x) => x.trim())
            .filter((x) => x);
      const fileExtension = file.name.indexOf('.') > -1 ? file.name.split('.').pop() : '';
      return accepts.some((type) => {
        const text = type && type.toLowerCase();
        const fileType = (file.type || '').toLowerCase();
        if (text === fileType) {
          // 类似excel文件这种
          // 比如application/vnd.ms-excel和application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
          // 本身就带有.字符的，不能走下面的.zip等文件扩展名判断处理
          // 所以优先对比input的accept类型和文件对象的type值
          return true;
        }
        if (new RegExp('\/\*').test(text)) {
          // application/* 这种通配的形式处理
          const regExp = new RegExp('\/.*$')
          return fileType.replace(regExp, '') === text.replace(regExp, '');
        }
        if (new RegExp('\..*').test(text)) {
          // .zip 后缀名
          return text === `.${fileExtension && fileExtension.toLowerCase()}`;
        }
        return false;
      });
    }
    return !!file;
  }

  const readFile = (file: File): Promise<string | ArrayBuffer | null> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (reader.error) {
          reject(reader.error);
        } else {
          // 移除内容中开头的"data:*/*;base64,"这部分
          resolve(String(reader.result).replace(/^data:.+;base64,/, ''));
        }
      };
      reader.readAsDataURL(file); // 或者使用其他读取方法，如 readAsDataURL, readAsArrayBuffer 等
    });
  };

  const doAdd = async (data, callback) => {
    api(API_UPLOAD_PACKAGE_TASKS, {
      method: 'post',
      data: {
        packageName: data.name,
        packageVersion: data.version,
      }
    }).then((res) => {
      const resData = res && res.data;
      if (resData && resData.code === 0) {
        callback && callback(resData.data);
      } else {
        Message.error(t['importForm.created.fail']);
      }
    }).catch(() => {
      Message.error(t['importForm.created.fail']);
    })
  }

  const doUploadChunk = (data) : Promise<any> => {
    return new Promise((resolve) => {
      if (data.chunkContent != null) {
        api(API_UPLOAD_PACKAGE_TASKS + "/" + data.id + "/chunks", {
          method: 'post',
          data: {
            id: data.id,
            chunkIndex: data.chunkIndex,
            chunkSize: data.chunkSize,
            chunkContent: data.chunkContent,
            totalChunks: data.totalChunks,
            totalSize: data.totalSize,
          },
          timeout: 3 * 60 * 1000, // 3 min
        }).then((res) => {
          const resData = res && res.data;
          resolve(resData.data);
        }).catch(() => {
          resolve({ state: "failed" });
        });
      }
    });
  }

  const doUpload = async (data, file) => {
    setLoading(true);
    doAdd(data, async (res) => {
      const id = res.id;
      setCurrentTaskId(id);
      setShowProgress(true);
      
      const totalChunks = Math.ceil(data.totalSize / CHUNK_SIZE);
      for (let i = 0; i < totalChunks; i++) {
        const start = i * CHUNK_SIZE;
        const end = Math.min(data.totalSize, start + CHUNK_SIZE);
        const chunk = file.slice(start, end);
        const chunkIndex = i;
        const chunkContent = await readFile(chunk);
        if (!chunkContent) {
          Message.error(t['importForm.created.fail']);
          setLoading(false);
          setShowProgress(false);
          return;
        }
        const resData = await doUploadChunk(
          {
            id: id,
            chunkIndex,
            chunkContent,
            chunkSize: end - start,
            totalChunks,
            totalSize: data.totalSize,
          }
        );
        if (resData && resData.state) {
          if (resData.state === "finished") {
            // 不立即关闭，让进度监控组件处理完成状态
            break;
          } else if (resData.state === "failed") {
            Message.error(t['importForm.created.fail']);
            setLoading(false);
            setShowProgress(false);
            break;
          }
        }
      }
      setLoading(false);
    });
  }
  
  // 处理进度监控完成
  const handleProgressComplete = (success: boolean, message?: string) => {
    if (success) {
      Message.success(t['importForm.created.success']);
      setTimeout(() => {
        history.push({ pathname: '/main/store' });
      }, 1000);
    } else {
      Message.error(message || t['importForm.created.fail']);
    }
  };
  
  // 关闭进度监控
  const handleProgressClose = () => {
    setShowProgress(false);
    setCurrentTaskId(null);
  };

  const handleSubmit = async () => {
    try {
      await form.validate();
      const fields = form.getFields();
      const data = {
        name: null,
        version: null,
        description: null,
        size: null,
        location: null,
        timestamp: null,
        chunkIndex: null,
        chunkContent: null,
        chunkSize: null,
        totalChunks: null,
        totalSize: null,
      };
      if (fields.attachment && fields.attachment.length > 0) {
        const file = fields.attachment[0];
        const newFile = file.originFile;
        if (fields.name && fields.name.length > 0) {
          data.name = fields.name;
        } else {
          data.name = newFile.name.split('.')[0];
        }
        data.version = String(newFile.lastModified);
        data.totalSize = newFile.size;
        Modal.confirm({
          title: t['importForm.publish.confirm.title'],
          content: t['importForm.publish.confirm.content'],
          onOk: () => {
            doUpload(data, newFile);
          },
        });
      }
    } catch (_) {}
  };

  return (
    <div className={styles.container}>
      <Card>
        <Link to='/main/store'>
          <Button 
            loading={loading}
            type="primary" 
            icon={<IconArrowLeft />}>
              {t['importForm.operations.back']}
          </Button>
        </Link>
        <div className={styles.wrapper}>
          <Form form={form} className={styles.form}>
            <Form.Item noStyle>
              <Form.Item
                  label={t['importForm.name.label']}
                  required
                  field="name"
                  rules={[
                    {
                      required: true,
                      message: t['importForm.name.required'],
                    },
                    {
                      validator: (value: string, callback) => {
                        // 允许字母、数字、-、_，长度不超过20
                        if (!/^[a-zA-Z0-9-_]{1,20}$/g.test(value)) {
                        // if (!/^[a-zA-Z0-9]{1,20}$/g.test(value)) {
                          callback(t['importForm.name.placeholder']);
                        }
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={t['importForm.name.placeholder']}
                  />
                </Form.Item>
              <Form.Item
                label={t['importForm.attachment.label']}
                required
                field="attachment"
                rules={[
                  {
                    required: true,
                    message: t['importForm.attachment.required'],
                  },
                ]}
              >
                <Upload
                  drag
                  accept='.zip'
                  autoUpload={false}
                  limit={1}
                  onDrop={(e) => {
                    const uploadFile = e.dataTransfer.files[0]
                    if (isAcceptFile(uploadFile, ['application/zip', '.zip'])) {
                      return;
                    } else {
                      Message.info(t['importForm.attachment.support-error.tips']);
                    }
                  }}
                  tip={t['importForm.attachment.support.tips']}
                />
              </Form.Item>
            </Form.Item>
            <Form.Item label=" ">
              <Space>
                <Button
                    loading={loading}
                    type="primary" size="large" onClick={handleSubmit}
                  >
                    {t['importForm.operations.submit']}
                  </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Card>
      
      {/* 进度监控组件 */}
      <ProgressMonitor
        visible={showProgress}
        taskId={currentTaskId}
        title="文件上传进度"
        onComplete={handleProgressComplete}
        onCancel={handleProgressClose}
        autoClose={false}
        showDetails={true}
      />
    </div>
  );
}

export default ImportForm;
