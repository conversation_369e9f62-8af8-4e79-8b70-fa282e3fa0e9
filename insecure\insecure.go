package insecure

import (
	"crypto/tls"
	"crypto/x509"
	"log"
)

const certPEM = `-----BEGIN CERTIFICATE-----
MIIDCjCCAfKgAwIBAgIQIj4BuOtQRWxvUA4CUaL+WjANBgkqhkiG9w0BAQsFADAS
MRAwDgYDVQQKEwdBY21lIENvMCAXDTE4MDIyMjEzNDA1NFoYDzIxMzIwMzIzMDU0
MDU0WjASMRAwDgYDVQQKEwdBY21lIENvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
MIIBCgKCAQEA0f+rxvg+P/YxJ9Rnj4qPypexre9OAwHfYIfDtBwPffSNhmaJa6Ir
JmPDAfrkGmAu8E+1EJMRge7R4js+y7lj/nxSTHQW4ixXWYNaHrXB8T2ty+dW2T+t
TWagtBkgdZqC+t3AloRtDJBIFKXcd6yHA9q9vj/KRtnafTPjDYD+m4obR5vhkFYm
5oJJoLkcuZ8hGr3MdzHFMIPOJ5Bm5YBY3z4TLqGnmDqhL3pqNHW0xHP7wGEJOTal
I/3OqRthAkLLMwUCHQcpLt1j2jTbavodUSr4ibNXTn5L1ynRGtozb2iE+4bZlRQZ
oR0Q32XxPQ+vkKtatgXS7E6yiq/vUc88hQIDAQABo1owWDAOBgNVHQ8BAf8EBAMC
AqQwEwYDVR0lBAwwCgYIKwYBBQUHAwEwDwYDVR0TAQH/BAUwAwEB/zAgBgNVHREE
GTAXgglsb2NhbGhvc3SHBAAAAACHBH8AAAEwDQYJKoZIhvcNAQELBQADggEBAJgo
hrLJDKN9VXh6EXYtaeMxRVEINt+swrXLoxNcNmRXZb5vX11yX9uHWCcIaOHZM4c6
+ZZe6gtdTGswrzl7vB5RJ5ZJEypj0MhAvH/PN0J9W0gXYbxzI839RQ2DqNXDjU7I
bEDlKBSSmFb0TjXTuXhHKyviLETAbf143Zb7M1i9L+U5fiPaq2Zt07NX6d2SYeMd
7udXyv/WhWfXKYj2Hoa8sKfcNr2e68IkbD6i1j9zXSbOMfvs1JZgryGqNIoGDOPz
+M3QhvvuiYJCSoOhDph0pNoVeH4NtaVwqPe7qMPnim11CGQSfjzxmZMFqsoJIsRe
lig/ubNJZbC6oA1X+t4=
-----END CERTIFICATE-----
`

const keyPEM = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

var (
	// Cert is a self signed certificate
	Cert tls.Certificate
	// CertPool contains the self signed certificate
	CertPool *x509.CertPool
)

func init() {
	var err error
	Cert, err = tls.X509KeyPair([]byte(certPEM), []byte(keyPEM))
	if err != nil {
		log.Fatalln("Failed to parse key pair:", err)
	}
	Cert.Leaf, err = x509.ParseCertificate(Cert.Certificate[0])
	if err != nil {
		log.Fatalln("Failed to parse certificate:", err)
	}

	CertPool = x509.NewCertPool()
	CertPool.AddCert(Cert.Leaf)
}
