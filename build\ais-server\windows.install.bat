@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: Set color and title
title AIS Server Installer
color 0A

:: Initialize variables
set "INSTALL_SUCCESS=0"
set "START_TIME=%TIME%"
set "LOG_FILE=%~dp0install.log"

:: Create log file
echo ================================================ > "%LOG_FILE%"
echo AIS Server Installation Log >> "%LOG_FILE%"
echo Start time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

echo.
echo ================================================
echo           AIS Server Installer
echo ================================================
echo Start time: %DATE% %TIME%
echo.

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
echo [INFO] Checking administrator privileges... >> "%LOG_FILE%"
echo [INFO] Checking administrator privileges...
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    echo [WARNING] Administrator privileges required, requesting elevation... >> "%LOG_FILE%"
    echo [WARNING] Administrator privileges required, requesting elevation...
    goto UACPrompt
) else ( 
    echo [SUCCESS] Administrator privileges obtained >> "%LOG_FILE%"
    echo [SUCCESS] Administrator privileges obtained
    goto gotAdmin 
)

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2 %~3 %~4 %~5 %~6 %~7", "", "runas", 1 >> "%temp%\getadmin.vbs"

    if exist "%temp%\getadmin.vbs" ( "%temp%\getadmin.vbs" )
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

:: Parse command line parameters
echo [INFO] Parsing installation parameters... >> "%LOG_FILE%"
echo [INFO] Parsing installation parameters...

set "C_NAME=%~1"
if "%C_NAME%"=="" (
    set "C_NAME=ais-server"
)
set "C_VERSION=%~2"
if "%C_VERSION%"=="" (
    set "C_VERSION=1.0.0"
)
set "C_OS_TYPE=%~3"
if "%C_OS_TYPE%"=="" (
    set "C_OS_TYPE=windows"
)
set "C_OS_ARCH=%~4"
if "%C_OS_ARCH%"=="" (
    set "C_OS_ARCH=amd64"
)
set "C_PKG_DIR=%~5"
if "%C_PKG_DIR%"=="" (
    set "C_PKG_DIR=%~dp0"
)

set "C_PROGRAM_EXE=%C_PKG_DIR%\%C_NAME%.exe"

echo [PARAM] Service name: %C_NAME% >> "%LOG_FILE%"
echo [PARAM] Version: %C_VERSION% >> "%LOG_FILE%"
echo [PARAM] Operating system: %C_OS_TYPE% >> "%LOG_FILE%"
echo [PARAM] Architecture: %C_OS_ARCH% >> "%LOG_FILE%"
echo [PARAM] Program directory: %C_PKG_DIR% >> "%LOG_FILE%"
echo [PARAM] Executable file: %C_PROGRAM_EXE% >> "%LOG_FILE%"

echo [PARAM] Service name: %C_NAME%
echo [PARAM] Version: %C_VERSION%
echo [PARAM] Program directory: %C_PKG_DIR%
echo.

:: Check if executable file exists
echo [INFO] Checking executable file... >> "%LOG_FILE%"
echo [INFO] Checking executable file: %C_PROGRAM_EXE%
if not exist "%C_PROGRAM_EXE%" (
    echo [ERROR] Executable file not found: %C_PROGRAM_EXE% >> "%LOG_FILE%"
    echo [ERROR] Executable file not found: %C_PROGRAM_EXE%
    echo.
    goto :install_failed
)
echo [SUCCESS] Executable file check passed >> "%LOG_FILE%"
echo [SUCCESS] Executable file check passed
echo.

:: Clean configuration file for cross-environment compatibility
echo [INFO] Cleaning configuration file for cross-environment compatibility... >> "%LOG_FILE%"
echo [INFO] Cleaning configuration file for cross-environment compatibility...
set "CONFIG_FILE=%C_PKG_DIR%\config.yml"
if exist "%CONFIG_FILE%" (
    echo [INFO] Found config file: %CONFIG_FILE% >> "%LOG_FILE%"
    echo [INFO] Removing environment-specific configurations...
    
    :: Create temporary file for cleaned config
    set "TEMP_CONFIG=%C_PKG_DIR%\config_temp.yml"
    
    :: Write basic configuration without environment-specific paths
    (
        echo grpc_port: 5555
        echo gateway_port: 5888
        echo mqtt_tcp_port: 5883
        echo mqtt_ws_port: 5884
        echo ssl: false
        echo log_level: INFO
    ) > "%TEMP_CONFIG%"
    
    :: Replace original config with cleaned version
    move "%TEMP_CONFIG%" "%CONFIG_FILE%" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo [SUCCESS] Configuration file cleaned successfully >> "%LOG_FILE%"
        echo [SUCCESS] Configuration file cleaned for cross-environment compatibility
    ) else (
        echo [WARNING] Failed to clean configuration file >> "%LOG_FILE%"
        echo [WARNING] Failed to clean configuration file, continuing with installation...
    )
) else (
    echo [INFO] No config file found, skipping cleanup >> "%LOG_FILE%"
    echo [INFO] No config file found, skipping cleanup
)
echo.

:: Execute service installation
echo [INFO] Starting service installation... >> "%LOG_FILE%"
echo [INFO] Starting service installation...
echo Executing command: "%C_PROGRAM_EXE%" install
"%C_PROGRAM_EXE%" install
set "INSTALL_RESULT=%ERRORLEVEL%"
echo [RESULT] Installation command return code: %INSTALL_RESULT% >> "%LOG_FILE%"

if %INSTALL_RESULT% NEQ 0 (
    echo [ERROR] Service installation failed, return code: %INSTALL_RESULT% >> "%LOG_FILE%"
    echo [ERROR] Service installation failed, return code: %INSTALL_RESULT%
    goto :install_failed
)
echo [SUCCESS] Service installation completed >> "%LOG_FILE%"
echo [SUCCESS] Service installation completed
echo.

:: Start service
echo [INFO] Starting service... >> "%LOG_FILE%"
echo [INFO] Starting service...
echo Executing command: "%C_PROGRAM_EXE%" start
"%C_PROGRAM_EXE%" start
set "START_RESULT=%ERRORLEVEL%"
echo [RESULT] Start command return code: %START_RESULT% >> "%LOG_FILE%"

if %START_RESULT% NEQ 0 (
    echo [WARNING] Service start failed, return code: %START_RESULT% >> "%LOG_FILE%"
    echo [WARNING] Service start failed, return code: %START_RESULT%
    echo [WARNING] Service installed but failed to start, please start manually
    goto :install_partial
)
echo [SUCCESS] Service start completed >> "%LOG_FILE%"
echo [SUCCESS] Service start completed
echo.

:: Installation fully successful
set "INSTALL_SUCCESS=1"
goto :install_success

:install_failed
echo.
echo ================================================
echo [FAILED] AIS Server installation failed!
echo ================================================
echo Failure time: %DATE% %TIME% >> "%LOG_FILE%"
echo [FAILED] Error occurred during installation >> "%LOG_FILE%"
color 0C
goto :cleanup

:install_partial
echo.
echo ================================================
echo [PARTIAL SUCCESS] AIS Server installation partially completed
echo Service installed but failed to start, please check configuration and start manually
echo ================================================
echo Partial success time: %DATE% %TIME% >> "%LOG_FILE%"
echo [PARTIAL SUCCESS] Service installation completed but start failed >> "%LOG_FILE%"
color 0E
goto :cleanup

:install_success
echo.
echo ================================================
echo [SUCCESS] AIS Server installation completed!
echo ================================================
echo Service name: %C_NAME%
echo Version: %C_VERSION%
echo Installation directory: %C_PKG_DIR%
echo.
echo Service successfully installed and started
echo Completion time: %DATE% %TIME% >> "%LOG_FILE%"
echo [SUCCESS] Installation and startup fully completed >> "%LOG_FILE%"
echo.
echo [INFO] Opening AIS Server management interface...
echo [INFO] Opening browser to access management interface >> "%LOG_FILE%"
start http://127.0.0.1:5888/
echo [SUCCESS] Browser opened, access URL: http://127.0.0.1:5888/
color 0A
goto :cleanup

:cleanup
echo.
echo Log file: %LOG_FILE%
echo End time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"
echo.

if %INSTALL_SUCCESS% EQU 1 (
    echo Installation successful! Window will close automatically in 5 seconds...
    timeout /t 5 /nobreak >nul
) else (
    echo Installation not fully successful, window will close automatically in 60 seconds...
    echo Please check the log file for detailed information: %LOG_FILE%
    timeout /t 60 /nobreak >nul
)
exit %INSTALL_SUCCESS%