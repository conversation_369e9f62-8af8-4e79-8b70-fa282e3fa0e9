# third_party
This directory contains ("vendors") abbreviated copies of the following repositories:

* OpenAPI - https://github.com/swagger-api/swagger-ui v4.15.5 (LICENSE, dist/)

The third_party/OpenAPI directory contains HTML, Javascript,
and CSS assets that dynamically generate Swagger documentation from a
Swagger-compliant API definition in [users.swagger.json](./OpenAPI/users/v1/users.swagger.json)
file. That file is auto-generated by running `make generate` in the root
of this repository. The static assets are copied from
[this dist folder](https://github.com/swagger-api/swagger-ui/tree/v4.15.5/dist)
of the OpenAPI-UI project. After copying, [`swagger-initializer.js`](./OpenAPI/swagger-initializer.js)
is edited to load the swagger file from the local server instead of the default petstore.

The steps above can be done automatically by using `make generate/swagger-ui` which is executing [./scripts/generate-swagger-ui.sh](./scripts/generate-swagger-ui.sh). The script will clone the [OpenAPI repo](https://github.com/swagger-api/swagger-ui) with specific version defined by `$SWAGGER_UI_VERSION` and cache it at `./.cache/swagger-ui/$SWAGGER_UI_VERSION` so if the script is running twice using the same version it will use the cache right away.

See the respective LICENSE files for each project for the applicable license terms.
