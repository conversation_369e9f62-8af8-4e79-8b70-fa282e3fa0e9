package domain

import (
	"time"
)

const (
	CHECK_SERVER_TIMEOUT           = 3 * time.Second
	GRPC_WAIT_TIMEOUT              = 5 * time.Second
	MQTT_WAIT_TIMEOUT              = 15 * time.Second
	PROGRAM_TIMEOUT                = 60 * time.Second
	TOPIC_SYS_BROKER_CLIENTS_TOTAL = "$SYS/broker/clients/total"
	TOPIC_X_CLIENT_CALL            = "$AIS/broker/clients/*/call"
	TOPIC_X_CLIENT_CMD             = "$AIS/broker/clients/*/cmd"
	TOPIC_ALL_CLIENTS_HEARTBEAT    = "$AIS/broker/clients/+/heartbeat"
	TOPIC_X_CLIENT_HEARTBEAT       = "$AIS/broker/clients/*/heartbeat"
	TOPIC_X_CLIENT_MESSAGE         = "$AIS/broker/clients/*/message"
)

var OsType = struct {
	Windows string
	Linux   string
	Darwin  string
}{
	Windows: "windows",
	Linux:   "linux",
	Darwin:  "darwin",
}

var ScriptNames = struct {
	Backup    string
	Config    string
	Install   string
	Uninstall string
	Health    string
	Start     string
	Stop      string
}{
	Backup:    "backup",
	Config:    "config",
	Install:   "install",
	Uninstall: "uninstall",
	Health:    "health",
	Start:     "start",
	Stop:      "stop",
}
