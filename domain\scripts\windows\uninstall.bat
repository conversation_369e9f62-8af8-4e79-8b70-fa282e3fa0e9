@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2 %~3 %~4 %~5 %~6 %~7", "", "runas", 1 >> "%temp%\getadmin.vbs"

    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

set "C_NAME=%~1"
set "C_VERSION=%~2"
set "C_OS_TYPE=%~3"
set "C_OS_ARCH=%~4"
set "C_PKG_DIR=%~5"
IF "%C_VERSION%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_NAME%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_OS_TYPE%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_OS_ARCH%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_PKG_DIR%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
)
set "C_IS_SDK=%~6"
if "%C_IS_SDK%"=="" (
    set "C_IS_SDK=false"
)
set "C_TAR_ROOT=%~7"
if "%C_TAR_ROOT%"=="" (
    set "C_TAR_ROOT=C:\AIS\%C_NAME%"
)

@REM 查找并卸载：判断C_TAR_ROOT目录是否存在，存在则卸载
if exist "%C_TAR_ROOT%\" (
    goto :uninstall
)
goto :eof

@REM 卸载：删除 C_TAR_ROOT 目录
:uninstall
sc query "%C_NAME%" | find "STATE" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    sc stop "%C_NAME%"
    sc delete "%C_NAME%"
)
rd /S /Q "%C_TAR_ROOT%\"
goto :eof