const i18n = {
  'en-US': {
    'menu.main': 'Main',
    'menu.main.center': 'Control Center',
    'menu.list': 'List',
    'menu.list.card': 'Card List',
    'main.message.app-not-found.tips': 'No available app found, please check it.',
    'cardList.tag.upgraded': 'Upgraded',
    'cardList.tag.upgradable': 'Upgradable',
    'cardList.tag.upgradable.confirm': 'Confirm upgrade?',
    'cardList.upgrade.failed': 'Upgrade failed',
    'cardList.upgrade.success': 'Upgrade success',
    'center.cardBlock.delete.title': 'Delete confirmation',
    'center.cardBlock.delete.content': 'Are you sure you want to delete?',
    'center.cardBlock.delete.operation.success': 'Delete success',
    'center.cardBlock.delete.operation.fail': 'Delete failed',
    'center.cardBlock.delete.operation.disabled': 'Delete disabled',
    'center.cardBlock.sync.operation.fail': 'Sync failed',
    'center.cardBlock.sync.operation.success': 'Sync success',
    'center.cardBlock.sync.operation.noData': 'No data',
    'center.cardBlock.sync.operation.offline': 'Sync failed, client is offline',
    'center.cardBlock.head.extra.sync': 'Sync',
    'center.cardBlock.head.extra.delete': 'Delete',
    'center.cardBlock.content.name': 'Name',
    'center.cardBlock.content.network': 'Network',
    'center.cardBlock.content.uptime': 'Uptime',
    'center.cardBlock.state.waiting': 'Waiting',
    'center.cardBlock.state.executing': 'Executing',
    'center.cardBlock.state.running': 'Running',
    'center.cardBlock.state.stopped': 'Stopped',
    'center.cardBlock.state.exited': 'Exited',
    'center.cardBlock.state.unknown': 'Unknown',
    'center.cardBlock.state.offline': 'Offline',
    'center.cardBlock.state.online': 'Online',
    'center.cardBlock.modal.update.title': 'Update confirmation',
    'center.cardBlock.modal.update.content': 'Are you sure you want to update?',
    'center.cardBlock.modal.update.operation.success': 'Update successfully',
    'center.cardBlock.modal.update.operation.fail': 'Update failed',
    'center.controlClient.error': 'Control client error',
    'center.controlClient.error.noTarget': 'No target found',
  },
  'zh-CN': {
    'menu.main': '主页',
    'menu.main.center': '控制中心',
    'menu.list': '列表页',
    'menu.list.card': '卡片列表',
    'main.message.app-not-found.tips': '未找到可用应用，请检查。',
    'cardList.tag.upgraded': '已升级',
    'cardList.tag.upgradable': '可升级',
    'cardList.tag.upgradable.confirm': '确认升级?',
    'cardList.upgrade.failed': '升级失败',
    'cardList.upgrade.success': '升级成功',
    'center.cardBlock.delete.title': '删除确认',
    'center.cardBlock.delete.content': '确定要删除吗？',
    'center.cardBlock.delete.operation.success': '删除成功',
    'center.cardBlock.delete.operation.fail': '删除失败',
    'center.cardBlock.delete.operation.disabled': '禁止删除',
    'center.cardBlock.sync.operation.fail': '同步失败',
    'center.cardBlock.sync.operation.success': '同步成功',
    'center.cardBlock.sync.operation.noData': '无数据',
    'center.cardBlock.sync.operation.offline': '同步失败，客户端已离线',
    'center.cardBlock.head.extra.sync': '同步',
    'center.cardBlock.head.extra.delete': '删除',
    'center.cardBlock.content.name': '名称',
    'center.cardBlock.content.network': '网络',
    'center.cardBlock.content.uptime': '运行时间',
    'center.cardBlock.state.waiting': '等待中',
    'center.cardBlock.state.executing': '执行中',
    'center.cardBlock.state.running': '运行中',
    'center.cardBlock.state.stopped': '已停止',
    'center.cardBlock.state.exited': '已退出',
    'center.cardBlock.state.unknown': '未知',
    'center.cardBlock.state.offline': '离线',
    'center.cardBlock.state.online': '在线',
    'center.cardBlock.modal.update.title': '更新确认',
    'center.cardBlock.modal.update.content': '确定要更新吗？',
    'center.cardBlock.modal.update.operation.success': '更新成功',
    'center.cardBlock.modal.update.operation.fail': '更新失败',
    'center.controlClient.error': '控制客户端错误',
    'center.controlClient.error.noTarget': '未找到目标',
  }
};

export default i18n;
