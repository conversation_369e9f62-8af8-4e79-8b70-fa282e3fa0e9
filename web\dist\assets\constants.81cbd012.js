import{j as e,s as t,B as a,T as n}from"./index.bbeb3af6.js";const{Text:d}=n;function l(n,l){return[{title:n["patch.columns.id"],dataIndex:"id"},{title:n["patch.columns.attribution"],dataIndex:"packageName",render:t=>e(d,{copyable:!0,children:t})},{title:n["patch.columns.name"],dataIndex:"name",render:t=>e(d,{copyable:!0,children:t})},{title:n["patch.columns.uri"],dataIndex:"uri",render:t=>e(d,{copyable:!0,children:t})},{title:n["patch.columns.operation"],dataIndex:"operation",headerCellStyle:{paddingLeft:"15px"},render:(d,i)=>e(t,{children:e(a,{type:"text",size:"small",onClick:()=>l(i,"delete"),children:n["patch.columns.operation.delete"]})})}]}export{l as getColumns};
