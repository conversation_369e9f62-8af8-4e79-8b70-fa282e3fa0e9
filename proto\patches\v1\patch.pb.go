// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: patches/v1/patch.proto

package patchesv1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_patches_v1_patch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_patches_v1_patch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_patches_v1_patch_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommonResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type ListPatchesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Data    []*Patch `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ListPatchesResponse) Reset() {
	*x = ListPatchesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_patches_v1_patch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPatchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPatchesResponse) ProtoMessage() {}

func (x *ListPatchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_patches_v1_patch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPatchesResponse.ProtoReflect.Descriptor instead.
func (*ListPatchesResponse) Descriptor() ([]byte, []int) {
	return file_patches_v1_patch_proto_rawDescGZIP(), []int{1}
}

func (x *ListPatchesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListPatchesResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPatchesResponse) GetData() []*Patch {
	if x != nil {
		return x.Data
	}
	return nil
}

type Patch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PackageName string `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	Uri         string `protobuf:"bytes,4,opt,name=uri,proto3" json:"uri,omitempty"`
}

func (x *Patch) Reset() {
	*x = Patch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_patches_v1_patch_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Patch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Patch) ProtoMessage() {}

func (x *Patch) ProtoReflect() protoreflect.Message {
	mi := &file_patches_v1_patch_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Patch.ProtoReflect.Descriptor instead.
func (*Patch) Descriptor() ([]byte, []int) {
	return file_patches_v1_patch_proto_rawDescGZIP(), []int{2}
}

func (x *Patch) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Patch) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Patch) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Patch) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

var File_patches_v1_patch_proto protoreflect.FileDescriptor

var file_patches_v1_patch_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e, 0x0a,
	0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x6a, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x05, 0x50, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x32, 0xb8, 0x02, 0x0a, 0x0c,
	0x50, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x94, 0x01, 0x0a,
	0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x11, 0x2e, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x74, 0x63, 0x68, 0x1a,
	0x1f, 0x2e, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x51, 0x92, 0x41, 0x34, 0x0a, 0x07, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x20, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x1a, 0x1b, 0x4c, 0x69,
	0x73, 0x74, 0x20, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68,
	0x65, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x62,
	0x01, 0x2a, 0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x12, 0x90, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61,
	0x74, 0x63, 0x68, 0x12, 0x11, 0x2e, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x74, 0x63, 0x68, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x52, 0x92, 0x41, 0x38, 0x0a, 0x07, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x12, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x20, 0x70, 0x61, 0x74, 0x63, 0x68, 0x1a, 0x1f,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x20, 0x61, 0x20, 0x70, 0x61, 0x74, 0x63, 0x68, 0x20, 0x66,
	0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x11, 0x2a, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x42, 0xf4, 0x01, 0x92, 0x41, 0x48, 0x12, 0x05, 0x32, 0x03,
	0x31, 0x2e, 0x30, 0x2a, 0x01, 0x02, 0x72, 0x3c, 0x0a, 0x0a, 0x41, 0x49, 0x53, 0x20, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x12, 0x2e, 0x68, 0x74, 0x74, 0x70, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x2e, 0x76, 0x31, 0x42, 0x0a, 0x50, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f, 0x6e, 0x67,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2f, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x50, 0x58, 0x58, 0xaa, 0x02, 0x0a, 0x50,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0a, 0x50, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x16, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea,
	0x02, 0x0b, 0x50, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_patches_v1_patch_proto_rawDescOnce sync.Once
	file_patches_v1_patch_proto_rawDescData = file_patches_v1_patch_proto_rawDesc
)

func file_patches_v1_patch_proto_rawDescGZIP() []byte {
	file_patches_v1_patch_proto_rawDescOnce.Do(func() {
		file_patches_v1_patch_proto_rawDescData = protoimpl.X.CompressGZIP(file_patches_v1_patch_proto_rawDescData)
	})
	return file_patches_v1_patch_proto_rawDescData
}

var file_patches_v1_patch_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_patches_v1_patch_proto_goTypes = []interface{}{
	(*CommonResponse)(nil),      // 0: patches.v1.CommonResponse
	(*ListPatchesResponse)(nil), // 1: patches.v1.ListPatchesResponse
	(*Patch)(nil),               // 2: patches.v1.Patch
}
var file_patches_v1_patch_proto_depIdxs = []int32{
	2, // 0: patches.v1.ListPatchesResponse.data:type_name -> patches.v1.Patch
	2, // 1: patches.v1.PatchService.ListPatches:input_type -> patches.v1.Patch
	2, // 2: patches.v1.PatchService.DeletePatch:input_type -> patches.v1.Patch
	1, // 3: patches.v1.PatchService.ListPatches:output_type -> patches.v1.ListPatchesResponse
	0, // 4: patches.v1.PatchService.DeletePatch:output_type -> patches.v1.CommonResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_patches_v1_patch_proto_init() }
func file_patches_v1_patch_proto_init() {
	if File_patches_v1_patch_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_patches_v1_patch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_patches_v1_patch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPatchesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_patches_v1_patch_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Patch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_patches_v1_patch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_patches_v1_patch_proto_goTypes,
		DependencyIndexes: file_patches_v1_patch_proto_depIdxs,
		MessageInfos:      file_patches_v1_patch_proto_msgTypes,
	}.Build()
	File_patches_v1_patch_proto = out.File
	file_patches_v1_patch_proto_rawDesc = nil
	file_patches_v1_patch_proto_goTypes = nil
	file_patches_v1_patch_proto_depIdxs = nil
}
