// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: packages/v1/package.proto

package packagesv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PackageService_ListPackages_FullMethodName           = "/packages.v1.PackageService/ListPackages"
	PackageService_ListPatchesByPackageId_FullMethodName = "/packages.v1.PackageService/ListPatchesByPackageId"
	PackageService_SavePackage_FullMethodName            = "/packages.v1.PackageService/SavePackage"
	PackageService_UploadPackage_FullMethodName          = "/packages.v1.PackageService/UploadPackage"
	PackageService_DownloadPackage_FullMethodName        = "/packages.v1.PackageService/DownloadPackage"
	PackageService_GetPackageFile_FullMethodName         = "/packages.v1.PackageService/GetPackageFile"
	PackageService_DeletePackage_FullMethodName          = "/packages.v1.PackageService/DeletePackage"
	PackageService_GetPatchFile_FullMethodName           = "/packages.v1.PackageService/GetPatchFile"
)

// PackageServiceClient is the client API for PackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PackageServiceClient interface {
	ListPackages(ctx context.Context, in *ListPackagesRequest, opts ...grpc.CallOption) (*ListPackagesResponse, error)
	ListPatchesByPackageId(ctx context.Context, in *Package, opts ...grpc.CallOption) (*ListPatchesResponse, error)
	SavePackage(ctx context.Context, in *Package, opts ...grpc.CallOption) (*PackageResponse, error)
	UploadPackage(ctx context.Context, in *UploadPackageRequest, opts ...grpc.CallOption) (*PackageResponse, error)
	DownloadPackage(ctx context.Context, in *DownloadPackageRequest, opts ...grpc.CallOption) (*PackageResponse, error)
	GetPackageFile(ctx context.Context, in *Package, opts ...grpc.CallOption) (PackageService_GetPackageFileClient, error)
	DeletePackage(ctx context.Context, in *Package, opts ...grpc.CallOption) (*PackageResponse, error)
	GetPatchFile(ctx context.Context, in *Patch, opts ...grpc.CallOption) (PackageService_GetPatchFileClient, error)
}

type packageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPackageServiceClient(cc grpc.ClientConnInterface) PackageServiceClient {
	return &packageServiceClient{cc}
}

func (c *packageServiceClient) ListPackages(ctx context.Context, in *ListPackagesRequest, opts ...grpc.CallOption) (*ListPackagesResponse, error) {
	out := new(ListPackagesResponse)
	err := c.cc.Invoke(ctx, PackageService_ListPackages_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) ListPatchesByPackageId(ctx context.Context, in *Package, opts ...grpc.CallOption) (*ListPatchesResponse, error) {
	out := new(ListPatchesResponse)
	err := c.cc.Invoke(ctx, PackageService_ListPatchesByPackageId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) SavePackage(ctx context.Context, in *Package, opts ...grpc.CallOption) (*PackageResponse, error) {
	out := new(PackageResponse)
	err := c.cc.Invoke(ctx, PackageService_SavePackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) UploadPackage(ctx context.Context, in *UploadPackageRequest, opts ...grpc.CallOption) (*PackageResponse, error) {
	out := new(PackageResponse)
	err := c.cc.Invoke(ctx, PackageService_UploadPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) DownloadPackage(ctx context.Context, in *DownloadPackageRequest, opts ...grpc.CallOption) (*PackageResponse, error) {
	out := new(PackageResponse)
	err := c.cc.Invoke(ctx, PackageService_DownloadPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) GetPackageFile(ctx context.Context, in *Package, opts ...grpc.CallOption) (PackageService_GetPackageFileClient, error) {
	stream, err := c.cc.NewStream(ctx, &PackageService_ServiceDesc.Streams[0], PackageService_GetPackageFile_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &packageServiceGetPackageFileClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PackageService_GetPackageFileClient interface {
	Recv() (*FileResponse, error)
	grpc.ClientStream
}

type packageServiceGetPackageFileClient struct {
	grpc.ClientStream
}

func (x *packageServiceGetPackageFileClient) Recv() (*FileResponse, error) {
	m := new(FileResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *packageServiceClient) DeletePackage(ctx context.Context, in *Package, opts ...grpc.CallOption) (*PackageResponse, error) {
	out := new(PackageResponse)
	err := c.cc.Invoke(ctx, PackageService_DeletePackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) GetPatchFile(ctx context.Context, in *Patch, opts ...grpc.CallOption) (PackageService_GetPatchFileClient, error) {
	stream, err := c.cc.NewStream(ctx, &PackageService_ServiceDesc.Streams[1], PackageService_GetPatchFile_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &packageServiceGetPatchFileClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PackageService_GetPatchFileClient interface {
	Recv() (*FileResponse, error)
	grpc.ClientStream
}

type packageServiceGetPatchFileClient struct {
	grpc.ClientStream
}

func (x *packageServiceGetPatchFileClient) Recv() (*FileResponse, error) {
	m := new(FileResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PackageServiceServer is the server API for PackageService service.
// All implementations should embed UnimplementedPackageServiceServer
// for forward compatibility
type PackageServiceServer interface {
	ListPackages(context.Context, *ListPackagesRequest) (*ListPackagesResponse, error)
	ListPatchesByPackageId(context.Context, *Package) (*ListPatchesResponse, error)
	SavePackage(context.Context, *Package) (*PackageResponse, error)
	UploadPackage(context.Context, *UploadPackageRequest) (*PackageResponse, error)
	DownloadPackage(context.Context, *DownloadPackageRequest) (*PackageResponse, error)
	GetPackageFile(*Package, PackageService_GetPackageFileServer) error
	DeletePackage(context.Context, *Package) (*PackageResponse, error)
	GetPatchFile(*Patch, PackageService_GetPatchFileServer) error
}

// UnimplementedPackageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedPackageServiceServer struct {
}

func (UnimplementedPackageServiceServer) ListPackages(context.Context, *ListPackagesRequest) (*ListPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPackages not implemented")
}
func (UnimplementedPackageServiceServer) ListPatchesByPackageId(context.Context, *Package) (*ListPatchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPatchesByPackageId not implemented")
}
func (UnimplementedPackageServiceServer) SavePackage(context.Context, *Package) (*PackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SavePackage not implemented")
}
func (UnimplementedPackageServiceServer) UploadPackage(context.Context, *UploadPackageRequest) (*PackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadPackage not implemented")
}
func (UnimplementedPackageServiceServer) DownloadPackage(context.Context, *DownloadPackageRequest) (*PackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadPackage not implemented")
}
func (UnimplementedPackageServiceServer) GetPackageFile(*Package, PackageService_GetPackageFileServer) error {
	return status.Errorf(codes.Unimplemented, "method GetPackageFile not implemented")
}
func (UnimplementedPackageServiceServer) DeletePackage(context.Context, *Package) (*PackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePackage not implemented")
}
func (UnimplementedPackageServiceServer) GetPatchFile(*Patch, PackageService_GetPatchFileServer) error {
	return status.Errorf(codes.Unimplemented, "method GetPatchFile not implemented")
}

// UnsafePackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PackageServiceServer will
// result in compilation errors.
type UnsafePackageServiceServer interface {
	mustEmbedUnimplementedPackageServiceServer()
}

func RegisterPackageServiceServer(s grpc.ServiceRegistrar, srv PackageServiceServer) {
	s.RegisterService(&PackageService_ServiceDesc, srv)
}

func _PackageService_ListPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ListPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_ListPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ListPackages(ctx, req.(*ListPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_ListPatchesByPackageId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Package)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ListPatchesByPackageId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_ListPatchesByPackageId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ListPatchesByPackageId(ctx, req.(*Package))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_SavePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Package)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).SavePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_SavePackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).SavePackage(ctx, req.(*Package))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_UploadPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).UploadPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_UploadPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).UploadPackage(ctx, req.(*UploadPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_DownloadPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadPackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).DownloadPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_DownloadPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).DownloadPackage(ctx, req.(*DownloadPackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_GetPackageFile_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(Package)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PackageServiceServer).GetPackageFile(m, &packageServiceGetPackageFileServer{stream})
}

type PackageService_GetPackageFileServer interface {
	Send(*FileResponse) error
	grpc.ServerStream
}

type packageServiceGetPackageFileServer struct {
	grpc.ServerStream
}

func (x *packageServiceGetPackageFileServer) Send(m *FileResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _PackageService_DeletePackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Package)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).DeletePackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_DeletePackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).DeletePackage(ctx, req.(*Package))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_GetPatchFile_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(Patch)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PackageServiceServer).GetPatchFile(m, &packageServiceGetPatchFileServer{stream})
}

type PackageService_GetPatchFileServer interface {
	Send(*FileResponse) error
	grpc.ServerStream
}

type packageServiceGetPatchFileServer struct {
	grpc.ServerStream
}

func (x *packageServiceGetPatchFileServer) Send(m *FileResponse) error {
	return x.ServerStream.SendMsg(m)
}

// PackageService_ServiceDesc is the grpc.ServiceDesc for PackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "packages.v1.PackageService",
	HandlerType: (*PackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPackages",
			Handler:    _PackageService_ListPackages_Handler,
		},
		{
			MethodName: "ListPatchesByPackageId",
			Handler:    _PackageService_ListPatchesByPackageId_Handler,
		},
		{
			MethodName: "SavePackage",
			Handler:    _PackageService_SavePackage_Handler,
		},
		{
			MethodName: "UploadPackage",
			Handler:    _PackageService_UploadPackage_Handler,
		},
		{
			MethodName: "DownloadPackage",
			Handler:    _PackageService_DownloadPackage_Handler,
		},
		{
			MethodName: "DeletePackage",
			Handler:    _PackageService_DeletePackage_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetPackageFile",
			Handler:       _PackageService_GetPackageFile_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "GetPatchFile",
			Handler:       _PackageService_GetPatchFile_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "packages/v1/package.proto",
}
