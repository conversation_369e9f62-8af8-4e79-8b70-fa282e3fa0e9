package domain

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
	clientsv1 "gitlab.jhonginfo.com/product/ais-server/proto/clients/v1"
	sysinfov1 "gitlab.jhonginfo.com/product/ais-server/proto/sysinfos/v1"
)

var current *Client
var mqttServer *MQTTServer

func InitializeCurrentClient() {
	// Serve MQTT
	if mqttServer == nil {
		id, err := config.GetId()
		if err != nil {
			slog.Error("Get id err: " + err.Error())
		}
		mqttTCPAddr := config.GetMqttTcpAddress()
		mqttWSAddr := config.GetMqttWsAddress()
		mqttServer := MQTTServer{
			Id:         *id,
			TCPAddress: mqttTCPAddr,
			WSAddress:  mqttWSAddr,
		}
		go mqttServer.Run()
	}
	current, err := GetAndSaveCurrent()
	if err != nil {
		slog.Error("Get current error: " + err.Error())
		go ReInitializeCurrentClient()
		return
	}
	parentMQTTServer := current.GetParentMQTTServer()
	if parentMQTTServer != "" {
		if !CheckServerConnection(parentMQTTServer) {
			slog.Error("Parent mqtt server is not connected")
			go ReInitializeCurrentClient()
			return
		}
	}
	err = current.Connect()
	if err != nil {
		slog.Error("Connect err: " + err.Error())
		go ReInitializeCurrentClient()
		return
	}
}

func ReInitializeCurrentClient() {
	if current != nil {
		current.Disconnect()
	}
	time.Sleep(1 * time.Second)
	InitializeCurrentClient()
}

func GetCurrentClient() (*Client, error) {
	if current != nil {
		return current, nil
	}
	var id string
	var err error
	conf := config.Get()
	idFile := filepath.Join(conf.StorageDir, ".id")
	if _, err := os.Stat(idFile); os.IsNotExist(err) {
		id = ""
	} else {
		idBytes, err := os.ReadFile(idFile)
		if err != nil {
			return nil, err
		}
		id = string(idBytes)
	}
	if id == "" {
		return nil, fmt.Errorf("id is empty")
	}
	current = &Client{
		Id: id,
	}
	err = current.GetById()
	if err != nil {
		return nil, err
	}
	return current, nil
}

func GetAndSaveCurrent() (*Client, error) {
	var ssl int32 = 0
	conf := config.Get()
	id, err := config.GetId()
	if err != nil {
		return nil, err
	}
	sysinfo := GetSysinfo()
	if sysinfo == nil {
		return nil, fmt.Errorf("sysinfo is empty")
	}
	newNetworks := []*sysinfov1.Network{}
	grpcPort := sysinfo.Server.GrpcPort
	gatewayPort := sysinfo.Server.GatewayPort
	if conf.SSL {
		ssl = 1
	}
	mqttTcpPort := sysinfo.Server.MqttTcpPort
	mqttWsPort := sysinfo.Server.MqttWsPort
	osType := sysinfo.Os.Type
	uptime := sysinfo.Times.Up
	version := sysinfo.Server.Version
	for _, value := range sysinfo.Networks {
		isIPv4 := value.Family == "IPv4"
		hasMAC := value.Mac != ""
		if isIPv4 && hasMAC {
			newNetworks = append(newNetworks, value)
		}
	}
	networks, err := json.Marshal(newNetworks)
	if err != nil {
		return nil, err
	}
	old := &Client{
		Id: *id,
	}
	err = old.GetById()
	if err != nil {
		current = &Client{
			Id:                *id,
			Name:              *id,
			GrpcPort:          grpcPort,
			GatewayPort:       gatewayPort,
			Ssl:               ssl,
			ParentId:          *id,
			ParentHost:        "localhost",
			ParentGrpcPort:    grpcPort,
			ParentGatewayPort: gatewayPort,
			ParentMqttTcpPort: mqttTcpPort,
			ParentMqttWsPort:  mqttWsPort,
			ParentSsl:         0,
			OsType:            osType,
			Uptime:            uptime,
			Version:           version,
			Networks:          string(networks),
			State:             ClientState.Online,
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
			IsLocal:           1,
		}
		err = current.Add()
		if err != nil {
			return nil, err
		}
	} else {
		if current != nil {
			current = MergeClient(old, current)
		} else {
			current = old
		}
		current.GrpcPort = grpcPort
		current.GatewayPort = gatewayPort
		current.Ssl = ssl
		current.ParentGatewayPort = gatewayPort
		current.ParentGrpcPort = grpcPort
		current.ParentMqttTcpPort = mqttTcpPort
		current.ParentMqttWsPort = mqttWsPort
		current.OsType = osType
		current.Uptime = uptime
		current.Version = version
		current.Networks = string(networks)
		err = current.Online()
		if err != nil {
			return nil, err
		}
	}
	if current.Networks == "" {
		return nil, fmt.Errorf("networks is empty")
	}
	return current, nil
}

func MergeClient(src *Client, tar *Client) *Client {
	if src.Id != "" {
		tar.Id = src.Id
	}
	if src.Name != "" {
		tar.Name = src.Name
	}
	if src.GrpcPort != 0 {
		tar.GrpcPort = src.GrpcPort
	}
	if src.GatewayPort != 0 {
		tar.GatewayPort = src.GatewayPort
	}
	if src.Ssl != 0 {
		tar.Ssl = src.Ssl
	}
	if src.ParentId != "" {
		tar.ParentId = src.ParentId
	}
	if src.ParentHost != "" {
		tar.ParentHost = src.ParentHost
	}
	if src.ParentGrpcPort != 0 {
		tar.ParentGrpcPort = src.ParentGrpcPort
	}
	if src.ParentGatewayPort != 0 {
		tar.ParentGatewayPort = src.ParentGatewayPort
	}
	if src.ParentMqttTcpPort != 0 {
		tar.ParentMqttTcpPort = src.ParentMqttTcpPort
	}
	if src.ParentMqttWsPort != 0 {
		tar.ParentMqttWsPort = src.ParentMqttWsPort
	}
	tar.ParentSsl = src.ParentSsl
	if src.OsType != "" {
		tar.OsType = src.OsType
	}
	if src.Networks != "" {
		tar.Networks = src.Networks
	}
	if src.Uptime != "" {
		tar.Uptime = src.Uptime
	}
	if src.Version != "" {
		tar.Version = src.Version
	}
	if src.State != "" {
		tar.State = src.State
	}
	if !src.CreatedAt.IsZero() {
		tar.CreatedAt = src.CreatedAt
	}
	if !src.UpdatedAt.IsZero() {
		tar.UpdatedAt = src.UpdatedAt
	}
	tar.IsLocal = src.IsLocal
	return tar
}

func ClientVO2PO(vo *clientsv1.Client) *Client {
	if vo == nil {
		return nil
	}
	po := &Client{
		Id:                vo.Id,
		Name:              vo.Name,
		GrpcPort:          vo.GrpcPort,
		GatewayPort:       vo.GatewayPort,
		Ssl:               vo.Ssl,
		ParentId:          vo.ParentId,
		ParentHost:        vo.ParentHost,
		ParentGrpcPort:    vo.ParentGrpcPort,
		ParentGatewayPort: vo.ParentGatewayPort,
		ParentMqttTcpPort: vo.ParentMqttTcpPort,
		ParentMqttWsPort:  vo.ParentMqttWsPort,
		ParentSsl:         vo.ParentSsl,
		OsType:            vo.OsType,
		Networks:          vo.Networks,
		Uptime:            vo.Uptime,
		Version:           vo.Version,
		State:             vo.State,
		IsLocal:           vo.IsLocal,
	}
	if vo.CreatedAt != "" {
		createdAt, err := time.Parse("2006-01-02 15:04:05", vo.CreatedAt)
		if err == nil {
			po.CreatedAt = createdAt
		}
	}
	if vo.UpdatedAt != "" {
		updatedAt, err := time.Parse("2006-01-02 15:04:05", vo.UpdatedAt)
		if err == nil {
			po.UpdatedAt = updatedAt
		}
	}
	return po
}

func ClientPOs2VOs(clients []*Client) []*clientsv1.Client {
	var vos []*clientsv1.Client
	for _, client := range clients {
		vo := client.ToVO()
		vos = append(vos, vo)
	}
	return vos
}
