package domain

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"github.com/robfig/cron/v3"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	tasksv1 "gitlab.jhonginfo.com/product/ais-server/proto/tasks/v1"
)

var tasks = make(map[string]func(), 0)
var tempFilesSizeMap = make(map[string]int32, 0)

func AddTask(schedule string, fc func()) bool {
	if _, ok := tasks[schedule]; ok {
		slog.Warn(fmt.Sprintf("Task %s already exists", schedule))
		return false
	}
	tasks[schedule] = fc
	return true
}

func InitializeScheduleTasks() {
	c := cron.New()
	// c.AddFunc("@every 5s", ClearLogsScheduleTask)
	// 自动检查安装包情况
	c.AddFunc("@every 15s", CheckPackageScheduleTask)
	// 自动清理日志
	c.AddFunc("@every 1d", ClearLogsScheduleTask)
	if len(tasks) > 0 {
		for k, fc := range tasks {
			c.AddFunc(k, fc)
		}
	}
	c.Start()
}

func MergeTask(src *Task, tar *Task) *Task {
	if src.Name != "" {
		tar.Name = src.Name
	}
	if src.Type != "" {
		tar.Type = src.Type
	}
	if src.Content != "" {
		tar.Content = src.Content
	}
	if src.State != "" {
		tar.State = src.State
	}
	if src.Progress != "" {
		tar.Progress = src.Progress
	}
	if src.PackageId != 0 {
		tar.PackageId = src.PackageId
	}
	return tar
}

func TaskPO2TaskVO(po *Task) *tasksv1.Task {
	if po == nil {
		return nil
	}
	return &tasksv1.Task{
		Id:        int32(po.Id),
		Name:      po.Name,
		Type:      po.Type,
		Content:   po.Content,
		PackageId: po.PackageId,
		Progress:  po.Progress,
		State:     po.State,
		CreatedAt: po.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: po.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

func TaskVO2TaskPO(vo *tasksv1.Task) *Task {
	if vo == nil {
		return nil
	}
	po := &Task{
		Id:        vo.Id,
		Name:      vo.Name,
		Type:      vo.Type,
		Content:   vo.Content,
		PackageId: vo.PackageId,
		State:     vo.State,
	}
	return po
}

func (b *Backend) ExecuteTask(ctx context.Context, req *tasksv1.Task) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	taskName := req.Name
	task := TaskVO2TaskPO(req)
	if task == nil {
		return nil, fmt.Errorf("task is required")
	}
	if task.Id != 0 {
		old := &Task{Id: task.Id}
		err := old.GetById()
		if err != nil {
			return nil, err
		}
		task.Merge(old)
	}
	task.Id = 0
	task.Type = TaskTypes.Command
	task.State = TaskStates.Finished
	packageId := task.PackageId
	if packageId == 0 {
		return nil, fmt.Errorf("package_id is required")
	}
	pkg := &Package{Id: packageId}
	err := pkg.GetById()
	if err != nil {
		return nil, err
	}
	slog.Debug(fmt.Sprintf("Execute %s task(%s)", pkg.Name, taskName))
	switch taskName {
	case TaskNames.Download:
		if !DownloadPackage(pkg) {
			return nil, fmt.Errorf("download package failed")
		}
		slog.Info(fmt.Sprintf("Download %s successful", pkg.Name))
	case TaskNames.Install:
		err := pkg.Install()
		if err != nil {
			slog.Error(fmt.Sprintf("Install %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Install %s to %s successful", pkg.Name, pkg.InstallPath))
	case TaskNames.Uninstall:
		err := pkg.Uninstall()
		if err != nil {
			slog.Error(fmt.Sprintf("Uninstall %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Uninstall %s successful", pkg.Name))
	case TaskNames.Start:
		err := pkg.Start()
		if err != nil {
			slog.Error(fmt.Sprintf("Start %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Start %s successful", pkg.Name))
	case TaskNames.Stop:
		err := pkg.Stop()
		if err != nil {
			slog.Error(fmt.Sprintf("Stop %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Stop %s successful", pkg.Name))
	case TaskNames.Backup:
		err := pkg.Backup()
		if err != nil {
			slog.Error(fmt.Sprintf("Backup %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Backup %s successful", pkg.Name))
	case TaskNames.Restore:
		err := pkg.Restore()
		if err != nil {
			slog.Error(fmt.Sprintf("Restore %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Restore %s successful", pkg.Name))
	case TaskNames.Update:
		if req.PatchName != "" {
			if !DownloadPatch(pkg, req.PatchName) {
				errMsg := fmt.Sprintf("download patch[%s] error", req.PatchName)
				slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
				return nil, fmt.Errorf(errMsg)
			}
			patchesDir, err := pkg.GetPatchesDir()
			if err != nil {
				errMsg := fmt.Sprintf("get patches dir error: %v", err)
				slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
				return nil, fmt.Errorf(errMsg)
			}
			patchFile := filepath.Join(patchesDir, req.PatchName)
			if _, err := os.Stat(patchFile); os.IsNotExist(err) {
				errMsg := fmt.Sprintf("patch file %s not exists", patchFile)
				slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
				return nil, fmt.Errorf(errMsg)
			}
			err = pkg.UpdateFrom(patchFile)
			if err != nil {
				slog.Error(fmt.Sprintf("Update %s from patch[%s] failed: %s", pkg.Name, req.PatchName, err.Error()))
				return nil, err
			}
			slog.Info(fmt.Sprintf("Update %s successful", pkg.Name))
			break
		}
		if task.Content == "" {
			errMsg := "task content is required"
			slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
			return nil, fmt.Errorf(errMsg)
		}
		err := pkg.UpdateFrom(task.Content)
		if _, err := os.Stat(task.Content); !os.IsNotExist(err) {
			defer os.Remove(task.Content)
		}
		if err != nil {
			slog.Error(fmt.Sprintf("Update %s from file[%s] failed: %s", pkg.Name, task.Content, err.Error()))
			return nil, err
		}
		if _, err := os.Stat(task.Content); !os.IsNotExist(err) {
			patchesDir, err := pkg.GetPatchesDir()
			if err != nil {
				errMsg := fmt.Sprintf("get patches dir error: %v", err)
				slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
				return nil, fmt.Errorf(errMsg)
			}
			err = lib.Copy(task.Content, filepath.Join(patchesDir, time.Now().Format("20060102150405")+".zip"))
			if err != nil {
				errMsg := fmt.Sprintf("copy %s to %s error: %v", task.Content, patchesDir, err)
				slog.Error(fmt.Sprintf("Update %s failed: %s", pkg.Name, errMsg))
				return nil, fmt.Errorf(errMsg)
			}
		}
		slog.Info(fmt.Sprintf("Update %s successful", pkg.Name))
	case TaskNames.Upgrade:
		storeDir, err := config.GetStoreDir()
		if err != nil {
			return nil, err
		}
		storePkgDir := filepath.Join(storeDir, pkg.Name)
		if _, err := os.Stat(storePkgDir); os.IsNotExist(err) {
			err = os.Mkdir(storePkgDir, 0755)
			if err != nil {
				return nil, fmt.Errorf("create %s error: %v", storePkgDir, err)
			}
		}
		newFile := filepath.Join(storePkgDir, time.Now().Format("20060102"))
		err = lib.Copy(task.Content, newFile)
		if err != nil {
			return nil, fmt.Errorf("copy %s to %s error: %v", task.Content, newFile, err)
		}
		slog.Info(fmt.Sprintf("Backup %s to %s successful", task.Content, newFile))
		defer os.Remove(task.Content)
		err = pkg.UpgradeFrom(newFile)
		if err != nil {
			slog.Error(fmt.Sprintf("Upgrade %s from file[%s] failed: %s", pkg.Name, newFile, err.Error()))
			os.Remove(newFile)
			return nil, err
		}
		slog.Info(fmt.Sprintf("Upgrade %s successful", pkg.Name))
	case TaskNames.Export:
		err := pkg.Export()
		if err != nil {
			slog.Error(fmt.Sprintf("Export %s failed: %s", pkg.Name, err.Error()))
			return nil, err
		}
		pkgDir, err := pkg.GetPackageDir()
		if err != nil {
			slog.Error(fmt.Sprintf("Export %s failed of get package dir: %s", pkg.Name, err.Error()))
			return nil, err
		}
		pkgUri := filepath.Join(pkgDir, pkg.Name+".zip")
		if _, err := os.Stat(pkgUri); os.IsNotExist(err) {
			slog.Error(fmt.Sprintf("Export %s failed of package uri not exists: %s", pkg.Name, pkgUri))
			return nil, err
		}
		if _, err := os.Stat(pkg.Uri); !os.IsNotExist(err) {
			err = os.Remove(pkg.Uri)
			if err != nil {
				slog.Error(fmt.Sprintf("Export %s failed of remove package uri: %s", pkg.Name, err.Error()))
				defer os.Remove(pkgUri)
				return nil, err
			}
		}
		err = os.Rename(pkgUri, pkg.Uri)
		if err != nil {
			slog.Error(fmt.Sprintf("Export %s failed of rename package uri: %s", pkg.Name, err.Error()))
			defer os.Remove(pkgUri)
			return nil, err
		}
		task.Content = pkg.Uri
		slog.Info(fmt.Sprintf("Export %s successful", pkg.Name))
	}
	err = task.Add()
	if err != nil {
		return nil, err
	}
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

func (b *Backend) GetTask(ctx context.Context, req *tasksv1.GetTaskRequest) (*tasksv1.TaskResponse, error) {
	task := &Task{Id: req.Id}
	err := task.GetById()
	if err != nil {
		return nil, err
	}
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

// CreateTaskForLocalImport 创建本地文件导入任务
func (b *Backend) CreateTaskForLocalImport(ctx context.Context, req *tasksv1.LocalImportRequest) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	slog.Info(fmt.Sprintf("[API] 创建本地导入任务请求 - 包名: %s, 版本: %s, 本地路径: %s", req.PackageName, req.PackageVersion, req.LocalPath))

	task := &Task{}
	err := task.ImportLocalPackage(req.PackageName, req.PackageVersion, req.LocalPath)
	if err != nil {
		slog.Error(fmt.Sprintf("[API] 创建本地导入任务失败 - 包名: %s, 错误: %v", req.PackageName, err))
		return nil, err
	}

	slog.Info(fmt.Sprintf("[API] 创建本地导入任务成功 - 任务ID: %d, 包名: %s", task.Id, req.PackageName))
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

func (b *Backend) CreateTaskForUploadPackage(ctx context.Context, req *tasksv1.UploadRequest) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	task := &Task{}
	task.AddForUploadPackage(req.PackageName, req.PackageVersion)
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

func (b *Backend) ExecuteTaskForUploadPackage(ctx context.Context, req *tasksv1.UploadRequest) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	task := &Task{
		Id: req.Id,
	}
	err := task.GetById()
	if err != nil {
		return nil, err
	}

	chunkIndex := req.GetChunkIndex()
	chunkSize := req.GetChunkSize()
	chunkContent := req.GetChunkContent()
	totalChunks := req.GetTotalChunks()
	totalSize := req.GetTotalSize()
	if chunkContent != nil {
		err := task.UploadPackage(chunkIndex, chunkContent, totalChunks)
		if err != nil {
			slog.Error(fmt.Sprintf("Upload %s chunk[%d] failed: %s", task.Content, chunkIndex, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Upload %s chunk[%d] successful", task.Content, chunkIndex))
		tempFilesSizeMap[task.Content] += chunkSize
		slog.Debug(fmt.Sprintf("Check %s cache size: %d, total size: %d", task.Content, tempFilesSizeMap[task.Content], totalSize))
		if tempFilesSizeMap[task.Content] >= totalSize {
			err = task.MergePackage(totalChunks)
			if err != nil {
				slog.Error(fmt.Sprintf("Merge %s failed: %s", task.Content, err.Error()))
				return nil, err
			}
			slog.Info(fmt.Sprintf("Merge %s successful", task.Content))
			delete(tempFilesSizeMap, task.Content)
		}
	}
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

func (b *Backend) CreateTaskForUpload(ctx context.Context, req *tasksv1.UploadRequest) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	task := &Task{}
	task.AddForUpload(req.PackageName, req.PackageVersion)
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}

func (b *Backend) ExecuteTaskForUpload(ctx context.Context, req *tasksv1.UploadRequest) (*tasksv1.TaskResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	task := &Task{
		Id: req.Id,
	}
	err := task.GetById()
	if err != nil {
		return nil, err
	}

	chunkIndex := req.GetChunkIndex()
	chunkSize := req.GetChunkSize()
	chunkContent := req.GetChunkContent()
	totalChunks := req.GetTotalChunks()
	totalSize := req.GetTotalSize()
	if chunkContent != nil {
		err := task.UploadPackage(chunkIndex, chunkContent, totalChunks)
		if err != nil {
			slog.Error(fmt.Sprintf("Upload %s chunk[%d] failed: %s", task.Content, chunkIndex, err.Error()))
			return nil, err
		}
		slog.Info(fmt.Sprintf("Upload %s chunk[%d] successful", task.Content, chunkIndex))
		tempFilesSizeMap[task.Content] += chunkSize
		slog.Debug(fmt.Sprintf("Check %s cache size: %d, total size: %d", task.Content, tempFilesSizeMap[task.Content], totalSize))
		if tempFilesSizeMap[task.Content] >= totalSize {
			err = task.MergeUpload(totalChunks)
			if err != nil {
				slog.Error(fmt.Sprintf("Merge %s failed: %s", task.Content, err.Error()))
				return nil, err
			}
			slog.Info(fmt.Sprintf("Merge %s successful", task.Content))
			delete(tempFilesSizeMap, task.Content)
		}
	}
	return &tasksv1.TaskResponse{
		Code: 0,
		Data: TaskPO2TaskVO(task),
	}, nil
}
