// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: patches/v1/patch.proto

package patchesv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PatchService_ListPatches_FullMethodName = "/patches.v1.PatchService/ListPatches"
	PatchService_DeletePatch_FullMethodName = "/patches.v1.PatchService/DeletePatch"
)

// PatchServiceClient is the client API for PatchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PatchServiceClient interface {
	ListPatches(ctx context.Context, in *Patch, opts ...grpc.CallOption) (*ListPatchesResponse, error)
	DeletePatch(ctx context.Context, in *Patch, opts ...grpc.CallOption) (*CommonResponse, error)
}

type patchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPatchServiceClient(cc grpc.ClientConnInterface) PatchServiceClient {
	return &patchServiceClient{cc}
}

func (c *patchServiceClient) ListPatches(ctx context.Context, in *Patch, opts ...grpc.CallOption) (*ListPatchesResponse, error) {
	out := new(ListPatchesResponse)
	err := c.cc.Invoke(ctx, PatchService_ListPatches_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *patchServiceClient) DeletePatch(ctx context.Context, in *Patch, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, PatchService_DeletePatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PatchServiceServer is the server API for PatchService service.
// All implementations should embed UnimplementedPatchServiceServer
// for forward compatibility
type PatchServiceServer interface {
	ListPatches(context.Context, *Patch) (*ListPatchesResponse, error)
	DeletePatch(context.Context, *Patch) (*CommonResponse, error)
}

// UnimplementedPatchServiceServer should be embedded to have forward compatible implementations.
type UnimplementedPatchServiceServer struct {
}

func (UnimplementedPatchServiceServer) ListPatches(context.Context, *Patch) (*ListPatchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPatches not implemented")
}
func (UnimplementedPatchServiceServer) DeletePatch(context.Context, *Patch) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePatch not implemented")
}

// UnsafePatchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PatchServiceServer will
// result in compilation errors.
type UnsafePatchServiceServer interface {
	mustEmbedUnimplementedPatchServiceServer()
}

func RegisterPatchServiceServer(s grpc.ServiceRegistrar, srv PatchServiceServer) {
	s.RegisterService(&PatchService_ServiceDesc, srv)
}

func _PatchService_ListPatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Patch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PatchServiceServer).ListPatches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PatchService_ListPatches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PatchServiceServer).ListPatches(ctx, req.(*Patch))
	}
	return interceptor(ctx, in, info, handler)
}

func _PatchService_DeletePatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Patch)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PatchServiceServer).DeletePatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PatchService_DeletePatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PatchServiceServer).DeletePatch(ctx, req.(*Patch))
	}
	return interceptor(ctx, in, info, handler)
}

// PatchService_ServiceDesc is the grpc.ServiceDesc for PatchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PatchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "patches.v1.PatchService",
	HandlerType: (*PatchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPatches",
			Handler:    _PatchService_ListPatches_Handler,
		},
		{
			MethodName: "DeletePatch",
			Handler:    _PatchService_DeletePatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "patches/v1/patch.proto",
}
