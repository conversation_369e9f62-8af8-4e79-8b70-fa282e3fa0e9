package domain

import (
	"fmt"
	"log/slog"
	"net"
	"os"
	"runtime"
	"strings"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/lib"
	programsv1 "gitlab.jhonginfo.com/product/ais-server/proto/programs/v1"
)

var ProgramCommands = struct {
	Start     string
	Stop      string
}{
	Start:     "start",
	Stop:      "stop",
}

type ProgramMessage struct {
	Timestamp int64
	Datetime  string
	Action    string
	State     string
}

func (t *ProgramMessage) JSON() string {
	return lib.ToJSON(t)
}

func (t *ProgramMessage) FromJSON(data string) error {
	return lib.FromJSON(data, t)
}

func NewProgramActionMessage(act string) ProgramMessage {
	now := time.Now()
	return ProgramMessage{
		Timestamp: now.Unix(),
		Datetime:  now.Format("2006-01-02 15:04:05"),
		Action:    act,
	}
}

var ProgramActions = struct {
	Start     ProgramMessage
	Stop      ProgramMessage
	Uninstall ProgramMessage
}{
	Start:     NewProgramActionMessage("start"),
	Stop:      NewProgramActionMessage("stop"),
	Uninstall: NewProgramActionMessage("uninstall"),
}

func NeProgramStateMessage(state string) ProgramMessage {
	now := time.Now()
	return ProgramMessage{
		Timestamp: now.Unix(),
		Datetime:  now.Format("2006-01-02 15:04:05"),
		State:     state,
	}
}

var ProgramStates = struct {
	Installed   ProgramMessage
	Running     ProgramMessage
	Exited      ProgramMessage
	Uninstalled ProgramMessage
	Unkonwn     ProgramMessage
}{
	Installed:   NeProgramStateMessage("installed"),
	Running:     NeProgramStateMessage("running"),
	Exited:      NeProgramStateMessage("exited"),
	Uninstalled: NeProgramStateMessage("uninstalled"),
	Unkonwn:     NeProgramStateMessage("unknown"),
}

type Program struct {
	Id          int64  `json:"id" gorm:"primary_key"`
	Name        string `json:"name"`
	State       string `json:"state"`
	PackagePath string `json:"package_path"`
	InstallPath string `json:"install_path"`
	Port     	int64  `json:"port"`
	Bootable    bool   `json:"bootable"`
	Sort        int32  `json:"sort"`
}

func (t *Program) ToVO() *programsv1.Program {
	if t == nil {
		return nil
	}
	vo := &programsv1.Program{
		Id:          t.Id,
		Name:        t.Name,
		State:       t.State,
		PackagePath: t.PackagePath,
		InstallPath: t.InstallPath,
		Bootable:    t.Bootable,
		Sort:        t.Sort,
	}
	return vo
}

func (t *Program) CheckIsRunning() error {
	osType := runtime.GOOS
	switch osType {
	case OsType.Windows:
		program := &WindowsProgram{*t}
		if program.IsRunning() {
			return nil
		} else {
			return fmt.Errorf("service %s is not running", t.Name)
		}
	}
	return fmt.Errorf("not support %s system", osType)
}

func (t *Program) CheckIsNotRunning() error {
	osType := runtime.GOOS
	switch osType {
	case OsType.Windows:
		program := &WindowsProgram{*t}
		if program.IsNotRunning() {
			return nil
		} else {
			return fmt.Errorf("service %s is running", t.Name)
		}
	}
	return fmt.Errorf("not support %s system", osType)
}

func (t *Program) Delete() error {
	if t.InstallPath != "" {
		if _, err := os.Stat(t.InstallPath); !os.IsNotExist(err) {
			if err := Sheller.RemoveAll(t.InstallPath); err != nil {
				return err
			}
		}
	}
	return DeleteByID(t.Id, t)
}

func (t *Program) Start() error {
	osType := runtime.GOOS
	switch osType {
	case OsType.Windows:
		program := &WindowsProgram{*t}
		return program.Start()
	}
	return fmt.Errorf("not support %s system", osType)
}

func (t *Program) Stop() error {
	osType := runtime.GOOS
	switch osType {
	case OsType.Windows:
		program := &WindowsProgram{*t}
		return program.Stop()
	}
	return fmt.Errorf("not support %s system", osType)
}

func (t *Program) Restart() error {
	osType := runtime.GOOS
	switch osType {
	case OsType.Windows:
		program := &WindowsProgram{*t}
		program.Stop()
		return program.Start()
	}
	return fmt.Errorf("not support %s system", osType)
}

type WindowsProgram struct {
	Program
}

func (t *WindowsProgram) IsExisted() bool {
	cmdStr := fmt.Sprintf("sc query %s", t.Name)
	output, err := lib.ExecuteScript(cmdStr)
	if err != nil {
		slog.Error(fmt.Sprintf("Program(%s) query error: %s", t.Name, err.Error()))
		return false
	}
	if output != "" && strings.Contains(output, "SERVICE_NAME") && strings.Contains(output, t.Name) {
		return true
	}
	return false
}

func (t *WindowsProgram) IsRunning() bool {
	if (t.Port > 0) {
		_, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", t.Port), 3*time.Second)
		return err == nil
	} else {
		cmdStr := fmt.Sprintf("sc query %s", t.Name)
		output, err := lib.ExecuteScript(cmdStr)
		if err != nil {
			slog.Warn(fmt.Sprintf("Program(%s) query error: %s", t.Name, err.Error()))
			return false
		}
		if output != "" && strings.Contains(output, "STATE") && strings.Contains(output, "RUNNING") {
			return true
		}
	}
	return false
}

func (t *WindowsProgram) IsNotRunning() bool {
	if (t.Port > 0) {
		_, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", t.Port), 3*time.Second)
		return err != nil
	} else {
		cmdStr := fmt.Sprintf("sc query %s", t.Name)
		output, err := lib.ExecuteScript(cmdStr)
		if err != nil {
			slog.Warn(fmt.Sprintf("Program(%s) query error: %s", t.Name, err.Error()))
			return false
		}
		if output != "" && strings.Contains(output, "STATE") && strings.Contains(output, "STOPPED") {
			return true
		}
	}
	return false
}

func (t *WindowsProgram) Start() error {
	if t.IsExisted() && !t.IsRunning() {
		// sc start "%C_SERVICE_NAME%"
		cmdStr := fmt.Sprintf("sc start %s", t.Name)
		_, err := lib.ExecuteScript(cmdStr)
		if err != nil {
			slog.Warn(fmt.Sprintf("Program(%s) start error: %s", t.Name, err.Error()))
			return fmt.Errorf("service %s start error", t.Name)
		}
		programState := ProgramStates.Unkonwn
		ok, err := t.WaitTimeout(PROGRAM_TIMEOUT, t.IsRunning)
		if ok {
			programState = ProgramStates.Running
			slog.Info(fmt.Sprintf("Program(%s) start success", t.Name))
		} else if err != nil {
			programState = ProgramStates.Unkonwn
			slog.Error(fmt.Sprintf("Program(%s) start error: %s", t.Name, err.Error()))
		} else {
			programState = ProgramStates.Exited
			slog.Warn(fmt.Sprintf("Program(%s) start failed", t.Name))
		}
		err = t.UpdateState(programState.State)
		if err != nil {
			return err
		}
	} else {
		slog.Warn(fmt.Sprintf("Program(%s) already running", t.Name))
		err := t.UpdateState(ProgramStates.Running.State)
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *WindowsProgram) Stop() error {
	if t.IsExisted() && t.IsRunning() {
		// sc stop "%C_SERVICE_NAME%"
		cmdStr := fmt.Sprintf("sc stop %s", t.Name)
		_, err := lib.ExecuteScript(cmdStr)
		if err != nil {
			slog.Warn(fmt.Sprintf("Program(%s) stop error: %s", t.Name, err.Error()))
			return fmt.Errorf("service %s stop error", t.Name)
		}
		programState := ProgramStates.Unkonwn
		ok, err := t.WaitTimeout(PROGRAM_TIMEOUT, t.IsNotRunning)
		if ok {
			programState = ProgramStates.Exited
			slog.Info(fmt.Sprintf("Program(%s) stop success", t.Name))
		} else if err != nil {
			programState = ProgramStates.Unkonwn
			slog.Error(fmt.Sprintf("Program(%s) stop error: %s", t.Name, err.Error()))
		} else {
			slog.Warn(fmt.Sprintf("Program(%s) stop failed", t.Name))
		}
		err = t.UpdateState(programState.State)
		if err != nil {
			return err
		}
	} else {
		slog.Warn(fmt.Sprintf("Program(%s) not running", t.Name))
		err := t.UpdateState(ProgramStates.Exited.State)
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *WindowsProgram) Wait(judge func() bool) bool {
	for {
		if !t.IsExisted() {
			return false
		}
		if judge() {
			return true
		}
		oneSecond := 1 * time.Second
		time.Sleep(oneSecond)
	}
}

func (t *WindowsProgram) WaitTimeout(l time.Duration, judge func() bool) (bool, error) {
	for {
		if !t.IsExisted() {
			return false, nil
		}
		if judge() {
			return true, nil
		}
		oneSecond := 1 * time.Second
		time.Sleep(oneSecond)
		l -= oneSecond
		if l <= 0 {
			return false, fmt.Errorf("timeout")
		}
	}
}