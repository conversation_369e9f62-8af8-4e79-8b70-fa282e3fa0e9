package domain

import (
	"context"
	"fmt"
	"log/slog"

	programsv1 "gitlab.jhonginfo.com/product/ais-server/proto/programs/v1"
)

func (b *Backend) ListPrograms(_ context.Context, req *programsv1.ListProgramsRequest) (*programsv1.ListProgramsResponse, error) {
	var ts []*Program
	var data *programsv1.ListProgramsResponseData
	var total int64
	var err error

	page := int(req.Page)
	size := int(req.Size)

	id := req.GetId()
	name := req.GetName()
	if id != 0 {
		program := &Program{
			Id: id,
		}
		err := program.GetById()
		if err != nil {
			total = 0
			ts = []*Program{}
		} else {
			total = 1
			ts = append(ts, program)
		}
	} else if name != "" {
		do := &Program{
			Name: name,
		}
		total, err = do.CountByName()
		if err != nil {
			return nil, err
		}
		ts, err = do.ListByName()
		if err != nil {
			ts = []*Program{}
		}
	} else {
		do := &Program{}
		total, err = do.Count()
		if err != nil {
			return nil, err
		}
		ts, err = do.List()
		if err != nil {
			ts = []*Program{}
		}
	}
	if page > 0 && size > 0 {
		data = &programsv1.ListProgramsResponseData{
			Total:   int32(total),
			Page:    int32(page),
			Size:    int32(size),
			Content: ProgramPOs2ProgramVOs(ts),
		}
	} else {
		data = &programsv1.ListProgramsResponseData{
			Content: ProgramPOs2ProgramVOs(ts),
		}
	}
	return &programsv1.ListProgramsResponse{
		Code: 0,
		Data: data,
	}, nil
}

func (b *Backend) ExecuteProgram(ctx context.Context, req *programsv1.ProgramCommandRequest) (*programsv1.CommonResponse, error) {
	program := &Program{
		Id: req.GetId(),
	}
	err := program.GetById()
	if err != nil {
		return nil, err
	}
	switch req.GetType() {
		case ProgramCommands.Start:
			slog.Info(fmt.Sprintf("Start program %s", program.Name))
			err = program.Start()
		case ProgramCommands.Stop:
			slog.Info(fmt.Sprintf("Stop program %s", program.Name))
			err = program.Stop()
	}
	if err != nil {
		return nil, err
	}
	return &programsv1.CommonResponse{
		Code:    0,
		Message: "Execute successful",
	}, nil
}

func (b *Backend) DeleteProgram(_ context.Context, req *programsv1.Program) (*programsv1.CommonResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	t := &Program{
		Id: req.GetId(),
	}
	err := t.GetById()
	if err != nil {
		return nil, err
	}
	err = t.Delete()
	if err != nil {
		slog.Error(fmt.Sprintf("Delete program(%s) error: %s", t.Name, err.Error()))
		return nil, err
	}
	return &programsv1.CommonResponse{
		Code:    0,
		Message: "Delete program successful",
	}, nil
}