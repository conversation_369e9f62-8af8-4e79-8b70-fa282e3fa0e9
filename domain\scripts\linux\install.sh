#!/bin/bash
C_NAME=$1
C_VERSION=$2
C_OS_TYPE=$3
C_OS_ARCH=$4
C_PKG_DIR=$5
if [ -z $C_VERSION ] || [ -z $C_NAME ] || [ -z $C_OS_TYPE ] || [ -z $C_OS_ARCH ] || [ -z $C_PKG_DIR ]; then
    echo "Usage: $0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]"
    exit 1
fi
C_IS_SDK=${6:-'false'}
C_TAR_ROOT=${7:-/usr/local}/$C_NAME

C_HOME=$(echo $C_NAME | tr '[a-z]' '[A-Z]')_HOME
C_USER_ROOT=$(cd ~; pwd)
C_BASHRC=/etc/bashrc
C_PACKAGE_PATH=$C_PKG_DIR/$C_NAME
C_ORIGIN_PATH=$C_PKG_DIR/$C_NAME-$C_VERSION-$C_OS_TYPE-$C_OS_ARCH
C_SHELL_FILE_HEALTH=$C_PKG_DIR/health.sh
C_SHELL_FILE_START=$C_PKG_DIR/start.sh
C_SHELL_FILE_STOP=$C_PKG_DIR/stop.sh
C_BAK_SHELL_FILE_START=$C_SHELL_FILE_START.bak
C_BAK_SHELL_FILE_STOP=$C_SHELL_FILE_STOP.bak

# 安装服务
install_service()
{
    /bin/cp -rf $C_PKG_DIR/$C_NAME.service /usr/lib/systemd/system/$C_NAME.service
    sed -i "s|#$C_HOME|$C_TAR_ROOT|g" /usr/lib/systemd/system/$C_NAME.service
    systemctl daemon-reload
    systemctl enable $C_NAME
}

# 设置环境变量
set_env()
{
    C_RC=$1
    if [ -f $C_RC ]; then
        sed -i "\# ais-server $C_NAME#d" $C_RC
        sed -i "\#^export $C_HOME=$C_TAR_ROOT#d" $C_RC
        if [ $C_IS_SDK == 'true' ]; then
            sed -i "\#^export PATH=\$$C_HOME#d" $C_RC
        fi
        sed -i "\# ais-server $C_NAME end#d" $C_RC
        echo "# ais-server $C_NAME" >> $C_RC
        echo "export $C_HOME=$C_TAR_ROOT" >> $C_RC
        if [ $C_IS_SDK == 'true' ]; then
            echo "export PATH=\$$C_HOME/bin:\$PATH" >> $C_RC
        fi
        echo "# ais-server $C_NAME end" >> $C_RC
    fi
}

# 安装
install()
{
    if [ -d $C_TAR_ROOT ]; then
        rm -rf $C_TAR_ROOT
    fi
    /bin/cp -rf $C_ORIGIN_PATH $C_TAR_ROOT
    if [ $? -ne 0 ]; then
        echo "copy $C_NAME failed"
        exit 1
    fi
    set_env $C_BASHRC
    # 加载$C_BASHRC
    . $C_BASHRC
    if [ -f $C_SHELL_FILE_HEALTH ]; then
        chmod u+x $C_SHELL_FILE_HEALTH
        mv $C_SHELL_FILE_HEALTH $C_TAR_ROOT
    fi
    if [ -f $C_BAK_SHELL_FILE_START ]; then
        /bin/cp -rf $C_BAK_SHELL_FILE_START $C_SHELL_FILE_START
        sed -i "s|#$C_HOME|$C_TAR_ROOT|g" $C_SHELL_FILE_START
        chmod u+x $C_SHELL_FILE_START
        mv $C_SHELL_FILE_START $C_TAR_ROOT
    fi
    if [ -f $C_BAK_SHELL_FILE_STOP ]; then
        /bin/cp -rf $C_BAK_SHELL_FILE_STOP $C_SHELL_FILE_STOP
        sed -i "s|#$C_HOME|$C_TAR_ROOT|g" $C_SHELL_FILE_STOP
        chmod u+x $C_SHELL_FILE_STOP
        mv $C_SHELL_FILE_STOP $C_TAR_ROOT
    fi
    if [ $C_IS_SDK == 'false' ]; then
        install_service
    fi
}

install