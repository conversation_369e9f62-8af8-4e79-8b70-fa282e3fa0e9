import React, { useEffect, useState } from 'react';
import { 
  Card, 
  Message
} from '@arco-design/web-react';

import api from '@/utils/api';
import consts from '@/const';
import useLocale from '@/utils/useLocale';
import useStorage from '@/utils/useStorage';

import SoftwareForm from './software';
import locale from './locale';

function UserInfo() {
  const t = useLocale(locale);
  const [name, setName] = useStorage(consts.KEY_AIS_SERVER_NAME);
  const [host, setHost, removeHost] = useStorage(consts.KEY_AIS_SERVER_PARENT_HOST);
  const [ssl, setSSL, removeSSL] = useStorage(consts.KEY_AIS_SERVER_PARENT_SSL);
  const [, setServerUrl, removeServerUrl] = useStorage(consts.KEY_AIS_SERVER_PARENT_API_SERVER);
  const [loading, setLoading] = useState(false);

  const [data, setData] = useState({
    name: name,
    host: host,
    ssl: ssl === 'true',
    cacheDir: '',
    storageDir: '',
    version: '1.0.3',
  });

  const saveData = (newData) => {
    setData({
      ...data,
      ...newData,
    });
    setName(newData.name);
    if (newData.host.length > 0) {
      setHost(newData.host);
      setSSL(newData.ssl);
      setServerUrl(`http${newData.ssl ? 's' : ''}://${newData.host}:5888`);
    } else {
      removeHost();
      removeSSL();
      removeServerUrl();
    }
    api('/api/v1/clients/current').then((res) => {
      const resData = res && res.data;
      if (resData.data && resData.data.id) {
        resData.data.name = newData.name || resData.data.id;
        resData.data.parentHost = newData.host || 'localhost';
        resData.data.parentSSL = newData.ssl === 'true' ? 1 : 0;
        resData.data.parentGrpcPort = 5555;
        resData.data.parentGatewayPort = 5888;
        const isServer = resData.data.parentHost === '' || resData.data.parentHost === 'localhost' || resData.data.parentHost === '127.0.0.1';
        if (isServer) {
          resData.data.parentId = resData.data.id;
          api('/api/v1/clients/current', { method: 'post', data: resData.data }).then(() => {
            window.location.reload();
          })
          .catch(() => {
            Message.error(t['softwareSetting.operations.save.fail']);
          });
        } else {
          const serverUrl = `http${resData.data.parentSSL ? 's' : ''}://${resData.data.parentHost}:${resData.data.parentGatewayPort}`;
          api('/api/v1/clients/current', { serverUrl: serverUrl }).then((res) => {
            const parent = res && res.data && res.data.data;
            if (parent) {
              resData.data.parentId = parent.id;
              resData.data.parentGrpcPort = parent.grpcPort;
              resData.data.parentGatewayPort = parent.gatewayPort;
            }
            api('/api/v1/clients/current', { method: 'post', data: resData.data }).then(() => {
              window.location.reload();
            })
            .catch(() => {
              Message.error(t['softwareSetting.operations.save.fail']);
            });
          })
          .catch(() => {
            Message.error(t['softwareSetting.operations.save.fail']);
          });
        }
      }
    });
  };

  useEffect(() => {
    setLoading(true);
    api('/api/v1/clients/current')
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data) {
          return;
        }
        const { name, parentHost, parentSsl } = resData.data;
        setLoading(true);
        api('/api/v1/config')
        .then((res) => {
          const resData = res && res.data;
          if (!resData && !resData.data) {
            return;
          }
          const { cacheDir, storageDir, version } = resData.data;
          setData({
            ...data,
            name: name,
            host: parentHost || host,
            ssl: parentSsl === 1 || ssl === 'true',
            cacheDir: cacheDir,
            storageDir: storageDir,
            version: version,
          });
        })
        .finally(() => {
          setLoading(false);
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);
  return (
    <div>
      <Card style={{ marginTop: '16px' }}>
        <SoftwareForm loading={loading} data={data} callback={saveData} />
      </Card>
    </div>
  );
}

export default UserInfo;
