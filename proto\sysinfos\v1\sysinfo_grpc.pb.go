// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: sysinfos/v1/sysinfo.proto

package sysinfosv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SysinfoService_GetSysinfo_FullMethodName = "/sysinfos.v1.SysinfoService/GetSysinfo"
)

// SysinfoServiceClient is the client API for SysinfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SysinfoServiceClient interface {
	GetSysinfo(ctx context.Context, in *GetSysinfoRequest, opts ...grpc.CallOption) (*GetSysinfoResponse, error)
}

type sysinfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSysinfoServiceClient(cc grpc.ClientConnInterface) SysinfoServiceClient {
	return &sysinfoServiceClient{cc}
}

func (c *sysinfoServiceClient) GetSysinfo(ctx context.Context, in *GetSysinfoRequest, opts ...grpc.CallOption) (*GetSysinfoResponse, error) {
	out := new(GetSysinfoResponse)
	err := c.cc.Invoke(ctx, SysinfoService_GetSysinfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SysinfoServiceServer is the server API for SysinfoService service.
// All implementations should embed UnimplementedSysinfoServiceServer
// for forward compatibility
type SysinfoServiceServer interface {
	GetSysinfo(context.Context, *GetSysinfoRequest) (*GetSysinfoResponse, error)
}

// UnimplementedSysinfoServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSysinfoServiceServer struct {
}

func (UnimplementedSysinfoServiceServer) GetSysinfo(context.Context, *GetSysinfoRequest) (*GetSysinfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSysinfo not implemented")
}

// UnsafeSysinfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SysinfoServiceServer will
// result in compilation errors.
type UnsafeSysinfoServiceServer interface {
	mustEmbedUnimplementedSysinfoServiceServer()
}

func RegisterSysinfoServiceServer(s grpc.ServiceRegistrar, srv SysinfoServiceServer) {
	s.RegisterService(&SysinfoService_ServiceDesc, srv)
}

func _SysinfoService_GetSysinfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSysinfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SysinfoServiceServer).GetSysinfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SysinfoService_GetSysinfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SysinfoServiceServer).GetSysinfo(ctx, req.(*GetSysinfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SysinfoService_ServiceDesc is the grpc.ServiceDesc for SysinfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SysinfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sysinfos.v1.SysinfoService",
	HandlerType: (*SysinfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSysinfo",
			Handler:    _SysinfoService_GetSysinfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sysinfos/v1/sysinfo.proto",
}
