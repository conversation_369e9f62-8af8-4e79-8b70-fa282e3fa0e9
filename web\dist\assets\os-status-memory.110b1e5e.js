import{u as e,b as o,j as a,s as m,T as l}from"./index.fe31dd41.js";import{l as r,D as s}from"./index.9905d9df.js";import{C as t}from"./index.41001b26.js";function i(i){const n=e(r),{data:d}=i,u=[{label:n["os.memory.total"],value:d.memory.total},{label:n["os.memory.free"],value:d.memory.free},{label:n["os.memory.used"],value:d.memory.used},{label:n["os.memory.usage"],value:d.memory.usage}];return o(t,{children:[a(m,{align:"start",children:a(l.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:n["os.memory.title"]})}),a(s,{colon:": ",layout:"horizontal",data:u,column:2})]})}export{i as default};
