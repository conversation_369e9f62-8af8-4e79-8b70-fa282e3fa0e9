import{R as e,r,e as t,j as o,_ as c}from"./index.6227d37e.js";function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,o)}return t}function s(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach((function(r){c(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function a(e,c){var n=r.exports.useContext(t).prefixCls,a=void 0===n?"arco":n,i=e.spin,p=e.className,f=s(s({"aria-hidden":!0,focusable:!1,ref:c},e),{},{className:"".concat(p?p+" ":"").concat(a,"-icon ").concat(a,"-icon-refresh")});return i&&(f.className="".concat(f.className," ").concat(a,"-icon-loading")),delete f.spin,delete f.isIcon,o("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...f,children:o("path",{d:"M38.837 18C36.463 12.136 30.715 8 24 8 15.163 8 8 15.163 8 24s7.163 16 16 16c7.455 0 13.72-5.1 15.496-12M40 8v10H30"})})}var i=e.forwardRef(a);i.defaultProps={isIcon:!0},i.displayName="IconRefresh";var p=i;export{p as I};
