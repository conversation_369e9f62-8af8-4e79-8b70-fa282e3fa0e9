<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d075898b-dbb1-48fa-bdd8-00db20fe0360" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/build/ais-server/config.yml" beforeDir="false" afterPath="$PROJECT_DIR$/build/ais-server/config.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ais-server/windows.install.bat" beforeDir="false" afterPath="$PROJECT_DIR$/build/ais-server/windows.install.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ais-server/windows.uninstall.bat" beforeDir="false" afterPath="$PROJECT_DIR$/build/ais-server/windows.uninstall.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build/ais-server/windows.upgrade.bat" beforeDir="false" afterPath="$PROJECT_DIR$/build/ais-server/windows.upgrade.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/config.go" beforeDir="false" afterPath="$PROJECT_DIR$/config/config.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/domain/package.go" beforeDir="false" afterPath="$PROJECT_DIR$/domain/package.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/domain/scripts/windows/install.bat" beforeDir="false" afterPath="$PROJECT_DIR$/domain/scripts/windows/install.bat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/domain/task.go" beforeDir="false" afterPath="$PROJECT_DIR$/domain/task.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/domain/task_service.go" beforeDir="false" afterPath="$PROJECT_DIR$/domain/task_service.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/domain/task_service.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/domain/task_service.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.mod" beforeDir="false" afterPath="$PROJECT_DIR$/go.mod" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.sum" beforeDir="false" afterPath="$PROJECT_DIR$/go.sum" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/lib.go" beforeDir="false" afterPath="$PROJECT_DIR$/lib/lib.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/clients/v1/client.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/clients/v1/client.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/clients/v1/client.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/clients/v1/client.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/clients/v1/client_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/clients/v1/client_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/configs/v1/config.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/configs/v1/config.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/configs/v1/config.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/configs/v1/config.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/configs/v1/config_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/configs/v1/config_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/logs/v1/log.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/logs/v1/log.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/logs/v1/log.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/logs/v1/log.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/logs/v1/log_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/logs/v1/log_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/packages/v1/package.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/packages/v1/package.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/packages/v1/package.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/packages/v1/package.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/packages/v1/package_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/packages/v1/package_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/patches/v1/patch.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/patches/v1/patch.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/patches/v1/patch.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/patches/v1/patch.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/patches/v1/patch_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/patches/v1/patch_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/programs/v1/program.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/programs/v1/program.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/programs/v1/program.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/programs/v1/program.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/programs/v1/program_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/programs/v1/program_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/sysinfos/v1/sysinfo_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/tasks/v1/task.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/tasks/v1/task.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/tasks/v1/task.pb.gw.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/tasks/v1/task.pb.gw.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/tasks/v1/task.proto" beforeDir="false" afterPath="$PROJECT_DIR$/proto/tasks/v1/task.proto" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/tasks/v1/task_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/tasks/v1/task_grpc.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/clients/v1/client.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/clients/v1/client.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/configs/v1/config.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/configs/v1/config.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/logs/v1/log.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/logs/v1/log.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/packages/v1/package.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/packages/v1/package.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/patches/v1/patch.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/patches/v1/patch.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/programs/v1/program.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/programs/v1/program.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/sysinfos/v1/sysinfo.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/sysinfos/v1/sysinfo.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/third_party/OpenAPI/tasks/v1/task.swagger.json" beforeDir="false" afterPath="$PROJECT_DIR$/third_party/OpenAPI/tasks/v1/task.swagger.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/card-block.20965e26.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/card-block.2cfbfa76.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/card-block.2cfbfa76.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/constants.3633568f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/constants.3987a3f3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/constants.5d2baffb.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/constants.648fceb2.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/constants.648fceb2.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/constants.f5d49b79.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/horizontal.3f3db1ef.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.0a439d0b.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.0c53b62a.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.104235be.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.11415bf1.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.2a59f268.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.3644f225.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.3644f225.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.3f46a0e3.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.3f46a0e3.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.4623c961.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.4623c961.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.47110eb1.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.47110eb1.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.4dd25c7e.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.51e4d611.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.540f60ae.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.5dde1719.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.6072278c.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.60b96728.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.60b96728.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.6d19ce31.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.6d19ce31.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.783032c3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.7a30504c.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.7b5a8ccd.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.7fcec1dd.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.812cc408.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.8f78f1b3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.9381057c.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.967cbe15.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.a5ccee71.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.aab63742.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.aab63742.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.aac81768.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.bb956d71.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.c3da6d1d.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.ce77b0ae.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.d025aaa9.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.d76071bf.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.e7a6af1d.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.e7a6af1d.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.e821f389.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.e821f389.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.ea78f3d5.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.ee7b6abb.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.f06405de.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.f17d8aea.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.f17d8aea.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.fe5a0292.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.fe5a0292.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.module.cf3602d3.js" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.module.cf3602d3.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/index.module.db4eb3f1.css" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/assets/index.module.db4eb3f1.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/logo.f6830a7d.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/os-information.8e491c5f.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/os-network.5d28aed2.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/os-status-cpu.71f36ece.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/os-status-memory.beb3dcb1.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/search-form.02c7ca2b.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/search-form.3cb06c47.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/search-form.947dc5da.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/search-form.f1e55267.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/software.3d41fd30.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/text.4adbee75.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/assets/vertical.1ee6079e.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/web/dist/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/web/dist/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/locale/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/locale/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/pages/main/log/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/pages/main/log/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/pages/main/program/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/pages/main/program/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/pages/main/store-import-form/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/pages/main/store-import-form/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/pages/main/store/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/pages/main/store/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/web/src/routes.ts" beforeDir="false" afterPath="$PROJECT_DIR$/web/src/routes.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;tanwenkai&quot;,
      &quot;fullname&quot;: &quot;谭文开&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://3548922hm3.zicp.vip:10325/product/ais-server.git&quot;,
    &quot;second&quot;: &quot;ed7c60cd-a39b-4b43-9359-492513afed21&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="32Y2PCHPXj5L04Tn8770P9schPT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/gitee_workspace/jinghong-other/ais-server&quot;
  }
}</component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d075898b-dbb1-48fa-bdd8-00db20fe0360" name="Changes" comment="" />
      <created>1757583158486</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757583158486</updated>
    </task>
    <servers />
  </component>
</project>