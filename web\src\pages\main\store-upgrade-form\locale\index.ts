const i18n = {
  'en-US': {
    'form.name.label': 'Name',
    'form.name.required': 'Please enter the name',
    'form.name.placeholder': 'Please enter the name',
    'form.attachment.label': 'Attachment',
    'form.attachment.required': 'Please upload the attachment',
    'form.attachment.support.tips': 'Only zip can be uploaded',
    'form.attachment.support-error.tips':
      'Unacceptable file type, please re-upload the specified file type!',
    'form.upgrade.confirm.title': 'Upgrade confirmation',
    'form.upgrade.confirm.content': 'Are you sure you want to upgrade?',
    'form.upgrade.fail': 'Upgrade failed',
    'form.upgrade.success': 'Upgrade successful',
    'form.upgrade.success.title': 'Upgrade successful',
    'form.upgrade.success.desc': 'Upgrade successful',
    'form.upgrade.success.again': 'Upgrade again',
    'form.operations.back': 'Back',
    'form.operations.submit': 'Submit',
  },
  'zh-CN': {
    'form.name.label': '名称',
    'form.name.required': '请输入名称',
    'form.name.placeholder': '请输入名称',
    'form.attachment.label': '附件',
    'form.attachment.required': '请上传附件',
    'form.attachment.support.tips': '仅支持zip上传',
    'form.attachment.support-error.tips': '不接受的文件类型，请重新上传指定文件类型！',
    'form.upgrade.confirm.title': '升级确认',
    'form.upgrade.confirm.content': '是否确认升级？',
    'form.upgrade.fail': '升级失败',
    'form.upgrade.success': '升级成功',
    'form.upgrade.success.title': '升级成功',
    'form.upgrade.success.desc': '升级成功',
    'form.upgrade.success.again': '再次升级',
    'form.operations.back': '返回',
    'form.operations.submit': '提交',
  },
};

export default i18n;
