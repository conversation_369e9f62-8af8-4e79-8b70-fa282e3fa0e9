import{u as r,j as e,b as t,B as i,T as a,aN as l,aO as o}from"./index.e8bac691.js";import{R as s}from"./index.d67f20ea.js";import"./index.4d436df5.js";const n={"en-US":{"menu.result":"Result","menu.result.error":"Error","error.result.title":"Submit Error","error.result.subTitle":"Please check the modified information and try again","error.result.goBack":"GoBack","error.result.retry":"Retry","error.detailTitle":"Details of Error","error.detailLine.record":"The current domain name has not been registered, please check the registration process: ","error.detailLine.record.link":"Registration Process","error.detailLine.auth":"Your user group does not have the authority to perform this operation;","error.detailLine.auth.link":"Request for access"},"zh-CN":{"menu.result":"结果页","menu.result.error":"失败页","error.result.title":"提交失败","error.result.subTitle":"请核对修改信息后，再重试","error.result.goBack":"回到首页","error.result.retry":"返回修改","error.detailTitle":"错误详情","error.detailLine.record":"当前域名未备案，备案流程请查看：","error.detailLine.record.link":"备案流程","error.detailLine.auth":"你的用户组不具有进行此操作的权限；","error.detailLine.auth.link":"申请权限"}};var d="_wrapper_i1abw_1",u="_result_i1abw_7",c="_details-wrapper_i1abw_10";function h(){const h=r(n);return e("div",{children:t("div",{className:d,children:[e(s,{className:u,status:"error",title:h["error.result.title"],subTitle:h["error.result.subTitle"],extra:[e(i,{type:"secondary",style:{marginRight:16},children:h["error.result.goBack"]},"again"),e(i,{type:"primary",children:h["error.result.retry"]},"back")]}),t("div",{className:c,children:[e(a.Title,{heading:6,style:{marginTop:0},children:h["error.detailTitle"]}),e(a.Paragraph,{style:{marginBottom:0},children:t("ol",{children:[t("li",{children:[h["error.detailLine.record"],t(l,{children:[e(o,{}),h["error.detailLine.record.link"]]})]}),t("li",{children:[h["error.detailLine.auth"],e(l,{children:h["error.detailLine.auth.link"]})]})]})})]})]})})}export{h as default};
