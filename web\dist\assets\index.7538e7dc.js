import{R as e,r as t,e as r,j as o,_ as a,an as n,ao as s,F as i,ap as c,u as l,b as p,s as d,aq as m,B as u,k as f,l as b,i as h,ar as g}from"./index.fe31dd41.js";import{C as O}from"./index.41001b26.js";import{T as y}from"./index.a7c49f62.js";import{i as j,S as v,s as x}from"./search-form.f9b9e19d.js";import{getColumns as P}from"./constants.8ce476b0.js";import"./index.64abbf40.js";function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function S(e,a){var n=t.exports.useContext(r).prefixCls,s=void 0===n?"arco":n,i=e.spin,c=e.className,l=k(k({"aria-hidden":!0,focusable:!1,ref:a},e),{},{className:"".concat(c?c+" ":"").concat(s,"-icon ").concat(s,"-icon-import")});return i&&(l.className="".concat(l.className," ").concat(s,"-icon-loading")),delete l.spin,delete l.isIcon,o("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:o("path",{d:"m27.929 33.072-9.071-9.07 9.07-9.072M43 24H19m12 17H7V7h24"})})}var C=e.forwardRef(S);C.defaultProps={isIcon:!0},C.displayName="IconImport";var z=C;function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function I(e,a){var n=t.exports.useContext(r).prefixCls,s=void 0===n?"arco":n,i=e.spin,c=e.className,l=D(D({"aria-hidden":!0,focusable:!1,ref:a},e),{},{className:"".concat(c?c+" ":"").concat(s,"-icon ").concat(s,"-icon-folder")});return i&&(l.className="".concat(l.className," ").concat(s,"-icon-loading")),delete l.spin,delete l.isIcon,o("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:o("path",{d:"M6 13h18l-2.527-3.557a1.077 1.077 0 0 0-.88-.443H7.06C6.474 9 6 9.448 6 10v3Zm0 0h33.882c1.17 0 2.118.895 2.118 2v21c0 1.105-.948 3-2.118 3H8.118C6.948 39 6 38.105 6 37V13Z"})})}var $=e.forwardRef(I);$.defaultProps={isIcon:!0},$.displayName="IconFolder";var E=$;const q=e=>{const{backup:r,requiredPermissions:a,oneOfPerm:c}=e,l=n((e=>e.userInfo));return t.exports.useMemo((()=>s({requiredPermissions:a,oneOfPerm:c},l.permissions)),[c,a,l.permissions])?o(i,{children:H(e.children)}):r?o(i,{children:H(r)}):null};function H(t){return e.isValidElement(t)?t:o(i,{children:t})}function M(){const e=c(),r=l(j),a=async(t,a)=>{switch(a){case"upgrade":e.push({pathname:"/main/store-upgrade-form",state:t});break;case"update":e.push({pathname:"/main/store-update-form",state:t});break;case"delete":f.confirm({title:r["store.tables.main.modal.delete.title"],content:r["store.tables.main.modal.delete.content"],onOk:()=>{C(!0),b(`/api/v1/packages/${t.id}`,{method:"delete"}).then((()=>{h.success(r["store.tables.main.modal.delete.operation.success"]),I()})).catch((()=>{h.error(r["store.tables.main.modal.delete.operation.fail"])})).finally((()=>{C(!1)}))}});break;default:f.confirm({title:r[`store.tables.main.modal.${a}.title`],content:r[`store.tables.main.modal.${a}.content`],onOk:()=>{C(!0),b("/api/v1/tasks",{method:"post",data:{name:a,packageId:t.id},timeout:60*("export"===a?5:1)*1e3}).then((e=>{const t=e&&e.data;if(t||t.data){if("failed"===t.data.state)h.error(r[`store.tables.main.modal.${a}.operation.fail`]);else{if("export"===a){const e=`${Date.now()}`,a=t.data.content;g.info({id:e,title:r["store.notifications.export.title"],content:`${r["store.notifications.export.location"]}: ${a}`,duration:0,btn:p("span",{children:[o(u,{type:"secondary",size:"small",onClick:()=>g.remove(e),style:{margin:"0 12px"},children:r["store.const.cancel"]}),o(u,{type:"primary",size:"small",onClick:()=>g.remove(e),children:r["store.const.ok"]})]})})}h.success(r[`store.tables.main.modal.${a}.operation.success`])}I()}})).catch((()=>{h.error(r[`store.tables.main.modal.${a}.operation.fail`])})).finally((()=>{C(!1)}))}})}},n=t.exports.useMemo((()=>P(r,a)),[r]),[s,i]=t.exports.useState([]),[w,k]=t.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[S,C]=t.exports.useState(!0),[N,D]=t.exports.useState({});function I(){const{current:e,pageSize:t}=w;C(!0),b("/api/v1/packages",{data:{page:e,size:t,...N}}).then((r=>{const o=r&&r.data;(o||o.data||o.data.content)&&(i(o.data.content),k({...w,current:e,pageSize:t,total:o.data.total}))})).finally((()=>{C(!1)}))}return t.exports.useEffect((()=>{I()}),[w.current,w.pageSize,JSON.stringify(N)]),p(O,{children:[o(v,{loading:S,onSearch:function(e){k({...w,current:1}),D(e)}}),o(q,{requiredPermissions:[{resource:"menu.main.store",actions:["write"]}],children:o("div",{className:x["button-group"],children:p(d,{children:[o(m,{to:"/main/store-import-form",children:o(u,{loading:S,type:"primary",icon:o(z,{}),children:r["store.tables.main.operation.import"]})}),o(m,{to:"/main/store-local-import",children:o(u,{loading:S,type:"outline",icon:o(E,{}),children:"本地导入"})})]})})}),o(y,{rowKey:"id",loading:S,onChange:function({current:e,pageSize:t}){k({...w,current:e,pageSize:t})},pagination:w,columns:n,data:s})]})}export{M as default};
