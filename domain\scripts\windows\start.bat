@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1", "", "runas", 1 >> "%temp%\getadmin.vbs"

    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

set "C_NAME=%~1"
if "%C_NAME%"=="" (
    echo Usage: %0 name
    exit /B 1
)

net start | find "%C_NAME%" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Service %C_NAME% is already running.
    exit /B 0
)
wmic service get name | find "%C_NAME%" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    sc start "%C_NAME%"
    exit /B 0
)