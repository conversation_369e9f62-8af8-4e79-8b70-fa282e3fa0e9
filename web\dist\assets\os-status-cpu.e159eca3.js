import{u as a,b as o,j as s,s as e,T as t}from"./index.e8bac691.js";import{l,D as r}from"./index.b8bf3dbf.js";import{C as i}from"./index.a97db34b.js";function n(n){const c=a(l),{data:u}=n,d=[{label:c["os.cpu.cores"],value:u.cpu.cores},{label:c["os.cpu.usage"],value:u.cpu.usage}];return o(i,{children:[s(e,{align:"start",children:s(t.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:c["os.cpu.title"]})}),s(r,{colon:": ",layout:"horizontal",data:d,column:2})]})}export{n as default};
