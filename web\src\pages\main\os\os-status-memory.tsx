import React from 'react';
import {
  Card,
  Typography,
  // Tag,
  Space,
  Descriptions,
} from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import locale from './locale';

interface OSStatusProps {
  data: {
    memory?: {
      total: string;
      free: string;
      used: string;
      usage: string;
    };
  };
}

export default function OSInformation(props: OSStatusProps) {
  const t = useLocale(locale);
  const { data } = props;
  const dataMemoryStatus = [
    {
      label: t['os.memory.total'],
      value: data.memory.total,
    },
    {
      label: t['os.memory.free'],
      value: data.memory.free,
    },
    {
      label: t['os.memory.used'],
      value: data.memory.used,
    },
    {
      label: t['os.memory.usage'],
      value: data.memory.usage,
    }
  ];

  return (
    <Card>
      <Space align="start">
        <Typography.Title
          style={{ marginTop: 0, marginBottom: 16 }}
          heading={6}
        >
          {t['os.memory.title']}
        </Typography.Title>
      </Space>
      <Descriptions
        colon=": "
        layout="horizontal"
        data={dataMemoryStatus}
        column={2}
      />
    </Card>
  );
}
