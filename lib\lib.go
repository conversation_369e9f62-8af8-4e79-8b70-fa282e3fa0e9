package lib

import (
	"archive/zip"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

func ByteCountBinary(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := int64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %ciB", float64(b)/float64(div), "KMGTPE"[exp])
}

func Copy(src, dst string) error {
	info, err := os.Stat(src)
	if err != nil {
		return err
	}

	if info.IsDir() {
		return CopyDir(src, dst)
	}

	return CopyFile(src, dst)
}

func CopyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return err
	}

	return dstFile.Sync()
}

func CopyDir(src, dst string) error {
	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	err = os.MkdirAll(dst, 0755)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			err = CopyDir(srcPath, dstPath)
			if err != nil {
				return err
			}
		} else {
			err = CopyFile(srcPath, dstPath)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func NewUUID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

func CheckServerConnection(addr string) error {
	u, err := url.Parse(addr)
	if err != nil {
		return err
	}
	schema := u.Scheme
	host := u.Host
	port := u.Port()
	var address string
	if port == "" {
		address = host + ":" + schema
	} else {
		address = host
	}
	_, err = net.DialTimeout("tcp", address, 3*time.Second)
	if err != nil {
		return err
	}
	return nil
}

func CheckServer(addr string, timeout time.Duration) error {
	u, err := url.Parse(addr)
	if err != nil {
		return err
	}
	schema := u.Scheme
	host := u.Host
	port := u.Port()
	var address string
	if port == "" {
		address = host + ":" + schema
	} else {
		address = host
	}
	_, err = net.DialTimeout("tcp", address, timeout)
	if err != nil {
		return err
	}
	return nil
}

func NewGPRCServerConnection(addr string, block bool) (*grpc.ClientConn, error) {
	var conn *grpc.ClientConn
	var err error
	var tlsConfig = &tls.Config{
		InsecureSkipVerify: true,
	}
	creds := credentials.NewTLS(tlsConfig)
	if block {
		conn, err = grpc.Dial(addr, grpc.WithTransportCredentials(creds), grpc.WithBlock())
	} else {
		conn, err = grpc.Dial(addr, grpc.WithTransportCredentials(creds))
	}
	if err != nil {
		return nil, err
	}
	return conn, nil
}

func ExecuteScriptWithDir(cmdStr string, dir string) (string, error) {
	// 将 command 分割成一个字符串切片
	args := strings.Fields(cmdStr)
	// 将 args 传递给 exec.Command
	cmd := exec.Command(args[0], args[1:]...)
	cmd.Dir = dir
	cmd.Env = os.Environ()
	output, err := cmd.Output()
	if err != nil {
		return string(output), err
	} else {
		outputStr := string(output)
		return outputStr, nil
	}
}

func ExecuteScript(cmdStr string) (string, error) {
	// 将 command 分割成一个字符串切片
	args := strings.Fields(cmdStr)
	// 将 args 传递给 exec.Command
	cmd := exec.Command(args[0], args[1:]...)
	cmd.Env = os.Environ()
	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), err
	} else {
		outputStr := string(output)
		return outputStr, nil
	}
}

func Unzip(src, dir string) error {
	if src == "" {
		return fmt.Errorf("location is empty")
	}
	if _, err := os.Stat(src); os.IsNotExist(err) {
		return fmt.Errorf("file %s not exists", src)
	}
	r, err := zip.OpenReader(src)
	if err != nil {
		return fmt.Errorf("failed to extract %s, %s", src, err.Error())
	}
	defer r.Close()
	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return fmt.Errorf("failed to extract %s, %s", src, err.Error())
		}
		
		path := filepath.Join(dir, f.Name)
		if f.FileInfo().IsDir() {
			os.MkdirAll(path, f.Mode())
			rc.Close() // 立即关闭目录的文件句柄
		} else {
			os.MkdirAll(filepath.Dir(path), f.Mode())
			outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				rc.Close() // 确保在错误时关闭文件句柄
				return fmt.Errorf("failed to extract %s, %s", src, err.Error())
			}
			
			_, err = io.Copy(outFile, rc)
			outFile.Close() // 立即关闭输出文件
			rc.Close()      // 立即关闭输入文件句柄
			
			if err != nil {
				return fmt.Errorf("failed to extract %s, %s", src, err.Error())
			}
		}
	}
	return nil
}

// UnzipOptimized 优化的标准库解压缩，使用并发和大缓冲区
func UnzipOptimized(src, dir string, progressCallback func(float64, string)) error {
	if src == "" {
		return fmt.Errorf("source file path is empty")
	}
	if _, err := os.Stat(src); os.IsNotExist(err) {
		return fmt.Errorf("file %s not exists", src)
	}

	// 确保目标目录存在
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %v", dir, err)
	}

	// 打开ZIP文件
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	// 计算总大小用于进度计算
	totalSize := int64(0)
	for _, f := range r.File {
		totalSize += int64(f.UncompressedSize64)
	}

	// 使用并发解压，限制并发数
	maxWorkers := 4 // 设置合理的并发数
	
	workChan := make(chan *zip.File, len(r.File))
	errorChan := make(chan error, len(r.File))
	progressChan := make(chan int64, len(r.File))
	
	// 启动工作协程
	for i := 0; i < maxWorkers; i++ {
		go func() {
			for f := range workChan {
				err := extractFileOptimized(f, dir)
				if err != nil {
					errorChan <- err
					return
				}
				progressChan <- int64(f.UncompressedSize64)
			}
		}()
	}
	
	// 进度监控协程
	var processedSize int64
	progressDone := make(chan bool)
	go func() {
		for size := range progressChan {
			processedSize += size
			progress := float64(processedSize) / float64(totalSize) * 100
			if progressCallback != nil {
				progressCallback(progress, fmt.Sprintf("已解压: %s / %s", 
					ByteCountBinary(uint64(processedSize)), 
					ByteCountBinary(uint64(totalSize))))
			}
		}
		progressDone <- true
	}()
	
	// 发送任务到工作队列
	go func() {
		for _, f := range r.File {
			workChan <- f
		}
		close(workChan)
	}()
	
	// 等待所有文件处理完成
	processedFiles := 0
	for processedFiles < len(r.File) {
		select {
		case err := <-errorChan:
			return err
		case <-progressChan:
			processedFiles++
		}
	}
	
	close(progressChan)
	<-progressDone
	
	if progressCallback != nil {
		progressCallback(100, "解压完成")
	}
	
	return nil
}

// extractFileOptimized 优化的单文件解压函数
func extractFileOptimized(f *zip.File, destDir string) error {
	// 构建目标路径
	path := filepath.Join(destDir, f.Name)
	
	// 安全检查，防止路径遍历攻击
	if !strings.HasPrefix(path, filepath.Clean(destDir)+string(os.PathSeparator)) {
		return fmt.Errorf("invalid file path: %s", f.Name)
	}
	
	// 如果是目录，创建目录
	if f.FileInfo().IsDir() {
		return os.MkdirAll(path, f.FileInfo().Mode())
	}
	
	// 确保父目录存在
	if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
		return err
	}
	
	// 打开ZIP文件中的文件
	rc, err := f.Open()
	if err != nil {
		return err
	}
	defer rc.Close()
	
	// 创建目标文件
	outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.FileInfo().Mode())
	if err != nil {
		return err
	}
	defer outFile.Close()
	
	// 使用大缓冲区复制文件
	bufferSize := 64 * 1024 // 64KB缓冲区
	buf := make([]byte, bufferSize)
	
	for {
		n, err := rc.Read(buf)
		if err != nil && err != io.EOF {
			return err
		}
		if n == 0 {
			break
		}
		
		_, err = outFile.Write(buf[:n])
		if err != nil {
			return err
		}
	}
	
	return nil
}

// 获取指定目录下的所有目录（是否需要递归）
func ListDirs(dir string, recursive bool) ([]string, error) {
	var dirs []string
	// 检查 dir 是否存在
	if _, err := os.Stat(dir); err == nil {
		// 使用 filepath.Walk 遍历目录
		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err // 无法访问文件或目录的错误
			}
			if info.IsDir() {
				dirs = append(dirs, path) // 添加目录路径
				if !recursive && path != dir {
					return filepath.SkipDir // 跳过子目录
				}
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	} else if os.IsNotExist(err) {
		return nil, fmt.Errorf("dir %s not found", dir)
	} else {
		return nil, err
	}
	return dirs, nil
}

// 获取指定目录下的所有文件
func ListFiles(dir string) ([]string, error) {
	var files []string
	// 检查 dir 是否存在
	if _, err := os.Stat(dir); err == nil {
		// 使用 filepath.Walk 遍历目录
		err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err // 无法访问文件或目录的错误
			}
			if !info.IsDir() {
				files = append(files, path) // 添加文件路径
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	} else if os.IsNotExist(err) {
		return nil, fmt.Errorf("dir %s not found", dir)
	} else {
		return nil, err
	}
	return files, nil
}

// 将结构体转换为 JSON 字符串
func ToJSON(v interface{}) string {
	b, err := json.Marshal(v)
	if err != nil {
		slog.Error("Failed to convert struct to JSON: " + err.Error())
		return ""
	}
	return string(b)
}

// 将 JSON 字符串转换为结构体
func FromJSON(jsonStr string, v interface{}) error {
	err := json.Unmarshal([]byte(jsonStr), v)
	if err != nil {
		return err
	}
	return nil
}

// UnzipAuto 自动选择解压方式
func UnzipAuto(src, dir string) error {
	// 简化实现，直接使用优化解压
	return UnzipOptimized(src, dir, nil)
}

// UnzipWithProgress 带进度回调的解压函数
func UnzipWithProgress(src, dir string, progressCallback func(float64, string)) error {
	return UnzipOptimized(src, dir, progressCallback)
}
