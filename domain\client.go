package domain

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	clientsv1 "gitlab.jhonginfo.com/product/ais-server/proto/clients/v1"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
)

type MqttClient struct {
	Origin        mqtt.Client
	ServerAddress string
}
type ClientAction struct {
	From   string  `json:"from"`
	To     string  `json:"to"`
	Data   string  `json:"data"`
	Type   string  `json:"type"`
	Result *string `json:"result"`
}

var ClientActionTypes = struct {
	Backup    string
	Reload    string
	Sync      string
	Start     string
	Stop      string
	Restart   string
	Update    string
	Upgrade   string
	Install   string
	Uninstall string
}{
	Backup:    "backup",
	Reload:    "reload",
	Sync:      "sync",
	Start:     "start",
	Stop:      "stop",
	Restart:   "restart",
	Update:    "update",
	Upgrade:   "upgrade",
	Install:   "install",
	Uninstall: "uninstall",
}

var ClientState = struct {
	Online  string
	Offline string
}{
	Online:  "online",
	Offline: "offline",
}

type Client struct {
	Id                string    `json:"id" gorm:"primary_key"`
	Name              string    `json:"name"`
	GrpcPort          int32     `json:"grpc_port"`
	GatewayPort       int32     `json:"gateway_port"`
	Ssl               int32     `json:"ssl"`
	ParentId          string    `json:"parent_id"`
	ParentHost        string    `json:"parent_host"`
	ParentGrpcPort    int32     `json:"parent_grpc_port"`
	ParentGatewayPort int32     `json:"parent_gateway_port"`
	ParentMqttTcpPort int32     `json:"parent_mqtt_tcp_port"`
	ParentMqttWsPort  int32     `json:"parent_mqtt_ws_port"`
	ParentSsl         int32     `json:"parent_ssl"`
	OsType            string    `json:"os_type"`
	Networks          string    `json:"networks"`
	Uptime            string    `json:"uptime"`
	Version           string    `json:"version"`
	State             string    `json:"state"`
	CreatedAt         time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`
	IsLocal           int32     `json:"is_local"`

	Mqtt *MqttClient `json:"-" gorm:"-"`
}

func (t *Client) IsConnected() bool {
	if t.Mqtt == nil || t.Mqtt.Origin == nil {
		return false
	}
	return t.Mqtt.Origin.IsConnected()
}

func (c *Client) GetParentGrpcServer() string {
	if c.ParentHost == "" {
		return ""
	}
	return fmt.Sprintf("%s:%d", c.ParentHost, c.ParentGrpcPort)
}

func (c *Client) GetParentApiServer() string {
	if c.ParentHost == "" {
		return ""
	}
	protocol := "http"
	isSSL := c.ParentSsl == 1
	if isSSL {
		protocol = "https"
	}
	return fmt.Sprintf("%s://%s:%d", protocol, c.ParentHost, c.ParentGatewayPort)
}

func (c *Client) GetParentMQTTServer() string {
	if c.ParentHost == "" {
		return ""
	}
	protocol := "tcp"
	isSSL := c.ParentSsl == 1
	if isSSL {
		protocol = "tls"
	}
	return fmt.Sprintf("%s://%s:%d", protocol, c.ParentHost, c.ParentMqttTcpPort)
}

func (t *Client) Connect() error {
	if t.IsConnected() {
		return nil
	}
	if t.Id == "" {
		return fmt.Errorf("id or server address is empty")
	}
	addr := t.GetParentMQTTServer()
	if t.Mqtt == nil {
		t.Mqtt = &MqttClient{
			ServerAddress: addr,
		}
	}
	opts := mqtt.NewClientOptions()
	opts.SetClientID(t.Id)
	opts.AddBroker(t.GetParentMQTTServer())
	opts.OnConnect = func(client mqtt.Client) {
		slog.Info(fmt.Sprintf("Client(%s) connected to server(%s)", t.Id, t.Mqtt.ServerAddress))
		info, _ := json.Marshal(t)
		clientHeartbeatTopic := strings.Replace(TOPIC_X_CLIENT_HEARTBEAT, "*", t.Id, 1)
		t.Publish(clientHeartbeatTopic, 0, false, info)
		clientCmdTopic := strings.Replace(TOPIC_X_CLIENT_CMD, "*", t.Id, 1)
		clientCallTopic := strings.Replace(TOPIC_X_CLIENT_CALL, "*", t.Id, 1)
		t.Subscribe(clientCmdTopic, 2, func(topic string, payload []byte) {
			slog.Info("Receive message from topic[" + topic + "]: " + string(payload))
			result := "fail"
			act := &ClientAction{}
			err := json.Unmarshal(payload, act)
			if err != nil {
				slog.Error("Unmarshal err: " + err.Error())
				return
			}
			if t.Execute(act) {
				result = "success"
			}
			if act.From != "" {
				clientMessageTopic := strings.Replace(TOPIC_X_CLIENT_MESSAGE, "*", act.From, 1)
				t.Publish(clientMessageTopic, 0, false, []byte(result))
			}
			act.Result = &result
			reply, _ := json.Marshal(act)
			t.Publish(clientCallTopic, 0, false, reply)
		})
	}
	opts.OnConnectionLost = func(client mqtt.Client, err error) {
		slog.Error(fmt.Sprintf("Client(%s) connection lost: %s", t.Id, err.Error()))
	}
	opts.OnReconnecting = func(client mqtt.Client, options *mqtt.ClientOptions) {
		slog.Warn(fmt.Sprintf("Client(%s) reconnecting to server(%s)", t.Id, t.Mqtt.ServerAddress))
	}
	// 创建 MQTT 客户端
	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.WaitTimeout(MQTT_WAIT_TIMEOUT) && token.Error() != nil {
		return token.Error()
	}
	t.Mqtt.Origin = client
	slog.Info(fmt.Sprintf("Client(%s) connect to server(%s)", t.Id, t.Mqtt.ServerAddress))
	return nil
}

func (t *Client) Disconnect() {
	if t.IsConnected() {
		t.Mqtt.Origin.Disconnect(250)
		slog.Info(fmt.Sprintf("Client(%s) disconnect from server(%s)", t.Id, t.Mqtt.ServerAddress))
	}
}

func (t *Client) Reconnect() error {
	t.Disconnect()
	if !t.IsConnected() {
		return t.Connect()
	}
	return nil
}

func (t *Client) Publish(topic string, qos byte, retained bool, payload interface{}) error {
	if !t.IsConnected() {
		err := t.Connect()
		if err != nil {
			return err
		}
	}
	if token := t.Mqtt.Origin.Publish(topic, qos, retained, payload); token.WaitTimeout(MQTT_WAIT_TIMEOUT) && token.Error() != nil {
		err := token.Error()
		slog.Error(fmt.Sprintf("Client(%s) publish message to topic(%s) error: %s", t.Id, topic, err.Error()))
		return err
	}
	slog.Debug(fmt.Sprintf("Client(%s) publish message to topic(%s): %v", t.Id, topic, payload))
	return nil
}

func (t *Client) Subscribe(topic string, qos byte, handle func(topic string, payload []byte)) error {
	if !t.IsConnected() {
		err := t.Connect()
		if err != nil {
			return err
		}
	}
	if token := t.Mqtt.Origin.Subscribe(topic, qos, func(cl mqtt.Client, msg mqtt.Message) {
		handle(msg.Topic(), msg.Payload())
	}); token.WaitTimeout(MQTT_WAIT_TIMEOUT) && token.Error() != nil {
		err := token.Error()
		slog.Error(fmt.Sprintf("Client(%s) subscribe topic(%s) error: %s", t.Id, topic, err.Error()))
		return err
	}
	slog.Info(fmt.Sprintf("Client(%s) subscribe topic(%s) successful", t.Id, topic))
	return nil
}

func (t *Client) Unsubscribe(topic string) error {
	if !t.IsConnected() {
		err := t.Connect()
		if err != nil {
			return err
		}
	}
	if token := t.Mqtt.Origin.Unsubscribe(topic); token.WaitTimeout(MQTT_WAIT_TIMEOUT) && token.Error() != nil {
		err := token.Error()
		slog.Error(fmt.Sprintf("Client(%s) unsubscribe topic(%s) error: %v", t.Id, topic, err.Error()))
		return err
	}
	slog.Info(fmt.Sprintf("Client(%s) unsubscribe topic(%s) successful", t.Id, topic))
	return nil
}

func (t *Client) Sync() error {
	slog.Info("Sync packages")
	needSync := t.Id != "" && t.ParentId != "" && t.Id != t.ParentId
	if !needSync {
		slog.Warn("No need to sync")
		return nil
	}
	addr := t.GetParentGrpcServer()
	if addr == "" {
		slog.Error("Parent grpc server is empty")
		return fmt.Errorf("parent grpc server is empty")
	}
	slog.Info("Connecting to " + addr)
	ctx, cancel := context.WithTimeout(context.Background(), GRPC_WAIT_TIMEOUT)
	defer cancel()
	conn, err := lib.NewGPRCServerConnection(addr, false)
	if err != nil {
		slog.Error("Connect err: " + err.Error())
		return err
	}
	defer conn.Close()
	slog.Info("Connected to " + addr)

	client := packagesv1.NewPackageServiceClient(conn)
	resp, err := client.ListPackages(ctx, &packagesv1.ListPackagesRequest{})
	if err != nil {
		slog.Error("List packages err: " + err.Error())
		return err
	}
	if resp.Data != nil && len(resp.Data.Content) > 0 {
		slog.Info(fmt.Sprintf("List packages total: %d", len(resp.Data.Content)))
		newPackages := make([]*Package, 0)
		pos := PackageVOs2PackagePOs(resp.Data.Content)
		for _, p := range pos {
			old := &Package{
				Id: p.Id,
			}
			err := old.GetById()
			if err == nil {
				continue
			}
			p.Downloaded = 0
			p.Installed = 0
			p.Uri = ""
			p.PackagePath = ""
			p.InstallPath = ""
			p.Support = ""
			p.State = PackageStates.Unknown
			newPackages = append(newPackages, p)
			slog.Info(fmt.Sprintf("New package: %s v%s", p.Name, p.Version))
		}
		if len(newPackages) > 0 {
			do := &Package{}
			do.SaveAll(newPackages)
			slog.Info(fmt.Sprintf("Save packages total: %d", len(newPackages)))
		}
	}
	slog.Info("Sync packages successful")
	return nil
}

func (t *Client) Execute(act *ClientAction) bool {
	if act == nil {
		return false
	}
	switch act.Type {
	case ClientActionTypes.Sync:
		err := t.Sync()
		if err != nil {
			slog.Error("Sync err: " + err.Error())
			return false
		}
	}
	return true
}

func (t *Client) ToVO() *clientsv1.Client {
	return &clientsv1.Client{
		Id:                t.Id,
		Name:              t.Name,
		GrpcPort:          t.GrpcPort,
		GatewayPort:       t.GatewayPort,
		Ssl:               t.Ssl,
		ParentId:          t.ParentId,
		ParentHost:        t.ParentHost,
		ParentGrpcPort:    t.ParentGrpcPort,
		ParentGatewayPort: t.ParentGatewayPort,
		ParentMqttTcpPort: t.ParentMqttTcpPort,
		ParentMqttWsPort:  t.ParentMqttWsPort,
		ParentSsl:         t.ParentSsl,
		OsType:            t.OsType,
		Networks:          t.Networks,
		Uptime:            t.Uptime,
		Version:           t.Version,
		State:             t.State,
		CreatedAt:         t.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         t.UpdatedAt.Format("2006-01-02 15:04:05"),
		IsLocal:           t.IsLocal,
	}
}

func (t *Client) ToJSON() string {
	data, _ := json.Marshal(t)
	return string(data)
}
