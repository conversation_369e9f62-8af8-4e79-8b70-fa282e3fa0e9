package lib

import (
	"archive/zip"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"sync"
	"time"
)

// ExtractTask 解压任务
type ExtractTask struct {
	File     *zip.File
	DestPath string
}

// ExtractStats 解压统计信息
type ExtractStats struct {
	TotalFiles     int64
	ProcessedFiles int64
	TotalSize      int64
	ProcessedSize  int64
	StartTime      time.Time
	SmallFiles     int64 // 小文件数量（<1MB）
	LargeFiles     int64 // 大文件数量（>=1MB）
	Errors         []error
	mu             sync.RWMutex
}

func (es *ExtractStats) AddProcessed(size int64) {
	es.mu.Lock()
	defer es.mu.Unlock()
	es.ProcessedFiles++
	es.ProcessedSize += size
}

func (es *ExtractStats) AddError(err error) {
	es.mu.Lock()
	defer es.mu.Unlock()
	es.Errors = append(es.Errors, err)
}

func (es *ExtractStats) GetProgress() (float64, string) {
	es.mu.RLock()
	defer es.mu.RUnlock()
	
	if es.TotalFiles == 0 {
		return 0, "准备中..."
	}
	
	progress := float64(es.ProcessedFiles) / float64(es.TotalFiles) * 100
	elapsed := time.Since(es.StartTime)
	
	if es.ProcessedFiles > 0 {
		avgTimePerFile := elapsed / time.Duration(es.ProcessedFiles)
		remainingFiles := es.TotalFiles - es.ProcessedFiles
		eta := avgTimePerFile * time.Duration(remainingFiles)
		
		return progress, fmt.Sprintf("已解压: %d/%d 文件 (小文件:%d, 大文件:%d), 预计剩余: %v", 
			es.ProcessedFiles, es.TotalFiles, es.SmallFiles, es.LargeFiles, eta.Round(time.Second))
	}
	
	return progress, fmt.Sprintf("已解压: %d/%d 文件", es.ProcessedFiles, es.TotalFiles)
}

// ExtractConfig 解压配置参数
type ExtractConfig struct {
	ExtractThreads   int32 // 解压线程数，0=使用所有CPU核心
	ExtractQueueSize int32 // 解压队列大小
}

// DefaultExtractConfig 默认解压配置
func DefaultExtractConfig() *ExtractConfig {
	return &ExtractConfig{
		ExtractThreads:   0,  // 使用所有CPU核心
		ExtractQueueSize: 10, // 队列大小
	}
}

// UnzipOptimizedForFragments 专门针对大量碎片文件优化的解压函数
func UnzipOptimizedForFragments(src, dir string, progressCallback func(float64, string)) error {
	return UnzipOptimizedForFragmentsWithConfig(src, dir, DefaultExtractConfig(), progressCallback)
}

// UnzipOptimizedForFragmentsWithConfig 使用指定配置的优化解压函数
func UnzipOptimizedForFragmentsWithConfig(src, dir string, conf *ExtractConfig, progressCallback func(float64, string)) error {
	
	// 打开ZIP文件
	r, err := zip.OpenReader(src)
	if err != nil {
		return fmt.Errorf("打开ZIP文件失败: %v", err)
	}
	defer r.Close()
	
	// 确保目标目录存在
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}
	
	// 分析文件结构，优化处理策略
	stats := analyzeZipStructure(&r.Reader)
	slog.Info(fmt.Sprintf("[高性能解压] ZIP文件分析完成: 总文件数=%d, 小文件=%d, 大文件=%d, 总大小=%s", 
		stats.TotalFiles, stats.SmallFiles, stats.LargeFiles, ByteCountBinary(uint64(stats.TotalSize))))
	
	// 根据文件特征选择最优解压策略
	if stats.SmallFiles > stats.LargeFiles*10 { // 如果小文件数量是大文件的10倍以上
		return extractWithSmallFileOptimization(&r.Reader, dir, stats, conf, progressCallback)
	} else {
		return extractWithBalancedStrategy(&r.Reader, dir, stats, conf, progressCallback)
	}
}

// analyzeZipStructure 分析ZIP文件结构
func analyzeZipStructure(r *zip.Reader) *ExtractStats {
	stats := &ExtractStats{
		StartTime: time.Now(),
	}
	
	const smallFileThreshold = 1024 * 1024 // 1MB阈值
	
	for _, f := range r.File {
		if !f.FileInfo().IsDir() {
			stats.TotalFiles++
			stats.TotalSize += int64(f.UncompressedSize64)
			
			if f.UncompressedSize64 < smallFileThreshold {
				stats.SmallFiles++
			} else {
				stats.LargeFiles++
			}
		}
	}
	
	return stats
}

// extractWithSmallFileOptimization 针对大量小文件的优化解压策略
func extractWithSmallFileOptimization(r *zip.Reader, dir string, stats *ExtractStats, conf *ExtractConfig, progressCallback func(float64, string)) error {
	// 对于大量小文件，使用更多的并发线程和批处理
	workers := int(conf.ExtractThreads)
	if workers <= 0 {
		workers = runtime.NumCPU() * 2 // 小文件场景使用更多线程
	}
	
	// 先创建所有目录
	if err := createDirectories(r, dir); err != nil {
		return fmt.Errorf("创建目录结构失败: %v", err)
	}
	
	// 按文件大小排序，小文件优先处理
	files := make([]*zip.File, 0, len(r.File))
	for _, f := range r.File {
		if !f.FileInfo().IsDir() {
			files = append(files, f)
		}
	}
	
	sort.Slice(files, func(i, j int) bool {
		return files[i].UncompressedSize64 < files[j].UncompressedSize64
	})
	
	return extractFilesParallel(files, dir, stats, workers, progressCallback)
}

// extractWithBalancedStrategy 平衡策略解压
func extractWithBalancedStrategy(r *zip.Reader, dir string, stats *ExtractStats, conf *ExtractConfig, progressCallback func(float64, string)) error {
	workers := int(conf.ExtractThreads)
	if workers <= 0 {
		workers = runtime.NumCPU()
	}
	
	// 先创建所有目录
	if err := createDirectories(r, dir); err != nil {
		return fmt.Errorf("创建目录结构失败: %v", err)
	}
	
	// 分离大文件和小文件
	var smallFiles, largeFiles []*zip.File
	const threshold = 1024 * 1024 // 1MB
	
	for _, f := range r.File {
		if !f.FileInfo().IsDir() {
			if f.UncompressedSize64 < threshold {
				smallFiles = append(smallFiles, f)
			} else {
				largeFiles = append(largeFiles, f)
			}
		}
	}
	
	// 先处理大文件（减少并发度）
	if len(largeFiles) > 0 {
		slog.Info(fmt.Sprintf("[高性能解压] 开始处理 %d 个大文件", len(largeFiles)))
		if err := extractFilesParallel(largeFiles, dir, stats, workers/2, progressCallback); err != nil {
			return err
		}
	}
	
	// 再处理小文件（增加并发度）
	if len(smallFiles) > 0 {
		slog.Info(fmt.Sprintf("[高性能解压] 开始处理 %d 个小文件", len(smallFiles)))
		if err := extractFilesParallel(smallFiles, dir, stats, workers, progressCallback); err != nil {
			return err
		}
	}
	
	return nil
}

// createDirectories 预先创建所有目录
func createDirectories(r *zip.Reader, dir string) error {
	for _, f := range r.File {
		if f.FileInfo().IsDir() {
			path := filepath.Join(dir, f.Name)
			if err := os.MkdirAll(path, f.Mode()); err != nil {
				return err
			}
		} else {
			// 确保文件的父目录存在
			path := filepath.Join(dir, f.Name)
			if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
				return err
			}
		}
	}
	return nil
}

// extractFilesParallel 并发解压文件
func extractFilesParallel(files []*zip.File, dir string, stats *ExtractStats, workers int, progressCallback func(float64, string)) error {
	taskChan := make(chan *ExtractTask, len(files))
	errorChan := make(chan error, len(files))
	
	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			extractWorker(workerID, taskChan, errorChan, stats)
		}(i)
	}
	
	// 启动进度监控协程
	progressDone := make(chan bool)
	go func() {
		ticker := time.NewTicker(500 * time.Millisecond)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				if progressCallback != nil {
					progress, message := stats.GetProgress()
					progressCallback(progress, message)
				}
			case <-progressDone:
				return
			}
		}
	}()
	
	// 发送任务到工作队列
	go func() {
		defer close(taskChan)
		for _, f := range files {
			taskChan <- &ExtractTask{
				File:     f,
				DestPath: filepath.Join(dir, f.Name),
			}
		}
	}()
	
	// 等待所有工作完成
	wg.Wait()
	close(errorChan)
	progressDone <- true
	
	// 检查错误
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}
	
	// 最终进度更新
	if progressCallback != nil {
		progress, message := stats.GetProgress()
		progressCallback(progress, message)
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("解压过程中发生 %d 个错误，首个错误: %v", len(errors), errors[0])
	}
	
	return nil
}

// extractWorker 解压工作协程
func extractWorker(workerID int, taskChan <-chan *ExtractTask, errorChan chan<- error, stats *ExtractStats) {
	for task := range taskChan {
		if err := extractSingleFile(task.File, task.DestPath); err != nil {
			errorChan <- fmt.Errorf("解压文件 %s 失败: %v", task.File.Name, err)
			continue
		}
		
		stats.AddProcessed(int64(task.File.UncompressedSize64))
	}
}

// extractSingleFile 解压单个文件
func extractSingleFile(f *zip.File, destPath string) error {
	rc, err := f.Open()
	if err != nil {
		return err
	}
	defer rc.Close()
	
	outFile, err := os.OpenFile(destPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
	if err != nil {
		return err
	}
	defer outFile.Close()
	
	// 根据文件大小选择缓冲区大小
	bufferSize := 64 * 1024 // 默认64KB
	if f.UncompressedSize64 > 1024*1024 { // 大于1MB的文件使用更大缓冲区
		bufferSize = 256 * 1024 // 256KB
	}
	
	buf := make([]byte, bufferSize)
	_, err = io.CopyBuffer(outFile, rc, buf)
	return err
}
