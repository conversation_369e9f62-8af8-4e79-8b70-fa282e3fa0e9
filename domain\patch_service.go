package domain

import (
	"os"
	"path/filepath"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	patchesv1 "gitlab.jhonginfo.com/product/ais-server/proto/patches/v1"
)

func PatchPO2PatchVO(po *Patch) *patchesv1.Patch {
	if po == nil {
		return nil
	}
	vo := &patchesv1.Patch{
		Id:          po.Id,
		Name:        po.Name,
		PackageName: po.PackageName,
		Uri:         po.Uri,
	}
	return vo
}

func PatchPOs2PatchVOs(pos []*Patch) []*patchesv1.Patch {
	vos := make([]*patchesv1.Patch, 0)
	for _, po := range pos {
		vo := PatchPO2PatchVO(po)
		vos = append(vos, vo)
	}
	return vos
}

func ListPatches(name string, uri string, pName string) ([]*Patch, error) {
	storeDir, err := config.GetStoreDir()
	if err != nil {
		return nil, err
	}
	dirs, err := lib.ListDirs(storeDir, false)
	if err != nil {
		return nil, err
	}
	var i int32 = 1
	patches := make([]*Patch, 0)
	for _, dir := range dirs {
		patchDir := filepath.Join(dir, "patches")
		if _, err := os.Stat(patchDir); os.IsNotExist(err) {
			continue
		}
		patchFiles, err := lib.ListFiles(patchDir)
		if err != nil {
			return nil, err
		}
		pkgName := filepath.Base(dir)
		for _, patchFile := range patchFiles {
			patchName := filepath.Base(patchFile)
			if pName != "" && pName != pkgName {
				continue
			}
			if name != "" && name != patchName {
				continue
			}
			if uri != "" && uri != patchFile {
				continue
			}
			patch := &Patch{
				Id:          i,
				Name:        patchName,
				PackageName: pkgName,
				Uri:         patchFile,
			}
			patches = append(patches, patch)
			i++
		}
	}
	return patches, nil
}
