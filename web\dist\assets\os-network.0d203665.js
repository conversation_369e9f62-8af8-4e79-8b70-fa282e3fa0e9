import{u as a,b as e,j as o,s,T as t}from"./index.bbeb3af6.js";import{l as n,D as l}from"./index.6af89ef4.js";import{C as r}from"./index.cf9faf12.js";function i(i){const f=a(n),{data:m}=i,d=[];if(m.networks)for(const[,a]of Object.entries(m.networks))d.push({label:f["os.network.name"],value:a.name}),d.push({label:f["os.network.family"],value:a.family}),d.push({label:f["os.network.address"],value:a.address}),d.push({label:f["os.network.mac"],value:a.mac});return e(r,{children:[o(s,{align:"start",children:o(t.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:f["os.network.title"]})}),o(l,{colon:": ",layout:"horizontal",data:d,column:4})]})}export{i as default};
