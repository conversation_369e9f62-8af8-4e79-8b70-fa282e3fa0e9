@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: Helper function for robust directory copy with robocopy and logging
:: Usage: call :ROBOCOPY_DIR <SourceDirPath> <DestinationDirPath>
:ROBOCOPY_DIR
    set "SOURCE_DIR=%~1"
    set "DEST_DIR=%~2"

    echo [INFO] Attempting to copy directory: "%SOURCE_DIR%" to "%DEST_DIR%"

    :: Perform robocopy
    robocopy "%SOURCE_DIR%" "%DEST_DIR%" /E /NFL /NDL /NJH /NJS /NC /NS /NP /LOG+:%~dp0install.log /R:3 /W:5
    set "ROBOCOPY_EXIT_CODE=!ERRORLEVEL!"

    :: Robocopy exit codes:
    :: 0 - No errors, no files copied.
    :: 1 - All files copied successfully.
    :: 2 - Some extra files or mismatched files were detected.
    :: 3 - Some files could not be copied (and retries exhausted).
    :: 4 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected.
    :: 5 - Some files could not be copied (and retries exhausted) and all files copied successfully.
    :: 6 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully.
    :: 7 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully and some extra files or mismatched files were detected.
    :: 8 - Some files could not be copied (and retries exhausted) and some extra files or mismatched files were detected and all files copied successfully and some extra files or mismatched files were detected and some files could not be copied (and retries exhausted).

    if !ROBOCOPY_EXIT_CODE! LEQ 8 (
        echo [SUCCESS] Directory copy completed with robocopy exit code: !ROBOCOPY_EXIT_CODE!
        exit /b 0
    ) else (
        echo [ERROR] Directory copy failed with robocopy exit code: !ROBOCOPY_EXIT_CODE!
        exit /b 1
    )
goto :EOF

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2 %~3 %~4 %~5 %~6 %~7", "", "runas", 1 >> "%temp%\getadmin.vbs"

    "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

set "C_NAME=%~1"
set "C_VERSION=%~2"
set "C_OS_TYPE=%~3"
set "C_OS_ARCH=%~4"
set "C_PKG_DIR=%~5"
if "%C_VERSION%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_NAME%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_OS_TYPE%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_OS_ARCH%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
) || if "%C_PKG_DIR%"=="" (
    echo Usage: %0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]
    exit /B 1
)
set "C_IS_SDK=%~6"
if "%C_IS_SDK%"=="" (
    set "C_IS_SDK=false"
)
set "C_TAR_ROOT=%~7"
if "%C_TAR_ROOT%"=="" (
    set "C_TAR_ROOT=C:\AIS\%C_NAME%"
)

set "C_HOME=%C_NAME%"
for %%i in (A B C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    set "C_HOME=!C_HOME:%%i=%%i!"
)
set "C_HOME=%C_HOME%_HOME"
set "C_PACKAGE_PATH=%C_PKG_DIR%\%C_NAME%\"
set "C_ORIGIN_PATH=%C_PKG_DIR%\%C_NAME%-%C_VERSION%-%C_OS_TYPE%-%C_OS_ARCH%\"

@REM 查找并安装：判断C_ORIGIN_PATH目录是否存在，存在则安装
if exist "%C_ORIGIN_PATH%" (
    goto :install
)
goto :eof

@REM 安装：将C_ORIGIN_PATH目录拷贝到C_TAR_ROOT目录
:install
if not exist "%C_TAR_ROOT%" (
    call :ROBOCOPY_DIR "%C_ORIGIN_PATH%" "%C_TAR_ROOT%"
)
call :set_env
goto :eof

@REM 设置环境变量
:set_env
setx %C_HOME% "%C_TAR_ROOT%" /M
goto :eof