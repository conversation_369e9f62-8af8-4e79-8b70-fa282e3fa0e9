import{u as a,b as o,j as e,s,T as i}from"./index.bbeb3af6.js";import{l,D as t}from"./index.6af89ef4.js";import{C as n}from"./index.cf9faf12.js";function r(r){const f=a(l),{data:m}=r,u=[{label:f["os.info.type"],value:m.os.type},{label:f["os.info.version"],value:m.os.version},{label:f["os.info.arch"],value:m.os.arch},{label:f["os.times.up"],value:m.times.up}];return o(n,{children:[e(s,{align:"start",children:e(i.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:f["os.info.title"]})}),e(t,{colon:": ",layout:"horizontal",data:u,column:2})]})}export{r as default};
