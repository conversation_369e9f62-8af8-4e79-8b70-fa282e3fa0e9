.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
  }

  .content {
    padding: 0;
  }

  .fileSelector {
    .arco-input-wrapper {
      .arco-input-suffix {
        padding: 0;
        border-left: none;
        
        .arco-btn {
          border-radius: 0 4px 4px 0;
          border-left: 1px solid var(--color-border-2);
        }
      }
    }
  }

  .fileInfo {
    margin-top: 16px;
    background-color: var(--color-fill-1);
    border: 1px solid var(--color-border-2);
    
    .arco-card-body {
      padding: 12px 16px;
    }
  }

  .progressCard {
    margin-top: 16px;
    border: 1px solid var(--color-primary-light-3);
    background-color: var(--color-primary-light-1);
    
    .arco-card-body {
      padding: 16px;
    }
    
    .progressHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
  }
}

.uploadArea {
  border: 2px dashed var(--color-border-3);
  border-radius: 6px;
  padding: 40px 20px;
  text-align: center;
  background-color: var(--color-fill-1);
  transition: all 0.3s;
  cursor: pointer;
  
  &:hover {
    border-color: var(--color-primary-6);
    background-color: var(--color-primary-light-1);
  }
  
  .uploadIcon {
    font-size: 48px;
    color: var(--color-text-3);
    margin-bottom: 16px;
  }
  
  .uploadText {
    color: var(--color-text-2);
    margin-bottom: 8px;
  }
  
  .uploadHint {
    color: var(--color-text-3);
    font-size: 12px;
  }
}

.selectedFile {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--color-success-light-1);
  border: 1px solid var(--color-success-light-3);
  border-radius: 4px;
  margin-top: 16px;
  
  .fileIcon {
    color: var(--color-success-6);
    margin-right: 12px;
    font-size: 20px;
  }
  
  .fileDetails {
    flex: 1;
    
    .fileName {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .filePath {
      font-size: 12px;
      color: var(--color-text-3);
      word-break: break-all;
    }
  }
  
  .removeBtn {
    color: var(--color-text-3);
    cursor: pointer;
    
    &:hover {
      color: var(--color-danger-6);
    }
  }
}

.progressSection {
  margin-top: 24px;
  padding: 20px;
  background-color: var(--color-fill-1);
  border-radius: 6px;
  border: 1px solid var(--color-border-2);
  
  .progressTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .progressBar {
    margin-bottom: 8px;
  }
  
  .progressMessage {
    font-size: 12px;
    color: var(--color-text-3);
  }
}

.actionButtons {
  margin-top: 24px;
  text-align: center;
  
  .arco-btn {
    margin: 0 8px;
  }
}