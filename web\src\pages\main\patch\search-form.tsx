import React from 'react';
import {
  Form,
  Input,
  Button,
  Grid,
} from '@arco-design/web-react';
import locale from './locale';
import useLocale from '@/utils/useLocale';
import { IconRefresh, IconSearch } from '@arco-design/web-react/icon';
import styles from './style/index.module.less';

const { Row, Col } = Grid;
const { useForm } = Form;

function SearchForm(props: {
  loading: boolean;
  onSearch: (values: Record<string, any>) => void;
}) {
  const t = useLocale(locale);
  const { loading } = props;
  const [form] = useForm();

  const handleSubmit = () => {
    const values = form.getFieldsValue();
    props.onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
    props.onSearch({});
  };

  const colSpan = 12;

  return (
    <div className={styles['search-form-wrapper']}>
      <Form
        form={form}
        className={styles['search-form']}
        labelAlign="left"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
      >
        <Row gutter={24}>
          <Col span={colSpan}>
            <Form.Item label={t['patch.columns.attribution']} field="packageName">
              <Input placeholder={t['patch.forms.search.attribution.placeholder']} allowClear />
            </Form.Item>
          </Col>
          <Col span={colSpan}>
            <Form.Item label={t['patch.columns.name']} field="name">
              <Input
                allowClear
                placeholder={t['patch.forms.search.name.placeholder']}
              />
            </Form.Item>
          </Col>
          <Col span={colSpan}>
            <Form.Item
              label={t['patch.columns.uri']}
              field="uri"
            >
              <Input
                allowClear
                placeholder={t['patch.forms.search.uri.placeholder']}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <div className={styles['right-button']}>
        <Button
          loading={loading}
          type="primary" icon={<IconSearch />}
          onClick={handleSubmit}>
          {t['patch.tables.main.form.search']}
        </Button>
        <Button
          loading={loading}
          icon={<IconRefresh />}
          onClick={handleReset}>
          {t['patch.tables.main.form.reset']}
        </Button>
      </div>
    </div>
  );
}

export default SearchForm;
