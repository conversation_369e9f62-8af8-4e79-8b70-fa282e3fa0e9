import{u as a,b as o,j as e,s,T as i}from"./index.e8bac691.js";import{l,D as t}from"./index.b8bf3dbf.js";import{C as n}from"./index.a97db34b.js";function r(r){const d=a(l),{data:m}=r,u=[{label:d["os.info.type"],value:m.os.type},{label:d["os.info.version"],value:m.os.version},{label:d["os.info.arch"],value:m.os.arch},{label:d["os.times.up"],value:m.times.up}];return o(n,{children:[e(s,{align:"start",children:e(i.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:d["os.info.title"]})}),e(t,{colon:": ",layout:"horizontal",data:u,column:2})]})}export{r as default};
