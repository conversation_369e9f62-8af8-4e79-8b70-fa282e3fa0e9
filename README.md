# ais-server

## Requirements

Go 1.21+

## Getting started

After cloning the repo, there are a couple of initial steps;

1. Finally, generate the files with `make generate`.

Now you can run the web server with `go run main.go`.

## Making it your own

The next step is to define the interface you want to expose in
`proto/projects/v1/project.proto`. See https://developers.google.com/protocol-buffers/
tutorials and guides on writing Protobuf files. See the Buf
[style guide](https://docs.buf.build/best-practices/style-guide#files-and-packages)
for tips on how to structure your packages.

Once that is done, regenerate the files using
`make generate`. This will mean you'll need to implement any functions in
`server/server.go`, or else the build will fail since your struct won't
be implementing the interface defined by the generated files anymore.

This should hopefully be all you need to get started playing around with the gRPC-Gateway!

## Deploying
### Build the Docker image
```shell
docker build -t ais-server .
```