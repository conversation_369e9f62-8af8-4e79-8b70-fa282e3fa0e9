import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  Card,
  PaginationProps,
  Button,
  Space,
  Message,
} from '@arco-design/web-react';

import { IconRefresh } from '@arco-design/web-react/icon';

import useLocale from '@/utils/useLocale';
import api from '@/utils/api';

import locale from './locale';
import styles from './style/index.module.less';
import { getColumns } from './constants';

function StoreTable() {
  const t = useLocale(locale);

  const columns = useMemo(() => getColumns(t), [t]);

  const [data, setData] = useState([]);
  const [pagination, setPatination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 50,
    current: 1,
    pageSizeChangeResetCurrent: true,
    pageSizeOptions: ['10', '20', '50', '100', '200'],
  });
  const [loading, setLoading] = useState(true);

  function fetchData() {
    const { current, pageSize } = pagination;
    setLoading(true);
    api('/api/v1/logs', {
      data: {
        page: current,
        size: pageSize,
      }
    })
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data && !resData.data.content) {
          return;
        }
        setData(resData.data.content);
        setPatination({
          ...pagination,
          current,
          pageSize,
          total: resData.data.total,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function onChangeTable({ current, pageSize }) {
    setPatination({
      ...pagination,
      current,
      pageSize,
    });
  }

  function handleClear() {
    api('/api/v1/logs', { method: "delete" }).then(() => {
      Message.success(t['searchTable.operations.clear.success']);
      fetchData();
    });
  }

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  return (
    <Card>
      {/* <SearchForm onSearch={handleSearch} /> */}
      <div className={styles['button-group']}>
        <Space>
          <Button type="primary" status="danger" icon={<IconRefresh />} onClick={handleClear}>
            {t['searchTable.operations.clear']}
          </Button>
        </Space>
      </div>
      <Table
        rowKey="id"
        loading={loading}
        onChange={onChangeTable}
        pagination={pagination}
        columns={columns}
        data={data}
      />
    </Card>
  );
}

export default StoreTable;
