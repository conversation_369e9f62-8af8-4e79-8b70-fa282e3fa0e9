import React, { useState, useEffect, useMemo } from 'react';
import { Link, useHistory } from 'react-router-dom';
import {
  Table,
  Card,
  PaginationProps,
  Button,
  Space,
  Message,
  Modal,
  Notification
} from '@arco-design/web-react';

import PermissionWrapper from '@/components/PermissionWrapper';
import { 
  IconImport,
  IconFolder
} from '@arco-design/web-react/icon';

import api from '@/utils/api';
import useLocale from '@/utils/useLocale';

import SearchForm from './search-form';
import locale from './locale';
import styles from './style/index.module.less';
import { getColumns } from './constants';

function StoreTable() {
  const history = useHistory();
  const t = useLocale(locale);

  const tableCallback = async (record, type) => {
    switch (type) {
      case 'upgrade':
        history.push({ pathname: '/main/store-upgrade-form', state: record });
        break;
      case 'update':
        history.push({ pathname: '/main/store-update-form', state: record });
        break;
      case 'delete':
        Modal.confirm({
          title: t['store.tables.main.modal.delete.title'],
          content: t['store.tables.main.modal.delete.content'],
          onOk: () => {
            setLoading(true);
            api(`/api/v1/packages/${record.id}`, {
              method: 'delete'
            }).then(() => {
              Message.success(t['store.tables.main.modal.delete.operation.success']);
              fetchData();
            }).catch(() => {
              Message.error(t['store.tables.main.modal.delete.operation.fail']);
            }).finally(() => {
              setLoading(false);
            });
          },
        });
        break;
      default:
        Modal.confirm({
          title: t[`store.tables.main.modal.${type}.title`],
          content: t[`store.tables.main.modal.${type}.content`],
          onOk: () => {
            setLoading(true);
            api(`/api/v1/tasks`, {
              method: 'post',
              data: {
                name: type,
                packageId: record.id
              },
              timeout: (type === 'export' ? 5 : 1) * 60 * 1000, // 1~5 min
            }).then((res) => {
              const resData = res && res.data;
              if (!resData && !resData.data) {
                return;
              }
              if (resData.data.state === 'failed') {
                Message.error(t[`store.tables.main.modal.${type}.operation.fail`]);
              } else {
                if (type === 'export') {
                  const id = `${Date.now()}`;
                  const uri = resData.data.content;
                  Notification.info({
                    id: id,
                    title: t['store.notifications.export.title'],
                    content: `${t['store.notifications.export.location']}: ${uri}`,
                    duration: 0,
                    btn: (
                      <span>
                        <Button
                          type='secondary'
                          size='small'
                          onClick={() => Notification.remove(id)}
                          style={{ margin: '0 12px' }}
                        >
                          { t['store.const.cancel'] }
                        </Button>
                        <Button type='primary' size='small' onClick={() => Notification.remove(id)}>
                          { t['store.const.ok'] }
                        </Button>
                      </span>
                    ),
                  });
                }
                Message.success(t[`store.tables.main.modal.${type}.operation.success`]);
              }
              fetchData();
            }).catch(() => {
              Message.error(t[`store.tables.main.modal.${type}.operation.fail`]);
            }).finally(() => {
              setLoading(false);
            });
          },
        });
        break;
    }
  };

  const columns = useMemo(() => getColumns(t, tableCallback), [t]);

  const [data, setData] = useState([]);
  const [pagination, setPatination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 50,
    current: 1,
    pageSizeChangeResetCurrent: true,
    pageSizeOptions: ['10', '20', '50', '100', '200'],
  });
  const [loading, setLoading] = useState(true);
  const [formParams, setFormParams] = useState({});

  function fetchData() {
    const { current, pageSize } = pagination;
    setLoading(true);
    api('/api/v1/packages', {
      data: {
        page: current,
        size: pageSize,
        ...formParams,
      }
    })
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data && !resData.data.content) {
          return;
        }
        setData(resData.data.content);
        setPatination({
          ...pagination,
          current,
          pageSize,
          total: resData.data.total
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function onChangeTable({ current, pageSize }) {
    setPatination({
      ...pagination,
      current,
      pageSize,
    });
  }

  function handleSearch(params) {
    setPatination({ ...pagination, current: 1 });
    setFormParams(params);
  }

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, JSON.stringify(formParams)]);

  return (
    <Card>
      <SearchForm
        loading={loading}
        onSearch={handleSearch}
      />
      <PermissionWrapper
        requiredPermissions={[
          { resource: 'menu.main.store', actions: ['write'] },
        ]}
      >
        <div className={styles['button-group']}>
          <Space>
            <Link to='/main/store-import-form'>
              <Button
                  loading={loading}
                  type="primary"
                  icon={<IconImport />}>
                  {t['store.tables.main.operation.import']}
              </Button>
            </Link>
            <Link to='/main/store-local-import'>
              <Button
                  loading={loading}
                  type="outline"
                  icon={<IconFolder />}>
                  本地导入
              </Button>
            </Link>
          </Space>
        </div>
      </PermissionWrapper>
      <Table
        rowKey="id"
        loading={loading}
        onChange={onChangeTable}
        pagination={pagination}
        columns={columns}
        data={data}
      />
    </Card>
  );
}

export default StoreTable;
