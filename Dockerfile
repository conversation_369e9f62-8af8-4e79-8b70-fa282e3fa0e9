# Build stage
FROM golang:1.21.0-alpine AS builder
RUN apk add --no-cache tzdata \
    && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
WORKDIR /src/ebot
ADD . .
ENV GOPROXY https://goproxy.cn,direct
RUN CGO_ENABLED=0 GOOS=linux go build -o /app

# Production stage
FROM scratch
COPY --from=builder /app /
COPY --from=builder /etc/localtime /etc/localtime
COPY --from=builder /etc/timezone /etc/timezone
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
ENV TZ=Asia/Shanghai
ENTRYPOINT ["/app"]
