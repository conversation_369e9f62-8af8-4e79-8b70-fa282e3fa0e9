import{u as e,r as a,b as t,j as o,k as r,l as n,i as s}from"./index.e8bac691.js";import{C as i}from"./index.a97db34b.js";import{T as m}from"./index.acd49a0a.js";import{i as c,S as l}from"./search-form.02b25c0c.js";import{getColumns as p}from"./constants.2e564450.js";import"./index.dde5f5a0.js";function d(){const d=e(c),g=async(e,a)=>{if("delete"===a)r.confirm({title:d["program.tables.main.modal.delete.title"],content:d["program.tables.main.modal.delete.content"],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}`,{method:"delete"}).then((()=>{s.success(d["program.tables.main.modal.delete.operation.success"]),$()})).catch((()=>{s.error(d["program.tables.main.modal.delete.operation.fail"])})).finally((()=>{z(!1)}))}});else r.confirm({title:d[`program.tables.main.modal.${a}.title`],content:d[`program.tables.main.modal.${a}.content`],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}/cmds`,{method:"post",data:{type:a},timeout:6e4}).then((e=>{const t=e&&e.data;(t||t.data)&&(0!==t.code?s.error(d[`program.tables.main.modal.${a}.operation.fail`]):s.success(d[`program.tables.main.modal.${a}.operation.success`]),$())})).catch((()=>{s.error(d[`program.tables.main.modal.${a}.operation.fail`])})).finally((()=>{z(!1)}))}})},u=a.exports.useMemo((()=>p(d,g)),[d]),[f,S]=a.exports.useState([]),[b,h]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[x,z]=a.exports.useState(!0),[j,y]=a.exports.useState({});function $(){const{current:e,pageSize:a}=b;z(!0),n("/api/v1/programs",{data:{page:e,size:a,...j}}).then((t=>{const o=t&&t.data;(o||o.data||o.data.content)&&(S(o.data.content),h({...b,current:e,pageSize:a,total:o.data.total}))})).finally((()=>{z(!1)}))}return a.exports.useEffect((()=>{$()}),[b.current,b.pageSize,JSON.stringify(j)]),t(i,{children:[o(l,{loading:x,onSearch:function(e){h({...b,current:1}),y(e)}}),o(m,{rowKey:"id",loading:x,onChange:function({current:e,pageSize:a}){h({...b,current:e,pageSize:a})},pagination:b,columns:u,data:f})]})}export{d as default};
