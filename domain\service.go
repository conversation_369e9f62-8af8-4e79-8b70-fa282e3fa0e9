package domain

import (
	"context"
	"fmt"
	"log/slog"
	"net"

	"github.com/kardianos/service"
	"github.com/urfave/cli/v3"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/gateway"
	"gitlab.jhonginfo.com/product/ais-server/insecure"
	clientsv1 "gitlab.jhonginfo.com/product/ais-server/proto/clients/v1"
	configsv1 "gitlab.jhonginfo.com/product/ais-server/proto/configs/v1"
	logsv1 "gitlab.jhonginfo.com/product/ais-server/proto/logs/v1"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
	patchesv1 "gitlab.jhonginfo.com/product/ais-server/proto/patches/v1"
	programsv1 "gitlab.jhonginfo.com/product/ais-server/proto/programs/v1"
	sysinfosv1 "gitlab.jhonginfo.com/product/ais-server/proto/sysinfos/v1"
	tasksv1 "gitlab.jhonginfo.com/product/ais-server/proto/tasks/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

const MAX_MESSAGE_SIZE = 1024 * 1024 * 1024 // 1GB

type SystemService struct {
	service.Service
}

func (t *SystemService) run() {
	name := config.Name()

	// Initialize the current client
	InitializeCurrentClient()

	// Initialize the schedule tasks
	InitializeScheduleTasks()

	addr := config.GetAddress()
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		slog.Error("Failed to listen:" + err.Error())
	}

	s := grpc.NewServer(
		// TODO: Replace with your own certificate!
		grpc.Creds(credentials.NewServerTLSFromCert(&insecure.Cert)),
		grpc.MaxRecvMsgSize(MAX_MESSAGE_SIZE),
		grpc.MaxSendMsgSize(MAX_MESSAGE_SIZE),
	)
	backend := New()

	clientsv1.RegisterClientServiceServer(s, backend)
	configsv1.RegisterConfigServiceServer(s, backend)
	logsv1.RegisterLogServiceServer(s, backend)
	programsv1.RegisterPackageServiceServer(s, backend)
	packagesv1.RegisterPackageServiceServer(s, backend)
	patchesv1.RegisterPatchServiceServer(s, backend)
	sysinfosv1.RegisterSysinfoServiceServer(s, backend)
	tasksv1.RegisterTaskServiceServer(s, backend)

	// Serve gRPC Server
	slog.Info("Serving gRPC on https://" + addr)
	go func() {
		err := s.Serve(lis)
		if err != nil {
			slog.Error("Failed to serve gRPC: " + err.Error())
		}
	}()

	err = gateway.Run("dns:///" + addr)
	slog.Error("Gateway run failed, err: " + err.Error())
	slog.Info(fmt.Sprintf("Service(%s) started.", name))
}

func (t *SystemService) Start(s service.Service) error {
	go t.run()
	return nil
}

func (t *SystemService) Stop(s service.Service) error {
	return nil
}

func (t *SystemService) Default(ctx context.Context, _ *cli.Command) error {
	err := t.Run()
	if err != nil {
		return err
	}
	return nil
}

func (t *SystemService) Control(ctx context.Context, cmd *cli.Command) error {
	switch cmd.Name {
	case ScriptNames.Config:
		config.Get().Show()
		return nil
	}
	err := service.Control(t.Service, cmd.Name)
	if err != nil {
		return err
	}
	if ScriptNames.Install == cmd.Name {
		err = config.Get().Save()
		if err != nil {
			return err
		}
	}
	return nil
}

func NewSystemService() (*SystemService, error) {
	name := config.Name()
	svcConfig := &service.Config{
		Name:        name,
		DisplayName: name,
		Description: name,
		Option: service.KeyValue{
			"OnFailure":              "restart",
			"OnFailureDelayDuration": "200000ms", // 启动失败三分钟再次拉起
			"StartType":              "automatic",
			"DelayedAutoStart":       true,  // 系统重启,agent服务延迟重启
			"OnFailureResetPeriod":   86400, // 故障重置时间1天
		},
	}

	ss := &SystemService{}
	s, err := service.New(ss, svcConfig)
	if err != nil {
		return nil, fmt.Errorf("service new failed, err: " + err.Error())
	}
	ss.Service = s
	return ss, nil
}
