import React from 'react';
import { 
  Tag,
  But<PERSON>,
  Typo<PERSON>,
  Badge,
  Space
} from '@arco-design/web-react';

const { Text } = Typography;

const isSupport = (record, func) => {
  return record.support && record.support.indexOf(func) !== -1;
}

const isDownloading = (record) => {
  return record.state === 'downloading';
}

const isDownloaded = (record) => {
  return record.downloaded === 1;
}

const isInstalling = (record) => {
  return record.state === 'installing';
}

const isInstalled = (record) => {
  return record.installed === 1;
}

const isRunning = (record) => {
  return record.state === 'running';
}

const isBacked = (record) => {
  return record.backed === 1;
}

const canDownload = (record) => {
  return !isDownloading(record) && !isDownloaded(record);
}

const canInstall = (record) => {
  return !isDownloading(record) && !canDownload(record) && !isInstalling(record) && !isInstalled(record);
}

const canUninstall = (record) => {
  return !isDownloading(record) && !canDownload(record) && !isInstalling(record) && !canInstall(record) && isInstalled(record);
}

const canStart = (record) => {
  return canUninstall(record) && isSupport(record, 'start') && !isRunning(record);
}

const canStop = (record) => {
  return canUninstall(record) && isSupport(record, 'stop') && isRunning(record);
}

const canDelete = (record) => {
  return !isInstalled(record) && (!isSupport(record, 'stop') || (isSupport(record, 'stop') && !isRunning(record)));
}

const canBackup = (record) => {
  return isInstalled(record) && isSupport(record, 'backup') && isRunning(record);
}

const canRestore = (record) => {
  return isInstalled(record) && isSupport(record, 'restore') && isBacked(record);
}

const canUpdate = (record) => {
  return isInstalled(record) && isRunning(record);
}

const canUpgrade = (record) => {
  return isDownloaded(record) && !isRunning(record);
}

const canExport = (record) => {
  return isInstalled(record) && isRunning(record);
}

export function getColumns(
  t: any,
  callback: (record: Record<string, any>, type: string) => Promise<void>
) {
  return [
    {
      title: t['store.columns.id'],
      dataIndex: 'id',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['store.columns.name'],
      dataIndex: 'name',
    },
    {
      title: t['store.columns.version'],
      dataIndex: 'version',
    },
    {
      title: t['store.columns.type'],
      dataIndex: 'type',
      render: (value) => t[`store.columns.types.${value}`],
    },
    {
      title: t['store.columns.description'],
      dataIndex: 'description',
    },
    {
      title: t['store.columns.state'],
      dataIndex: 'state',
      render: (_, record) => {
        switch (record.state) {
          case 'running':
            return <Tag
                    color="green"
                    size="small"
                  >
                    {t[`store.columns.state.running`]}
                  </Tag>;
          case 'exited':
            return <Tag
                    color="red"
                    size="small"
                  >
                    {t[`store.columns.state.exited`]}
                  </Tag>;
          default:
            return <Tag
                    color="gray"
                    size="small"
                  >
                    { t[`store.columns.state.${record.state}`] || record.state }
                  </Tag>
            
        }
      },
    },
    {
      title: t['store.columns.installed'],
      dataIndex: 'installed',
      render: (x) => (x === 1 
        ? <Badge status="success" text={ t['store.const.answer.yes'] } /> 
        : <Badge status="error" text={ t['store.const.answer.no'] } />
      ),
    },
    // {
    //   title: t['store.columns.upgradable'],
    //   dataIndex: 'upgradable',
    //   render: (x) => (x === 1 
    //     ? <Badge status="success" text={ t['store.const.answer.yes'] } /> 
    //     : <Badge status="error" text={ t['store.const.answer.no'] } />
    //   ),
    // },
    {
      title: t['store.columns.updatedAt'],
      dataIndex: 'updatedAt',
    },
    {
      title: t['store.columns.operation'],
      dataIndex: 'operation',
      headerCellStyle: { paddingLeft: '15px' },
      render: (_, record) => (
        <Space>
          { canUpdate(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'update')}
            >
              {t['store.columns.operation.update']}
            </Button>) : null
          }
          { 
            canUpgrade(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'upgrade')}
            >
              {t['store.columns.operation.upgrade']}
            </Button>) : null
          }
          { 
            canStart(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'start')}
            >
              {t['store.columns.operation.start']}
            </Button>)
            : canStop(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'stop')}
            >
              {t['store.columns.operation.stop']}
            </Button>) : null
          }
          { 
            canInstall(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'install')}
            >
              {t['store.columns.operation.install']}
            </Button>)
            : canUninstall(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'uninstall')}
            >
              {t['store.columns.operation.uninstall']}
            </Button>) : null
          }
          { canDownload(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'download')}
            >
              {t['store.columns.operation.download']}
            </Button>) : null
          }
          { canBackup(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'backup')}
            >
              {t['store.columns.operation.backup']}
            </Button>) : null
          }
          { canRestore(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'restore')}
            >
              {t['store.columns.operation.restore']}
            </Button>) : null
          }
          {/* <Button
              type="text"
              size="small"
              onClick={() => callback(record, 'republish')}
            >
              {t['store.columns.operation.republish']}
          </Button> */}
          { canDelete(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'delete')}
            >
              {t['store.columns.operation.delete']}
            </Button>) : null }
          { canExport(record) ? (<Button
              type="text"
              size="small"
              onClick={() => callback(record, 'export')}
            >
              {t['store.columns.operation.export']}
            </Button>) : null }
        </Space>
      ),
    },
  ];
}
