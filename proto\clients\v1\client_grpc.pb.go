// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: clients/v1/client.proto

package configsv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ClientService_ListClients_FullMethodName  = "/configs.v1.ClientService/ListClients"
	ClientService_SaveClient_FullMethodName   = "/configs.v1.ClientService/SaveClient"
	ClientService_GetCurrent_FullMethodName   = "/configs.v1.ClientService/GetCurrent"
	ClientService_SaveCurrent_FullMethodName  = "/configs.v1.ClientService/SaveCurrent"
	ClientService_DeleteClient_FullMethodName = "/configs.v1.ClientService/DeleteClient"
)

// ClientServiceClient is the client API for ClientService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ClientServiceClient interface {
	ListClients(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ListClientsResponse, error)
	SaveClient(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ClientResponse, error)
	GetCurrent(ctx context.Context, in *GetCurrentRequest, opts ...grpc.CallOption) (*ClientResponse, error)
	SaveCurrent(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ClientResponse, error)
	DeleteClient(ctx context.Context, in *DeleteClientRequest, opts ...grpc.CallOption) (*ClientResponse, error)
}

type clientServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewClientServiceClient(cc grpc.ClientConnInterface) ClientServiceClient {
	return &clientServiceClient{cc}
}

func (c *clientServiceClient) ListClients(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ListClientsResponse, error) {
	out := new(ListClientsResponse)
	err := c.cc.Invoke(ctx, ClientService_ListClients_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientServiceClient) SaveClient(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ClientResponse, error) {
	out := new(ClientResponse)
	err := c.cc.Invoke(ctx, ClientService_SaveClient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientServiceClient) GetCurrent(ctx context.Context, in *GetCurrentRequest, opts ...grpc.CallOption) (*ClientResponse, error) {
	out := new(ClientResponse)
	err := c.cc.Invoke(ctx, ClientService_GetCurrent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientServiceClient) SaveCurrent(ctx context.Context, in *Client, opts ...grpc.CallOption) (*ClientResponse, error) {
	out := new(ClientResponse)
	err := c.cc.Invoke(ctx, ClientService_SaveCurrent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientServiceClient) DeleteClient(ctx context.Context, in *DeleteClientRequest, opts ...grpc.CallOption) (*ClientResponse, error) {
	out := new(ClientResponse)
	err := c.cc.Invoke(ctx, ClientService_DeleteClient_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClientServiceServer is the server API for ClientService service.
// All implementations should embed UnimplementedClientServiceServer
// for forward compatibility
type ClientServiceServer interface {
	ListClients(context.Context, *Client) (*ListClientsResponse, error)
	SaveClient(context.Context, *Client) (*ClientResponse, error)
	GetCurrent(context.Context, *GetCurrentRequest) (*ClientResponse, error)
	SaveCurrent(context.Context, *Client) (*ClientResponse, error)
	DeleteClient(context.Context, *DeleteClientRequest) (*ClientResponse, error)
}

// UnimplementedClientServiceServer should be embedded to have forward compatible implementations.
type UnimplementedClientServiceServer struct {
}

func (UnimplementedClientServiceServer) ListClients(context.Context, *Client) (*ListClientsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClients not implemented")
}
func (UnimplementedClientServiceServer) SaveClient(context.Context, *Client) (*ClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveClient not implemented")
}
func (UnimplementedClientServiceServer) GetCurrent(context.Context, *GetCurrentRequest) (*ClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCurrent not implemented")
}
func (UnimplementedClientServiceServer) SaveCurrent(context.Context, *Client) (*ClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCurrent not implemented")
}
func (UnimplementedClientServiceServer) DeleteClient(context.Context, *DeleteClientRequest) (*ClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteClient not implemented")
}

// UnsafeClientServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ClientServiceServer will
// result in compilation errors.
type UnsafeClientServiceServer interface {
	mustEmbedUnimplementedClientServiceServer()
}

func RegisterClientServiceServer(s grpc.ServiceRegistrar, srv ClientServiceServer) {
	s.RegisterService(&ClientService_ServiceDesc, srv)
}

func _ClientService_ListClients_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Client)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientServiceServer).ListClients(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientService_ListClients_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientServiceServer).ListClients(ctx, req.(*Client))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientService_SaveClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Client)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientServiceServer).SaveClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientService_SaveClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientServiceServer).SaveClient(ctx, req.(*Client))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientService_GetCurrent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientServiceServer).GetCurrent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientService_GetCurrent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientServiceServer).GetCurrent(ctx, req.(*GetCurrentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientService_SaveCurrent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Client)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientServiceServer).SaveCurrent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientService_SaveCurrent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientServiceServer).SaveCurrent(ctx, req.(*Client))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientService_DeleteClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientServiceServer).DeleteClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClientService_DeleteClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientServiceServer).DeleteClient(ctx, req.(*DeleteClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ClientService_ServiceDesc is the grpc.ServiceDesc for ClientService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ClientService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "configs.v1.ClientService",
	HandlerType: (*ClientServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListClients",
			Handler:    _ClientService_ListClients_Handler,
		},
		{
			MethodName: "SaveClient",
			Handler:    _ClientService_SaveClient_Handler,
		},
		{
			MethodName: "GetCurrent",
			Handler:    _ClientService_GetCurrent_Handler,
		},
		{
			MethodName: "SaveCurrent",
			Handler:    _ClientService_SaveCurrent_Handler,
		},
		{
			MethodName: "DeleteClient",
			Handler:    _ClientService_DeleteClient_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "clients/v1/client.proto",
}
