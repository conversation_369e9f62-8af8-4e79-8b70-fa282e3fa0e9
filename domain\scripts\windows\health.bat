@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

set "C_NAME=%~1"
if "%C_NAME%"=="" (
    echo false
    exit /B 1
)

net start | find "%C_NAME%" >nul 2>&1
if !ERRORLEVEL! EQU 0 (
    echo true
    exit /B 0
)

wmic service get name | find "%C_NAME%" >nul 2>&1
if !ERRORLEVEL! EQU 0 (
    sc query "%C_NAME%" | find "STATE" | find "RUNNING" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo true
        exit /B 0
    )
)

echo false
exit /B 1