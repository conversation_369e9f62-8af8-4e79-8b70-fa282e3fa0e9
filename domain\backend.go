package domain

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/lib"
)

// Backend implements the protobuf interface
type Backend struct {
	mu *sync.RWMutex
}

// New initializes a new Backend struct.
func New() *Backend {
	return &Backend{
		mu: &sync.RWMutex{},
	}
}

func GetFromAndTo(fromStr string, toStr string) (time.Time, time.Time, error) {
	var err error
	var from time.Time
	var to time.Time
	if fromStr != "" {
		from, err = time.Parse("2006-01-02", fromStr)
		if err != nil {
			return from, to, err
		}
	}
	if toStr != "" {
		to, err = time.Parse("2006-01-02", toStr)
		if err != nil {
			return from, to, err
		}
	}
	if from == (time.Time{}) && to != (time.Time{}) {
		from = time.Date(to.Year(), to.Month(), to.Day(), 0, 0, 0, 0, time.Local)
		to = time.Date(to.Year(), to.Month(), to.Day(), 23, 59, 59, 0, time.Local)
	} else {
		now := time.Now()
		from = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
		to = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, time.Local)
	}
	if from.After(to) {
		return from, to, fmt.Errorf("from date should be earlier than to date")
	}
	return from, to, nil
}

func CheckServerConnection(addr string) bool {
	err := lib.CheckServerConnection(addr)
	if err != nil {
		slog.Error("Error: " + err.Error())
	}
	return err == nil
}
