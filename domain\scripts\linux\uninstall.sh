#!/bin/bash
C_NAME=$1
C_VERSION=$2
C_OS_TYPE=$3
C_OS_ARCH=$4
C_PKG_DIR=$5
if [ -z $C_VERSION ] || [ -z $C_NAME ] || [ -z $C_OS_TYPE ] || [ -z $C_OS_ARCH ] || [ -z $C_PKG_DIR ]; then
    echo "Usage: $0 name version os_type os_arch pkg_dir [is_sdk] [tar_root]"
    exit 1
fi
C_IS_SDK=${6:-'false'}
C_TAR_ROOT=${7:-/usr/local}/$C_NAME

C_HOME=$(echo $C_NAME | tr '[a-z]' '[A-Z]')_HOME
C_BASHRC=/etc/bashrc

# 设置环境变量
set_env()
{
    C_RC=$1
    if [ -f $C_RC ]; then
        sed -i "\# ais-server $C_NAME#d" $C_RC
        sed -i "\#^export $C_HOME=$C_TAR_ROOT#d" $C_RC
        if [ $C_IS_SDK == 'true' ]; then
            sed -i "\#^export PATH=\$$C_HOME#d" $C_RC
        fi
        sed -i "\# ais-server $C_NAME end#d" $C_RC
    fi
}

# 卸载
uninstall()
{
    if [ -d $C_TAR_ROOT ]; then
        rm -rf $C_TAR_ROOT
    fi
    set_env $C_BASHRC
}

uninstall