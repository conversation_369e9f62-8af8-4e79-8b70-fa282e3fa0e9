package domain

import "time"

func (log *Log) Count() (int64, error) {
	return Count(log)
}

func (log *Log) Page(page *int, size *int) ([]*Log, error) {
	db, err := GetDBInstance()
	if err != nil {
		return nil, err
	}
	var logs []*Log
	var findErr error
	if page != nil && size != nil {
		findErr = db.Order("created_at DESC").
			Offset(((*page) - 1) * (*size)).
			Limit(*size).Find(&logs).
			Error
	} else {
		findErr = db.Order("created_at DESC").Find(&logs).Error
	}
	if findErr != nil {
		return nil, findErr
	}
	return logs, nil
}

func (log *Log) List() ([]*Log, error) {
	var logs []*Log
	err := FindAll(&logs)
	if err != nil {
		return nil, err
	}
	return logs, nil
}

func (log *Log) Add() error {
	log.Id = 0
	return Add(log)
}

func (log *Log) DeleteAll() error {
	return DeleteAll(log)
}

func (log *Log) DeleteBefore(days int) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	d := time.Now().AddDate(0, 0, -days)
	err = db.Where("created_at < ?", d).Delete(log).Error
	if err != nil {
		return err
	}
	return nil
}
