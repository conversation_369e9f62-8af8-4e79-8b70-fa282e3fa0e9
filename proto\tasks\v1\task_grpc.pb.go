// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: tasks/v1/task.proto

package tasksv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TaskService_ExecuteTask_FullMethodName                 = "/tasks.v1.TaskService/ExecuteTask"
	TaskService_GetTask_FullMethodName                     = "/tasks.v1.TaskService/GetTask"
	TaskService_CreateTaskForUploadPackage_FullMethodName  = "/tasks.v1.TaskService/CreateTaskForUploadPackage"
	TaskService_ExecuteTaskForUploadPackage_FullMethodName = "/tasks.v1.TaskService/ExecuteTaskForUploadPackage"
	TaskService_CreateTaskForUpload_FullMethodName         = "/tasks.v1.TaskService/CreateTaskForUpload"
	TaskService_ExecuteTaskForUpload_FullMethodName        = "/tasks.v1.TaskService/ExecuteTaskForUpload"
	TaskService_CreateTaskForLocalImport_FullMethodName    = "/tasks.v1.TaskService/CreateTaskForLocalImport"
	TaskService_BrowseLocalFiles_FullMethodName            = "/tasks.v1.TaskService/BrowseLocalFiles"
	TaskService_GetSystemDrives_FullMethodName             = "/tasks.v1.TaskService/GetSystemDrives"
)

// TaskServiceClient is the client API for TaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TaskServiceClient interface {
	ExecuteTask(ctx context.Context, in *Task, opts ...grpc.CallOption) (*TaskResponse, error)
	GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	CreateTaskForUploadPackage(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	ExecuteTaskForUploadPackage(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	CreateTaskForUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	ExecuteTaskForUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	CreateTaskForLocalImport(ctx context.Context, in *LocalImportRequest, opts ...grpc.CallOption) (*TaskResponse, error)
	BrowseLocalFiles(ctx context.Context, in *BrowseFilesRequest, opts ...grpc.CallOption) (*BrowseFilesResponse, error)
	GetSystemDrives(ctx context.Context, in *GetDrivesRequest, opts ...grpc.CallOption) (*GetDrivesResponse, error)
}

type taskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTaskServiceClient(cc grpc.ClientConnInterface) TaskServiceClient {
	return &taskServiceClient{cc}
}

func (c *taskServiceClient) ExecuteTask(ctx context.Context, in *Task, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_ExecuteTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) GetTask(ctx context.Context, in *GetTaskRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_GetTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) CreateTaskForUploadPackage(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_CreateTaskForUploadPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) ExecuteTaskForUploadPackage(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_ExecuteTaskForUploadPackage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) CreateTaskForUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_CreateTaskForUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) ExecuteTaskForUpload(ctx context.Context, in *UploadRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_ExecuteTaskForUpload_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) CreateTaskForLocalImport(ctx context.Context, in *LocalImportRequest, opts ...grpc.CallOption) (*TaskResponse, error) {
	out := new(TaskResponse)
	err := c.cc.Invoke(ctx, TaskService_CreateTaskForLocalImport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) BrowseLocalFiles(ctx context.Context, in *BrowseFilesRequest, opts ...grpc.CallOption) (*BrowseFilesResponse, error) {
	out := new(BrowseFilesResponse)
	err := c.cc.Invoke(ctx, TaskService_BrowseLocalFiles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) GetSystemDrives(ctx context.Context, in *GetDrivesRequest, opts ...grpc.CallOption) (*GetDrivesResponse, error) {
	out := new(GetDrivesResponse)
	err := c.cc.Invoke(ctx, TaskService_GetSystemDrives_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaskServiceServer is the server API for TaskService service.
// All implementations should embed UnimplementedTaskServiceServer
// for forward compatibility
type TaskServiceServer interface {
	ExecuteTask(context.Context, *Task) (*TaskResponse, error)
	GetTask(context.Context, *GetTaskRequest) (*TaskResponse, error)
	CreateTaskForUploadPackage(context.Context, *UploadRequest) (*TaskResponse, error)
	ExecuteTaskForUploadPackage(context.Context, *UploadRequest) (*TaskResponse, error)
	CreateTaskForUpload(context.Context, *UploadRequest) (*TaskResponse, error)
	ExecuteTaskForUpload(context.Context, *UploadRequest) (*TaskResponse, error)
	CreateTaskForLocalImport(context.Context, *LocalImportRequest) (*TaskResponse, error)
	BrowseLocalFiles(context.Context, *BrowseFilesRequest) (*BrowseFilesResponse, error)
	GetSystemDrives(context.Context, *GetDrivesRequest) (*GetDrivesResponse, error)
}

// UnimplementedTaskServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTaskServiceServer struct {
}

func (UnimplementedTaskServiceServer) ExecuteTask(context.Context, *Task) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteTask not implemented")
}
func (UnimplementedTaskServiceServer) GetTask(context.Context, *GetTaskRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTask not implemented")
}
func (UnimplementedTaskServiceServer) CreateTaskForUploadPackage(context.Context, *UploadRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTaskForUploadPackage not implemented")
}
func (UnimplementedTaskServiceServer) ExecuteTaskForUploadPackage(context.Context, *UploadRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteTaskForUploadPackage not implemented")
}
func (UnimplementedTaskServiceServer) CreateTaskForUpload(context.Context, *UploadRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTaskForUpload not implemented")
}
func (UnimplementedTaskServiceServer) ExecuteTaskForUpload(context.Context, *UploadRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteTaskForUpload not implemented")
}
func (UnimplementedTaskServiceServer) CreateTaskForLocalImport(context.Context, *LocalImportRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTaskForLocalImport not implemented")
}
func (UnimplementedTaskServiceServer) BrowseLocalFiles(context.Context, *BrowseFilesRequest) (*BrowseFilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BrowseLocalFiles not implemented")
}
func (UnimplementedTaskServiceServer) GetSystemDrives(context.Context, *GetDrivesRequest) (*GetDrivesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemDrives not implemented")
}

// UnsafeTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TaskServiceServer will
// result in compilation errors.
type UnsafeTaskServiceServer interface {
	mustEmbedUnimplementedTaskServiceServer()
}

func RegisterTaskServiceServer(s grpc.ServiceRegistrar, srv TaskServiceServer) {
	s.RegisterService(&TaskService_ServiceDesc, srv)
}

func _TaskService_ExecuteTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).ExecuteTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_ExecuteTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).ExecuteTask(ctx, req.(*Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_GetTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).GetTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_GetTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).GetTask(ctx, req.(*GetTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_CreateTaskForUploadPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).CreateTaskForUploadPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_CreateTaskForUploadPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).CreateTaskForUploadPackage(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_ExecuteTaskForUploadPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).ExecuteTaskForUploadPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_ExecuteTaskForUploadPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).ExecuteTaskForUploadPackage(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_CreateTaskForUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).CreateTaskForUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_CreateTaskForUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).CreateTaskForUpload(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_ExecuteTaskForUpload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).ExecuteTaskForUpload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_ExecuteTaskForUpload_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).ExecuteTaskForUpload(ctx, req.(*UploadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_CreateTaskForLocalImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocalImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).CreateTaskForLocalImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_CreateTaskForLocalImport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).CreateTaskForLocalImport(ctx, req.(*LocalImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_BrowseLocalFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BrowseFilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).BrowseLocalFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_BrowseLocalFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).BrowseLocalFiles(ctx, req.(*BrowseFilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_GetSystemDrives_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDrivesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).GetSystemDrives(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_GetSystemDrives_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).GetSystemDrives(ctx, req.(*GetDrivesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TaskService_ServiceDesc is the grpc.ServiceDesc for TaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tasks.v1.TaskService",
	HandlerType: (*TaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExecuteTask",
			Handler:    _TaskService_ExecuteTask_Handler,
		},
		{
			MethodName: "GetTask",
			Handler:    _TaskService_GetTask_Handler,
		},
		{
			MethodName: "CreateTaskForUploadPackage",
			Handler:    _TaskService_CreateTaskForUploadPackage_Handler,
		},
		{
			MethodName: "ExecuteTaskForUploadPackage",
			Handler:    _TaskService_ExecuteTaskForUploadPackage_Handler,
		},
		{
			MethodName: "CreateTaskForUpload",
			Handler:    _TaskService_CreateTaskForUpload_Handler,
		},
		{
			MethodName: "ExecuteTaskForUpload",
			Handler:    _TaskService_ExecuteTaskForUpload_Handler,
		},
		{
			MethodName: "CreateTaskForLocalImport",
			Handler:    _TaskService_CreateTaskForLocalImport_Handler,
		},
		{
			MethodName: "BrowseLocalFiles",
			Handler:    _TaskService_BrowseLocalFiles_Handler,
		},
		{
			MethodName: "GetSystemDrives",
			Handler:    _TaskService_GetSystemDrives_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tasks/v1/task.proto",
}
