.container {
  .progressCard {
    margin-bottom: 16px;
    border: 1px solid var(--color-primary-light-3);
    background-color: var(--color-primary-light-1);
    
    .arco-card-body {
      padding: 16px;
    }
    
    .statusHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .timeInfo {
      margin-top: 8px;
      text-align: center;
    }
  }
  
  .detailsCard {
    background-color: var(--color-fill-1);
    border: 1px solid var(--color-border-2);
    
    .arco-card-body {
      padding: 16px;
    }
    
    .detailItem {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      > :first-child {
        min-width: 80px;
        margin-right: 12px;
      }
      
      > :last-child {
        flex: 1;
        text-align: right;
      }
    }
  }
}

.statusIcon {
  font-size: 18px;
  margin-right: 8px;
  
  &.success {
    color: var(--color-success-6);
  }
  
  &.error {
    color: var(--color-danger-6);
  }
  
  &.loading {
    color: var(--color-primary-6);
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.progressText {
  margin-top: 8px;
  text-align: center;
  font-size: 12px;
  color: var(--color-text-3);
}

.errorAlert {
  margin-bottom: 16px;
}

.completedCard {
  border-color: var(--color-success-light-3);
  background-color: var(--color-success-light-1);
}

.failedCard {
  border-color: var(--color-danger-light-3);
  background-color: var(--color-danger-light-1);
}