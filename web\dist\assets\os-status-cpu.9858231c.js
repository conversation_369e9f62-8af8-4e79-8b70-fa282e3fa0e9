import{u as a,b as o,j as s,s as e,T as t}from"./index.fe31dd41.js";import{l,D as r}from"./index.9905d9df.js";import{C as i}from"./index.41001b26.js";function n(n){const u=a(l),{data:c}=n,d=[{label:u["os.cpu.cores"],value:c.cpu.cores},{label:u["os.cpu.usage"],value:c.cpu.usage}];return o(i,{children:[s(e,{align:"start",children:s(t.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:u["os.cpu.title"]})}),s(r,{colon:": ",layout:"horizontal",data:d,column:2})]})}export{n as default};
