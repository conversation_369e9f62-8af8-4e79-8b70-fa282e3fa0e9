# AIS Server 构建指南

本文档介绍如何将 AIS Server 项目编译为可执行文件。

## 前置要求

### 1. 安装 Go 语言环境

**方法一：官方安装**
1. 访问 [Go 官方下载页面](https://golang.org/dl/)
2. 下载适合 Windows 的安装包（推荐最新稳定版）
3. 运行安装程序，按照提示完成安装
4. 重新打开命令行窗口

**方法二：使用 winget（推荐）**
```powershell
winget install GoLang.Go
```

**方法三：使用 Chocolatey**
```powershell
choco install golang
```

### 2. 验证安装
```bash
go version
```

应该显示类似输出：
```
go version go1.21.0 windows/amd64
```

## 构建方法

### 方法一：使用 PowerShell 脚本（推荐）

1. **设置执行策略**（首次使用需要）：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **运行构建脚本**：
   ```powershell
   .\build.ps1
   ```

### 方法二：使用批处理脚本

```cmd
build.bat
```

### 方法三：使用 Makefile

```bash
make build
```

### 方法四：手动构建

```bash
# 下载依赖
go mod download
go mod tidy

# 构建 Windows 版本
go build -ldflags "-s -w" -o ais-server-1.1.2-windows-amd64.exe

# 或者构建其他平台版本
# Linux 64位
GOOS=linux GOARCH=amd64 go build -o ais-server-1.1.2-linux-amd64

# macOS 64位
GOOS=darwin GOARCH=amd64 go build -o ais-server-1.1.2-darwin-amd64
```

## 构建选项说明

### 编译标志
- `-ldflags "-s -w"`：减小可执行文件大小
  - `-s`：去除符号表
  - `-w`：去除调试信息
- `-o filename`：指定输出文件名

### 环境变量
- `GOOS`：目标操作系统（windows, linux, darwin）
- `GOARCH`：目标架构（amd64, arm64, 386）
- `CGO_ENABLED`：是否启用 CGO（0=禁用，1=启用）

## 输出文件

构建成功后，会生成以下文件：
- `ais-server-1.1.2-windows-amd64.exe`：Windows 64位可执行文件
- `build/ais-server/ais-server.exe`：复制到构建目录的文件

## 测试构建结果

### 1. 查看帮助信息
```cmd
ais-server-1.1.2-windows-amd64.exe --help
```

### 2. 查看版本信息
```cmd
ais-server-1.1.2-windows-amd64.exe config
```

### 3. 安装为 Windows 服务
```cmd
ais-server-1.1.2-windows-amd64.exe install
```

### 4. 启动服务
```cmd
ais-server-1.1.2-windows-amd64.exe start
```

## 故障排除

### 1. Go 命令未找到
**错误信息**：`'go' 不是内部或外部命令`

**解决方法**：
- 确保已正确安装 Go
- 检查 PATH 环境变量是否包含 Go 的 bin 目录
- 重新打开命令行窗口

### 2. 依赖下载失败
**错误信息**：`go mod download` 失败

**解决方法**：
```bash
# 设置 Go 代理（中国用户）
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 或者使用官方代理
go env -w GOPROXY=https://proxy.golang.org,direct
```

### 3. 编译错误
**常见原因**：
- 代码语法错误
- 缺少依赖包
- Go 版本不兼容

**解决方法**：
```bash
# 检查代码
go vet ./...

# 格式化代码
go fmt ./...

# 更新依赖
go get -u ./...
go mod tidy
```

### 4. PowerShell 执行策略限制
**错误信息**：`无法加载文件 build.ps1，因为在此系统上禁止运行脚本`

**解决方法**：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 高级构建选项

### 1. 交叉编译
```bash
# 编译所有平台版本
# Windows
GOOS=windows GOARCH=amd64 go build -o ais-server-windows-amd64.exe
GOOS=windows GOARCH=386 go build -o ais-server-windows-386.exe

# Linux
GOOS=linux GOARCH=amd64 go build -o ais-server-linux-amd64
GOOS=linux GOARCH=arm64 go build -o ais-server-linux-arm64

# macOS
GOOS=darwin GOARCH=amd64 go build -o ais-server-darwin-amd64
GOOS=darwin GOARCH=arm64 go build -o ais-server-darwin-arm64
```

### 2. 添加版本信息
```bash
go build -ldflags "-s -w -X 'main.Version=1.1.2' -X 'main.BuildTime=$(date)'" -o ais-server.exe
```

### 3. 静态链接
```bash
CGO_ENABLED=0 go build -a -ldflags '-extldflags "-static"' -o ais-server.exe
```

## 自动化构建

### 使用 GitHub Actions
创建 `.github/workflows/build.yml`：

```yaml
name: Build

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        goos: [linux, windows, darwin]
        goarch: [amd64, arm64]
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-go@v3
      with:
        go-version: '1.21'
    - name: Build
      run: |
        GOOS=${{ matrix.goos }} GOARCH=${{ matrix.goarch }} go build -ldflags "-s -w" -o ais-server-${{ matrix.goos }}-${{ matrix.goarch }}${{ matrix.goos == 'windows' && '.exe' || '' }}
    - name: Upload
      uses: actions/upload-artifact@v3
      with:
        name: ais-server-${{ matrix.goos }}-${{ matrix.goarch }}
        path: ais-server-*
```

## 相关文件

- `build.ps1`：PowerShell 构建脚本
- `build.bat`：批处理构建脚本
- `Makefile`：Make 构建配置
- `go.mod`：Go 模块依赖
- `main.go`：主程序入口

## 技术支持

如果在构建过程中遇到问题，请：

1. 检查 Go 版本是否为 1.21+
2. 确保网络连接正常（用于下载依赖）
3. 查看构建日志中的错误信息
4. 参考本文档的故障排除部分

更多信息请参考：
- [Go 官方文档](https://golang.org/doc/)
- [Go 模块参考](https://golang.org/ref/mod)
- [交叉编译指南](https://golang.org/doc/install/source#environment)