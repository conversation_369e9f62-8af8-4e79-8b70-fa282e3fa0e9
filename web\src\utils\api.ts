import axios from 'axios';

export default function useApi(path: string, options: Record<string, any> = {}) {
    const { serverUrl = import.meta.env.VITE_GO_URL || '', method = 'get', data = {}, ...rest } = options;
    const url = `${serverUrl}${path}`;
    if (method === 'get') {
        return axios.get(url, { params: data, ...rest });
    } else if (method === 'delete') {
        return axios.delete(url, { params: data, ...rest });
    }
    return axios({
        url,
        method,
        data,
        ...rest,
    });
}