// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: sysinfos/v1/sysinfo.proto

package sysinfosv1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSysinfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSysinfoRequest) Reset() {
	*x = GetSysinfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSysinfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSysinfoRequest) ProtoMessage() {}

func (x *GetSysinfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSysinfoRequest.ProtoReflect.Descriptor instead.
func (*GetSysinfoRequest) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{0}
}

type GetSysinfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Data    *Sysinfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetSysinfoResponse) Reset() {
	*x = GetSysinfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSysinfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSysinfoResponse) ProtoMessage() {}

func (x *GetSysinfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSysinfoResponse.ProtoReflect.Descriptor instead.
func (*GetSysinfoResponse) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{1}
}

func (x *GetSysinfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetSysinfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetSysinfoResponse) GetData() *Sysinfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type OS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Arch    string `protobuf:"bytes,3,opt,name=arch,proto3" json:"arch,omitempty"`
}

func (x *OS) Reset() {
	*x = OS{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OS) ProtoMessage() {}

func (x *OS) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OS.ProtoReflect.Descriptor instead.
func (*OS) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{2}
}

func (x *OS) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OS) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *OS) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type CPU struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cores int32  `protobuf:"varint,1,opt,name=cores,proto3" json:"cores,omitempty"`
	Usage string `protobuf:"bytes,2,opt,name=usage,proto3" json:"usage,omitempty"`
}

func (x *CPU) Reset() {
	*x = CPU{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CPU) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPU) ProtoMessage() {}

func (x *CPU) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPU.ProtoReflect.Descriptor instead.
func (*CPU) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{3}
}

func (x *CPU) GetCores() int32 {
	if x != nil {
		return x.Cores
	}
	return 0
}

func (x *CPU) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

type Memory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total string `protobuf:"bytes,1,opt,name=total,proto3" json:"total,omitempty"`
	Free  string `protobuf:"bytes,2,opt,name=free,proto3" json:"free,omitempty"`
	Used  string `protobuf:"bytes,3,opt,name=used,proto3" json:"used,omitempty"`
	Usage string `protobuf:"bytes,4,opt,name=usage,proto3" json:"usage,omitempty"`
}

func (x *Memory) Reset() {
	*x = Memory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Memory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Memory) ProtoMessage() {}

func (x *Memory) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Memory.ProtoReflect.Descriptor instead.
func (*Memory) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{4}
}

func (x *Memory) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

func (x *Memory) GetFree() string {
	if x != nil {
		return x.Free
	}
	return ""
}

func (x *Memory) GetUsed() string {
	if x != nil {
		return x.Used
	}
	return ""
}

func (x *Memory) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

type Disk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Total string `protobuf:"bytes,2,opt,name=total,proto3" json:"total,omitempty"`
	Free  string `protobuf:"bytes,3,opt,name=free,proto3" json:"free,omitempty"`
	Used  string `protobuf:"bytes,4,opt,name=used,proto3" json:"used,omitempty"`
	Usage string `protobuf:"bytes,5,opt,name=usage,proto3" json:"usage,omitempty"`
}

func (x *Disk) Reset() {
	*x = Disk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Disk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Disk) ProtoMessage() {}

func (x *Disk) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Disk.ProtoReflect.Descriptor instead.
func (*Disk) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{5}
}

func (x *Disk) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Disk) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

func (x *Disk) GetFree() string {
	if x != nil {
		return x.Free
	}
	return ""
}

func (x *Disk) GetUsed() string {
	if x != nil {
		return x.Used
	}
	return ""
}

func (x *Disk) GetUsage() string {
	if x != nil {
		return x.Usage
	}
	return ""
}

type Times struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Up string `protobuf:"bytes,1,opt,name=up,proto3" json:"up,omitempty"`
}

func (x *Times) Reset() {
	*x = Times{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Times) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Times) ProtoMessage() {}

func (x *Times) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Times.ProtoReflect.Descriptor instead.
func (*Times) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{6}
}

func (x *Times) GetUp() string {
	if x != nil {
		return x.Up
	}
	return ""
}

type Network struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Family   string `protobuf:"bytes,2,opt,name=family,proto3" json:"family,omitempty"`
	Address  string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	Mac      string `protobuf:"bytes,4,opt,name=mac,proto3" json:"mac,omitempty"`
	Internal bool   `protobuf:"varint,5,opt,name=internal,proto3" json:"internal,omitempty"`
}

func (x *Network) Reset() {
	*x = Network{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Network) ProtoMessage() {}

func (x *Network) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Network.ProtoReflect.Descriptor instead.
func (*Network) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{7}
}

func (x *Network) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Network) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *Network) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Network) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *Network) GetInternal() bool {
	if x != nil {
		return x.Internal
	}
	return false
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version     string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	GrpcPort    int32  `protobuf:"varint,2,opt,name=grpc_port,json=grpcPort,proto3" json:"grpc_port,omitempty"`
	GatewayPort int32  `protobuf:"varint,3,opt,name=gateway_port,json=gatewayPort,proto3" json:"gateway_port,omitempty"`
	MqttTcpPort int32  `protobuf:"varint,4,opt,name=mqtt_tcp_port,json=mqttTcpPort,proto3" json:"mqtt_tcp_port,omitempty"`
	MqttWsPort  int32  `protobuf:"varint,5,opt,name=mqtt_ws_port,json=mqttWsPort,proto3" json:"mqtt_ws_port,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{8}
}

func (x *Server) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Server) GetGrpcPort() int32 {
	if x != nil {
		return x.GrpcPort
	}
	return 0
}

func (x *Server) GetGatewayPort() int32 {
	if x != nil {
		return x.GatewayPort
	}
	return 0
}

func (x *Server) GetMqttTcpPort() int32 {
	if x != nil {
		return x.MqttTcpPort
	}
	return 0
}

func (x *Server) GetMqttWsPort() int32 {
	if x != nil {
		return x.MqttWsPort
	}
	return 0
}

type Sysinfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os       *OS        `protobuf:"bytes,1,opt,name=os,proto3" json:"os,omitempty"`
	Cpu      *CPU       `protobuf:"bytes,2,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Memory   *Memory    `protobuf:"bytes,3,opt,name=memory,proto3" json:"memory,omitempty"`
	Disks    []*Disk    `protobuf:"bytes,4,rep,name=disks,proto3" json:"disks,omitempty"`
	Times    *Times     `protobuf:"bytes,5,opt,name=times,proto3" json:"times,omitempty"`
	Networks []*Network `protobuf:"bytes,6,rep,name=networks,proto3" json:"networks,omitempty"`
	Server   *Server    `protobuf:"bytes,7,opt,name=server,proto3" json:"server,omitempty"`
}

func (x *Sysinfo) Reset() {
	*x = Sysinfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Sysinfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sysinfo) ProtoMessage() {}

func (x *Sysinfo) ProtoReflect() protoreflect.Message {
	mi := &file_sysinfos_v1_sysinfo_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sysinfo.ProtoReflect.Descriptor instead.
func (*Sysinfo) Descriptor() ([]byte, []int) {
	return file_sysinfos_v1_sysinfo_proto_rawDescGZIP(), []int{9}
}

func (x *Sysinfo) GetOs() *OS {
	if x != nil {
		return x.Os
	}
	return nil
}

func (x *Sysinfo) GetCpu() *CPU {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *Sysinfo) GetMemory() *Memory {
	if x != nil {
		return x.Memory
	}
	return nil
}

func (x *Sysinfo) GetDisks() []*Disk {
	if x != nil {
		return x.Disks
	}
	return nil
}

func (x *Sysinfo) GetTimes() *Times {
	if x != nil {
		return x.Times
	}
	return nil
}

func (x *Sysinfo) GetNetworks() []*Network {
	if x != nil {
		return x.Networks
	}
	return nil
}

func (x *Sysinfo) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

var File_sysinfos_v1_sysinfo_proto protoreflect.FileDescriptor

var file_sysinfos_v1_sysinfo_proto_rawDesc = []byte{
	0x0a, 0x19, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x79,
	0x73, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x73, 0x79, 0x73,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67,
	0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6c, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x73, 0x69,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x46, 0x0a, 0x02, 0x4f, 0x53, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63,
	0x68, 0x22, 0x31, 0x0a, 0x03, 0x43, 0x50, 0x55, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x72, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x5c, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x65, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x6e, 0x0a, 0x04, 0x44, 0x69, 0x73, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x65, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x17, 0x0a, 0x05, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x75,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x75, 0x70, 0x22, 0x7d, 0x0a, 0x07, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61,
	0x6d, 0x69, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x61, 0x6d, 0x69,
	0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22, 0xa8, 0x01, 0x0a, 0x06, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x67, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x22, 0x0a, 0x0d, 0x6d, 0x71, 0x74, 0x74, 0x5f, 0x74, 0x63, 0x70, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x71, 0x74, 0x74, 0x54, 0x63, 0x70, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x71, 0x74, 0x74, 0x5f, 0x77, 0x73, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x71, 0x74, 0x74, 0x57,
	0x73, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xad, 0x02, 0x0a, 0x07, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x1f, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x53, 0x52, 0x02,
	0x6f, 0x73, 0x12, 0x22, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x50,
	0x55, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x2b, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x64, 0x69, 0x73, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x6b, 0x52, 0x05, 0x64, 0x69, 0x73, 0x6b, 0x73, 0x12, 0x28, 0x0a, 0x05,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x79,
	0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52,
	0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x08,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x32, 0xb9, 0x01, 0x0a, 0x0e, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74,
	0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x57, 0x92, 0x41, 0x3a, 0x0a, 0x06, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x47, 0x65, 0x74, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x1a, 0x17,
	0x47, 0x65, 0x74, 0x20, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x62, 0x01, 0x2a,
	0x12, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66,
	0x6f, 0x42, 0xfd, 0x01, 0x92, 0x41, 0x48, 0x12, 0x05, 0x32, 0x03, 0x31, 0x2e, 0x30, 0x2a, 0x01,
	0x02, 0x72, 0x3c, 0x0a, 0x0a, 0x41, 0x49, 0x53, 0x20, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x2e, 0x68, 0x74, 0x74, 0x70, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a,
	0x68, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x0a,
	0x0f, 0x63, 0x6f, 0x6d, 0x2e, 0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x76, 0x31,
	0x42, 0x0c, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x44, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f, 0x6e, 0x67, 0x69, 0x6e,
	0x66, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2f, 0x61,
	0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x73, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x79, 0x73, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x53, 0x58, 0x58, 0xaa, 0x02, 0x0b, 0x53,
	0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0b, 0x53, 0x79, 0x73,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x17, 0x53, 0x79, 0x73, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0xea, 0x02, 0x0c, 0x53, 0x79, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x3a, 0x3a, 0x56,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sysinfos_v1_sysinfo_proto_rawDescOnce sync.Once
	file_sysinfos_v1_sysinfo_proto_rawDescData = file_sysinfos_v1_sysinfo_proto_rawDesc
)

func file_sysinfos_v1_sysinfo_proto_rawDescGZIP() []byte {
	file_sysinfos_v1_sysinfo_proto_rawDescOnce.Do(func() {
		file_sysinfos_v1_sysinfo_proto_rawDescData = protoimpl.X.CompressGZIP(file_sysinfos_v1_sysinfo_proto_rawDescData)
	})
	return file_sysinfos_v1_sysinfo_proto_rawDescData
}

var file_sysinfos_v1_sysinfo_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_sysinfos_v1_sysinfo_proto_goTypes = []interface{}{
	(*GetSysinfoRequest)(nil),  // 0: sysinfos.v1.GetSysinfoRequest
	(*GetSysinfoResponse)(nil), // 1: sysinfos.v1.GetSysinfoResponse
	(*OS)(nil),                 // 2: sysinfos.v1.OS
	(*CPU)(nil),                // 3: sysinfos.v1.CPU
	(*Memory)(nil),             // 4: sysinfos.v1.Memory
	(*Disk)(nil),               // 5: sysinfos.v1.Disk
	(*Times)(nil),              // 6: sysinfos.v1.Times
	(*Network)(nil),            // 7: sysinfos.v1.Network
	(*Server)(nil),             // 8: sysinfos.v1.Server
	(*Sysinfo)(nil),            // 9: sysinfos.v1.Sysinfo
}
var file_sysinfos_v1_sysinfo_proto_depIdxs = []int32{
	9, // 0: sysinfos.v1.GetSysinfoResponse.data:type_name -> sysinfos.v1.Sysinfo
	2, // 1: sysinfos.v1.Sysinfo.os:type_name -> sysinfos.v1.OS
	3, // 2: sysinfos.v1.Sysinfo.cpu:type_name -> sysinfos.v1.CPU
	4, // 3: sysinfos.v1.Sysinfo.memory:type_name -> sysinfos.v1.Memory
	5, // 4: sysinfos.v1.Sysinfo.disks:type_name -> sysinfos.v1.Disk
	6, // 5: sysinfos.v1.Sysinfo.times:type_name -> sysinfos.v1.Times
	7, // 6: sysinfos.v1.Sysinfo.networks:type_name -> sysinfos.v1.Network
	8, // 7: sysinfos.v1.Sysinfo.server:type_name -> sysinfos.v1.Server
	0, // 8: sysinfos.v1.SysinfoService.GetSysinfo:input_type -> sysinfos.v1.GetSysinfoRequest
	1, // 9: sysinfos.v1.SysinfoService.GetSysinfo:output_type -> sysinfos.v1.GetSysinfoResponse
	9, // [9:10] is the sub-list for method output_type
	8, // [8:9] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_sysinfos_v1_sysinfo_proto_init() }
func file_sysinfos_v1_sysinfo_proto_init() {
	if File_sysinfos_v1_sysinfo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sysinfos_v1_sysinfo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSysinfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSysinfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OS); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CPU); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Memory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Disk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Times); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Network); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sysinfos_v1_sysinfo_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Sysinfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sysinfos_v1_sysinfo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sysinfos_v1_sysinfo_proto_goTypes,
		DependencyIndexes: file_sysinfos_v1_sysinfo_proto_depIdxs,
		MessageInfos:      file_sysinfos_v1_sysinfo_proto_msgTypes,
	}.Build()
	File_sysinfos_v1_sysinfo_proto = out.File
	file_sysinfos_v1_sysinfo_proto_rawDesc = nil
	file_sysinfos_v1_sysinfo_proto_goTypes = nil
	file_sysinfos_v1_sysinfo_proto_depIdxs = nil
}
