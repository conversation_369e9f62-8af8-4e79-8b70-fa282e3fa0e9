const i18n = {
  'en-US': {
    'softwareSetting.name': 'Name',
    'softwareSetting.name.placeholder': 'Please enter the name',
    'softwareSetting.host': 'Host',
    'softwareSetting.host.placeholder': 'Please enter the host address, such as: 127.0.0.1',
    'softwareSetting.ssl': 'SSL',
    'softwareSetting.serverUrl': 'Server URL',
    'softwareSetting.serverUrl.placeholder': 'Please enter the server URL',
    'softwareSetting.cacheDir': 'Cache Directory',
    'softwareSetting.storageDir': 'Storage Directory',
    'softwareSetting.logDir': 'Logs Directory',
    'softwareSetting.autoStart': 'Auto Start',
    'softwareSetting.version': 'Software Version',
    'softwareSetting.autoReload': 'Server URL changed, auto reload in 3s',
    'softwareSetting.operations.reset': 'Reset',
    'softwareSetting.operations.save': 'Save',
    'softwareSetting.operations.save.success': 'Save successfully',
  },

  'zh-CN': {
    'softwareSetting.name': '名称',
    'softwareSetting.name.placeholder': '请输入名称',
    'softwareSetting.host': '主机地址',
    'softwareSetting.host.placeholder': '请输入主机地址，如：127.0.0.1',
    'softwareSetting.ssl': 'SSL',
    'softwareSetting.serverUrl': '服务地址',
    'softwareSetting.serverUrl.placeholder': '请输入服务端地址',
    'softwareSetting.cacheDir': '缓存目录',
    'softwareSetting.storageDir': '存储目录',
    'softwareSetting.logDir': '日志目录',
    'softwareSetting.autoStart': '开机自启',
    'softwareSetting.version': '软件版本',
    'softwareSetting.autoReload': '服务地址变更，3s后自动刷新',
    'softwareSetting.operations.reset': '重置',
    'softwareSetting.operations.save': '保存',
    'softwareSetting.operations.save.success': '保存成功',
    'softwareSetting.operations.save.fail': '保存失败',
  },
};

export default i18n;
