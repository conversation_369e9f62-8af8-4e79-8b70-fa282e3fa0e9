package domain

import (
	"fmt"
	"time"
)

func (pkg *Package) Add() error {
	if pkg.CreatedAt == (time.Time{}) {
		pkg.CreatedAt = time.Now()
	}
	if pkg.UpdatedAt == (time.Time{}) {
		pkg.UpdatedAt = time.Now()
	}
	return Add(pkg)
}

func (pkg *Package) Count() (int64, error) {
	return Count(pkg)
}

func (pkg *Package) CountByNameAndVersion() (int64, error) {
	if pkg.Name == "" {
		return 0, fmt.Errorf("name is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return 0, err
	}
	var count int64
	query := db.Model(pkg).Where("name = ?", pkg.Name)
	if pkg.Version != "" {
		query = query.Where("version = ?", pkg.Version)
	}
	err = query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (pkg *Package) GetById() error {
	err := FindOneByID(pkg.Id, pkg)
	if err != nil {
		return err
	}
	return nil
}

func (pkg *Package) GetByUniqueIndex() error {
	if pkg.Name == "" || pkg.Version == "" {
		return fmt.Errorf("name or version is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Where("name = ? AND version = ?", pkg.Name, pkg.Version).First(&pkg).Error
	if err != nil {
		return err
	}
	return nil
}

func (pkg *Package) GetByName() error {
	if pkg.Name == "" {
		return fmt.Errorf("name is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Where("name = ?", pkg.Name).First(&pkg).Error
	if err != nil {
		return err
	}
	return nil
}

func (pkg *Package) List() ([]*Package, error) {
	var pkgs []*Package
	err := FindAll(&pkgs)
	if err != nil {
		return nil, err
	}
	return pkgs, nil
}

func (pkg *Package) ListByNameAndVersion() ([]*Package, error) {
	if pkg.Name == "" {
		return nil, fmt.Errorf("name is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return nil, err
	}
	var pkgs []*Package
	query := db.Where("name = ?", pkg.Name)
	if pkg.Version != "" {
		query = query.Where("version = ?", pkg.Version)
	}
	err = query.Find(&pkgs).Error
	if err != nil {
		return nil, err
	}
	return pkgs, nil
}

func (pkg *Package) Update() error {
	if pkg.Id == 0 {
		return fmt.Errorf("id is required")
	}
	if pkg.UpdatedAt == (time.Time{}) {
		pkg.UpdatedAt = time.Now()
	}
	return Save(pkg)
}

func (pkg *Package) Save() error {
	return Save(pkg)
}

func (pkg *Package) SaveAll(pkgs []*Package) error {
	return Save(pkgs)
}
