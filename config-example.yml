# AIS Server 配置文件示例
# 复制此文件为 config.yml 并根据需要修改配置

# 服务端口配置
grpc_port: 5555
gateway_port: 5888
mqtt_tcp_port: 5883
mqtt_ws_port: 5884

# SSL 配置
ssl: false

# 日志级别 (DEBUG, INFO, WARN, ERROR)
log_level: INFO

# 目录配置
cache_dir: "/tmp/ais-cache"
storage_dir: "~/.ais"
database_file: "~/.ais/ais.db"

# 解压缩性能配置
# 解压缩线程数 (0 = 使用所有 CPU 核心)
extract_threads: 0

# 解压缩队列大小
extract_queue_size: 10

# 大文件阈值 (字节，默认 100MB)
# 大于此大小的文件将使用高性能解压缩
extract_large_file: 104857600

# 是否启用高性能解压缩模式
# true: 使用 xtractr 库进行多线程解压缩
# false: 使用 Go 标准库进行解压缩
extract_high_perf: true