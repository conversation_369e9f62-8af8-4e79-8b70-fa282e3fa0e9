import{u as a,r as e,b as t,j as s,i as o,k as n,l as i}from"./index.bbeb3af6.js";import{C as r}from"./index.cf9faf12.js";import{T as c}from"./index.b90074ae.js";import{i as d,S as l}from"./search-form.64b248b6.js";import{getColumns as m}from"./constants.81cbd012.js";import"./index.9dee27c6.js";function p(){const p=a(d),f=async(a,e)=>{if("delete"===e)n.confirm({title:p["patch.tables.main.modal.delete.title"],content:p["patch.tables.main.modal.delete.content"],onOk:()=>{g(!0),i("/api/v1/patches",{method:"delete",data:{name:a.name,packageName:a.packageName}}).then((()=>{o.success(p["patch.tables.main.modal.delete.operation.success"]),y()})).catch((()=>{o.error(p["patch.tables.main.modal.delete.operation.fail"])})).finally((()=>{g(!1)}))}});else o.warning(p["patch.tables.main.operation.no-support"])},u=e.exports.useMemo((()=>m(p,f)),[p]),[h,b]=e.exports.useState([]),[x,g]=e.exports.useState(!0),[j,S]=e.exports.useState({});function y(){g(!0),i("/api/v1/patches",{data:{...j}}).then((a=>{const e=a&&a.data;(e||e.data||e.data)&&b(e.data)})).finally((()=>{g(!1)}))}return e.exports.useEffect((()=>{y()}),[JSON.stringify(j)]),t(r,{children:[s(l,{loading:x,onSearch:function(a){S(a)}}),s(c,{rowKey:"id",loading:x,columns:u,data:h,pagination:!1})]})}export{p as default};
