syntax = "proto3";

package logs.v1;

import "google/api/annotations.proto";
import "google/protobuf/wrappers.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service LogService {
  rpc ListLogs(ListLogsRequest) returns (ListLogsResponse) {
    option (google.api.http) = {
      get: "/api/v1/logs"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List logs"
      description: "List logs from the server."
      tags: "Logs"
    };
  }

  rpc SaveLog(Log) returns (SaveLogResponse) {
    option (google.api.http) = {
      post: "/api/v1/logs"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Save log"
      description: "Save log to the server."
      tags: "Logs"
    };
  }

  rpc ClearLogs(ClearLogsRequest) returns (ClearLogsResponse) {
    option (google.api.http) = {
      delete: "/api/v1/logs"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Clear logs"
      description: "Clear logs from the server."
      tags: "Logs"
    };
  }
}

message SaveLogResponse {
  int32 code = 1;
  string message = 2;
  Log data = 3;
}

message ListLogsRequest {
  int32 page = 1;
  int32 size = 2;
}

message ListLogsResponse {
  int32 code = 1;
  string message = 2;
  ListLogsResponseData data = 3;
}

message ListLogsResponseData {
  int32 total = 1;
  int32 page = 2;
  int32 size = 3;
  repeated Log content = 4;
}

message ClearLogsRequest {
}

message ClearLogsResponse {
  int32 code = 1;
  string message = 2;
}

message Log {
  int64 id = 1;
  string level = 2;
  string message = 3;
  string created_at = 4;
}