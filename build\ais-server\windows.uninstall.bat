@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: Set color and title
title AIS Server Uninstaller
color 0E

:: Initialize variables
set "UNINSTALL_SUCCESS=0"
set "START_TIME=%TIME%"
set "LOG_FILE=%~dp0uninstall.log"

:: Create log file
echo ================================================ > "%LOG_FILE%"
echo AIS Server Uninstallation Log >> "%LOG_FILE%"
echo Start time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

echo.
echo ================================================
echo           AIS Server Uninstaller
echo ================================================
echo Start time: %DATE% %TIME%
echo.

:: BatchGotAdmin
:-------------------------------------
REM  --> Check for permissions
echo [INFO] Checking administrator privileges... >> "%LOG_FILE%"
echo [INFO] Checking administrator privileges...
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

REM --> If error flag set, we do not have admin.
if '%ERRORLEVEL%' NEQ '0' (
    echo [WARNING] Administrator privileges required, requesting elevation... >> "%LOG_FILE%"
    echo [WARNING] Administrator privileges required, requesting elevation...
    goto UACPrompt
) else ( 
    echo [SUCCESS] Administrator privileges obtained >> "%LOG_FILE%"
    echo [SUCCESS] Administrator privileges obtained
    goto gotAdmin 
)

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "%~1 %~2 %~3 %~4 %~5 %~6 %~7", "", "runas", 1 >> "%temp%\getadmin.vbs"

    if exist "%temp%\getadmin.vbs" ( "%temp%\getadmin.vbs" )
    exit /B

:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
:--------------------------------------

:: Parse command line parameters
echo [INFO] Parsing uninstallation parameters... >> "%LOG_FILE%"
echo [INFO] Parsing uninstallation parameters...

set "C_NAME=%~1"
if "%C_NAME%"=="" (
    set "C_NAME=ais-server"
)
set "C_VERSION=%~2"
if "%C_VERSION%"=="" (
    set "C_VERSION=1.0.0"
)
set "C_OS_TYPE=%~3"
if "%C_OS_TYPE%"=="" (
    set "C_OS_TYPE=windows"
)
set "C_OS_ARCH=%~4"
if "%C_OS_ARCH%"=="" (
    set "C_OS_ARCH=amd64"
)
set "C_PKG_DIR=%~5"
if "%C_PKG_DIR%"=="" (
    set "C_PKG_DIR=%~dp0"
)

set "C_PROGRAM_EXE=%C_PKG_DIR%\%C_NAME%.exe"

echo [PARAM] Service name: %C_NAME% >> "%LOG_FILE%"
echo [PARAM] Version: %C_VERSION% >> "%LOG_FILE%"
echo [PARAM] Operating system: %C_OS_TYPE% >> "%LOG_FILE%"
echo [PARAM] Architecture: %C_OS_ARCH% >> "%LOG_FILE%"
echo [PARAM] Program directory: %C_PKG_DIR% >> "%LOG_FILE%"
echo [PARAM] Executable file: %C_PROGRAM_EXE% >> "%LOG_FILE%"

echo [PARAM] Service name: %C_NAME%
echo [PARAM] Version: %C_VERSION%
echo [PARAM] Program directory: %C_PKG_DIR%
echo.

:: Check if executable file exists
echo [INFO] Checking executable file... >> "%LOG_FILE%"
echo [INFO] Checking executable file: %C_PROGRAM_EXE%
if not exist "%C_PROGRAM_EXE%" (
    echo [ERROR] Executable file not found: %C_PROGRAM_EXE% >> "%LOG_FILE%"
    echo [ERROR] Executable file not found: %C_PROGRAM_EXE%
    echo [WARNING] Cannot find program file, service may have been manually deleted
    echo.
    goto :uninstall_failed
)
echo [SUCCESS] Executable file check passed >> "%LOG_FILE%"
echo [SUCCESS] Executable file check passed
echo.

:: Stop service
echo [INFO] Stopping service... >> "%LOG_FILE%"
echo [INFO] Stopping service...
echo Executing command: "%C_PROGRAM_EXE%" stop
"%C_PROGRAM_EXE%" stop
set "STOP_RESULT=%ERRORLEVEL%"
echo [RESULT] Stop command return code: %STOP_RESULT% >> "%LOG_FILE%"

if %STOP_RESULT% NEQ 0 (
    echo [WARNING] Service stop failed, return code: %STOP_RESULT% >> "%LOG_FILE%"
    echo [WARNING] Service stop failed, return code: %STOP_RESULT%
    echo [WARNING] Service may already be stopped or does not exist, continuing uninstallation...
) else (
    echo [SUCCESS] Service stop completed >> "%LOG_FILE%"
    echo [SUCCESS] Service stop completed
)
echo.

:: Uninstall service
echo [INFO] Uninstalling service... >> "%LOG_FILE%"
echo [INFO] Uninstalling service...
echo Executing command: "%C_PROGRAM_EXE%" uninstall
"%C_PROGRAM_EXE%" uninstall
set "UNINSTALL_RESULT=%ERRORLEVEL%"
echo [RESULT] Uninstall command return code: %UNINSTALL_RESULT% >> "%LOG_FILE%"

if %UNINSTALL_RESULT% NEQ 0 (
    echo [ERROR] Service uninstallation failed, return code: %UNINSTALL_RESULT% >> "%LOG_FILE%"
    echo [ERROR] Service uninstallation failed, return code: %UNINSTALL_RESULT%
    goto :uninstall_failed
)
echo [SUCCESS] Service uninstallation completed >> "%LOG_FILE%"
echo [SUCCESS] Service uninstallation completed
echo.

:: Uninstallation fully successful
set "UNINSTALL_SUCCESS=1"
goto :uninstall_success

:uninstall_failed
echo.
echo ================================================
echo [FAILED] AIS Server uninstallation failed!
echo ================================================
echo Failure time: %DATE% %TIME% >> "%LOG_FILE%"
echo [FAILED] Error occurred during uninstallation >> "%LOG_FILE%"
color 0C
goto :cleanup

:uninstall_success
echo.
echo ================================================
echo [SUCCESS] AIS Server uninstallation completed!
echo ================================================
echo Service name: %C_NAME%
echo Version: %C_VERSION%
echo Program directory: %C_PKG_DIR%
echo.
echo Service successfully stopped and uninstalled
echo Completion time: %DATE% %TIME% >> "%LOG_FILE%"
echo [SUCCESS] Stop and uninstallation fully completed >> "%LOG_FILE%"
color 0A
goto :cleanup

:cleanup
echo.
echo Log file: %LOG_FILE%
echo End time: %DATE% %TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"
echo.

if %UNINSTALL_SUCCESS% EQU 1 (
    echo Uninstallation successful! Window will close automatically in 5 seconds...
    timeout /t 5 /nobreak >nul
) else (
    echo Uninstallation not fully successful, window will close automatically in 60 seconds...
    echo Please check the log file for detailed information: %LOG_FILE%
    timeout /t 60 /nobreak >nul
)
exit %UNINSTALL_SUCCESS%