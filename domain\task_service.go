package domain

import (
	"archive/zip"
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
)

func (t *Task) AddForUpload(name string, version string) error {
	cacheDir, err := config.GetCacheDir()
	if err != nil {
		return err
	}
	pkg := &Package{Name: name, Version: version}
	err = pkg.GetByUniqueIndex()
	if err == nil {
		t.PackageId = pkg.Id
	}
	location := fmt.Sprintf("%s/%s_%s_%d.zip", cacheDir, name, version, time.Now().UnixNano())
	t.Name = TaskNames.Upload
	t.Type = TaskTypes.File
	t.Content = location
	t.Progress = "0.00"
	t.State = TaskStates.Running
	err = t.Add()
	if err != nil {
		return err
	}
	return nil
}

func (t *Task) MergeUpload(totalChunks int32) error {
	if t.Id == 0 && t.Content == "" {
		return fmt.Errorf("task not exists")
	}
	uri := t.Content
	if _, err := os.Stat(uri); !os.IsNotExist(err) {
		err = os.Remove(uri)
		if err != nil {
			return err
		} else {
			slog.Debug(fmt.Sprintf("[Task(%d):MergeUpload] Remove %s", t.Id, uri))
		}
	}
	totalSize := 0
	fullFile, err := os.OpenFile(uri, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		return err
	}
	defer fullFile.Close()
	for i := 0; i < int(totalChunks); i++ {
		tmpFilename := fmt.Sprintf("%s_%d", uri, i)
		if stat, err := os.Stat(tmpFilename); !os.IsNotExist(err) {
			totalSize += int(stat.Size())
		}
		tmpFile, err := os.OpenFile(tmpFilename, os.O_RDONLY, 0600)
		if err != nil {
			return err
		}
		_, err = io.Copy(fullFile, tmpFile)
		if err != nil {
			return err
		}
		tmpFile.Close()
		err = os.Remove(tmpFilename)
		if err != nil {
			return err
		}
		slog.Debug(fmt.Sprintf("[Task(%d):MergeUpload] Write temp file %s to %s", t.Id, tmpFilename, uri))
	}
	// 判断uri是否存在
	if _, err := os.Stat(uri); os.IsNotExist(err) {
		return fmt.Errorf("file not exists")
	}
	t.State = TaskStates.Finished
	t.Progress = "100.00"
	err = t.Update()
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Task(%d):MergeUpload] Update successful", t.Id))
	return nil
}

// ImportLocalPackage 导入本地文件，参考文件上传逻辑但跳过分片上传
// 如果version为"auto"，则从压缩包中自动解析版本信息
func (t *Task) ImportLocalPackage(name string, version string, localPath string) error {
	slog.Info(fmt.Sprintf("[本地导入] 开始导入本地包 - 包名: %s, 版本: %s, 本地路径: %s", name, version, localPath))
	
	// 检查本地文件是否存在
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		errorMsg := fmt.Sprintf("本地文件不存在: %s", localPath)
		slog.Error(fmt.Sprintf("[本地导入] %s", errorMsg))
		t.ErrorLog = errorMsg
		t.State = TaskStates.Failed
		t.Update()
		return fmt.Errorf(errorMsg)
	}

	// 如果版本为auto，尝试从压缩包中解析版本信息
	if version == "auto" {
		parsedVersion, err := parseVersionFromZip(localPath)
		if err != nil {
			slog.Warn(fmt.Sprintf("[本地导入] 无法从压缩包解析版本，使用默认版本1.0.0: %v", err))
			version = "1.0.0"
		} else {
			version = parsedVersion
			slog.Info(fmt.Sprintf("[本地导入] 从压缩包解析到版本: %s", version))
		}
	}
	
	// 参考AddForUploadPackage的逻辑
	pkg := &Package{Name: name, Version: version}
	location, err := pkg.GetLocation()
	t.Name = TaskNames.Upload
	t.Type = TaskTypes.File
	if err != nil {
		t.State = TaskStates.Failed
		t.ErrorLog = fmt.Sprintf("获取包存储位置失败: %v", err)
	} else {
		t.Content = *location
		t.Progress = "0.00"
		t.State = TaskStates.Running
	}
	err = t.Add()
	if err != nil {
		errorMsg := fmt.Sprintf("创建任务失败: %v", err)
		slog.Error(fmt.Sprintf("[本地导入] %s", errorMsg))
		t.ErrorLog = errorMsg
		t.State = TaskStates.Failed
		t.Update()
		return fmt.Errorf(errorMsg)
	}
	
	slog.Info(fmt.Sprintf("[本地导入] 任务创建成功 - 任务ID: %d, 存储位置: %s", t.Id, *location))
	
	// 直接复制本地文件到目标位置（不添加.zip后缀）
	err = t.copyLocalFile(localPath, *location)
	if err != nil {
		errorMsg := fmt.Sprintf("文件复制失败: %v", err)
		slog.Error(fmt.Sprintf("[本地导入] %s", errorMsg))
		t.State = TaskStates.Failed
		t.ErrorLog = errorMsg
		t.Update()
		return fmt.Errorf(errorMsg)
	}
	
	// 更新任务进度为100%
	t.Progress = "100.00"
	t.State = TaskStates.Running // 保持运行状态，等待解压完成
	err = t.Update()
	if err != nil {
		slog.Error(fmt.Sprintf("[本地导入] 更新任务状态失败: %v", err))
	}
	
	slog.Info(fmt.Sprintf("[本地导入] 文件复制完成 - 任务ID: %d, 包名: %s", t.Id, name))
	
	// 参考MergePackage的解压逻辑
	err = t.performLocalExtraction(*location)
	if err != nil {
		errorMsg := fmt.Sprintf("解压失败: %v", err)
		slog.Error(fmt.Sprintf("[本地导入] %s", errorMsg))
		t.State = TaskStates.Failed
		t.ErrorLog = errorMsg
		t.Update()
		return fmt.Errorf(errorMsg)
	}
	
	// 设置任务完成状态
	t.State = TaskStates.Finished
	err = t.Update()
	if err != nil {
		slog.Error(fmt.Sprintf("[本地导入] 更新任务完成状态失败: %v", err))
	}
	
	slog.Info(fmt.Sprintf("[本地导入] 导入完成 - 任务ID: %d, 包名: %s, 包ID: %d", t.Id, name, t.PackageId))
	return nil
}

// parseVersionFromZip 从压缩包中解析版本信息
// 尝试从压缩包中的配置文件（如package.json、version.txt等）解析版本
func parseVersionFromZip(zipPath string) (string, error) {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return "", fmt.Errorf("无法打开压缩包: %v", err)
	}
	defer reader.Close()

	// 查找可能包含版本信息的文件
	for _, file := range reader.File {
		// 检查ais-cli.yml文件（优先级最高）
		if strings.HasSuffix(file.Name, "ais-cli.yml") || strings.HasSuffix(file.Name, "ais-cli.yaml") {
			version, err := parseVersionFromAisCliYml(file)
			if err == nil && version != "" {
				return version, nil
			}
		}
		// 检查package.json文件
		if strings.HasSuffix(file.Name, "package.json") {
			version, err := parseVersionFromPackageJson(file)
			if err == nil && version != "" {
				return version, nil
			}
		}
		// 检查version.txt文件
		if strings.HasSuffix(file.Name, "version.txt") || strings.HasSuffix(file.Name, "VERSION") {
			version, err := parseVersionFromTextFile(file)
			if err == nil && version != "" {
				return version, nil
			}
		}
		// 检查manifest.json或config.json文件
		if strings.HasSuffix(file.Name, "manifest.json") || strings.HasSuffix(file.Name, "config.json") {
			version, err := parseVersionFromJsonFile(file)
			if err == nil && version != "" {
				return version, nil
			}
		}
	}

	return "", fmt.Errorf("未找到版本信息")
}

// parseVersionFromPackageJson 从package.json文件解析版本
func parseVersionFromPackageJson(file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	data, err := io.ReadAll(rc)
	if err != nil {
		return "", err
	}

	var pkg struct {
		Version string `json:"version"`
	}

	if err := json.Unmarshal(data, &pkg); err != nil {
		return "", err
	}

	return pkg.Version, nil
}

// parseVersionFromTextFile 从文本文件解析版本
func parseVersionFromTextFile(file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	data, err := io.ReadAll(rc)
	if err != nil {
		return "", err
	}

	version := strings.TrimSpace(string(data))
	if version == "" {
		return "", fmt.Errorf("版本文件为空")
	}

	return version, nil
}

// parseVersionFromAisCliYml 从ais-cli.yml文件解析版本
func parseVersionFromAisCliYml(file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	data, err := io.ReadAll(rc)
	if err != nil {
		return "", err
	}

	// 解析YAML内容，查找version字段
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 匹配 version: 或 version = 格式
		if strings.HasPrefix(line, "version:") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				version := strings.TrimSpace(parts[1])
				// 移除引号
				version = strings.Trim(version, `"'`)
				if version != "" {
					return version, nil
				}
			}
		}
		// 匹配 version = 格式
		if strings.Contains(line, "version") && strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 && strings.TrimSpace(parts[0]) == "version" {
				version := strings.TrimSpace(parts[1])
				// 移除引号
				version = strings.Trim(version, `"'`)
				if version != "" {
					return version, nil
				}
			}
		}
	}

	return "", fmt.Errorf("未找到版本字段")
}

// parseVersionFromJsonFile 从JSON配置文件解析版本
func parseVersionFromJsonFile(file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()

	data, err := io.ReadAll(rc)
	if err != nil {
		return "", err
	}

	var config map[string]interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return "", err
	}

	// 尝试多种可能的版本字段名
	versionFields := []string{"version", "Version", "app_version", "appVersion", "build_version", "buildVersion"}
	for _, field := range versionFields {
		if version, ok := config[field].(string); ok && version != "" {
			return version, nil
		}
	}

	return "", fmt.Errorf("未找到版本字段")
}

// performLocalExtraction 执行本地文件解压，参考MergePackage的逻辑
func (t *Task) performLocalExtraction(uri string) error {
	startTime := time.Now()
	slog.Info(fmt.Sprintf("[本地导入] 开始解压文件: %s", uri))
	
	// 获取文件大小
	stat, err := os.Stat(uri)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %v", err)
	}
	totalSize := int(stat.Size())
	
	// 获取包目录
	pkgsDir, err := config.GetPackagesDir()
	if err != nil {
		return err
	}
	
	// 取目录的最后一级的上级目录名作为包名，例如：/Users/<USER>/.ais/store/mosquitto/1717496482026，包名为mosquitto
	parentDir := filepath.Dir(uri)
	pkgName := filepath.Base(parentDir)
	pkg := &Package{Name: pkgName, Uri: uri, Size: int32(totalSize)}
	
	// 使用高性能解压，根据文件大小自动选择最优方式
	err = lib.UnzipAuto(uri, pkgsDir)
	if err != nil {
		// 如果高性能解压失败，回退到优化的标准库解压
		slog.Warn(fmt.Sprintf("[解压] 高性能解压失败，回退到优化标准库: %v", err))
		t.ExtractProgress = "5.00"
		t.Update()
		
		// 首先尝试优化的标准库解压
		err = lib.UnzipOptimized(uri, pkgsDir, func(progress float64, message string) {
			t.ExtractProgress = fmt.Sprintf("%.2f", progress)
			t.Update()
			slog.Info(fmt.Sprintf("[解压] 任务ID: %d, 进度: %.1f%% - %s", t.Id, progress, message))
		})
		
		if err != nil {
			// 如果优化解压也失败，使用标准解压
			slog.Warn(fmt.Sprintf("[解压] 优化解压失败，使用标准解压: %v", err))
			err = lib.UnzipWithProgress(uri, pkgsDir, func(progress float64, message string) {
				t.ExtractProgress = fmt.Sprintf("%.2f", progress)
				t.Update()
				slog.Info(fmt.Sprintf("[解压] 任务ID: %d, 进度: %.1f%% - %s", t.Id, progress, message))
			})
			
			if err != nil {
				errorMsg := fmt.Sprintf("解压文件失败: %v", err)
				slog.Error(fmt.Sprintf("[解压] %s - 文件: %s, 目标目录: %s", errorMsg, uri, pkgsDir))
				t.ErrorLog = errorMsg
				t.State = TaskStates.Failed
				t.Update()
				return err
			}
		}
	} else {
		// 高性能解压成功，设置完成进度
		t.ExtractProgress = "100.00"
		t.Update()
	}
	
	slog.Debug(fmt.Sprintf("[Task(%d):performLocalExtraction] Unzip %s to %s", t.Id, uri, pkgsDir))
	
	// 加载包配置
	err = pkg.Load()
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Task(%d):performLocalExtraction] Load package config successful, %s %s", t.Id, pkg.Name, pkg.Version))
	
	// 检查是否已存在同名包
	oldPkg := &Package{Name: pkg.Name}
	err = oldPkg.GetByName()
	if err == nil {
		if uri != oldPkg.Uri {
			Sheller.RemoveAll(oldPkg.Uri)
		}
		oldPkg.Merge(pkg)
		if oldPkg.Type == "" {
			oldPkg.Type = PackageTypes.SERVICE
		}
		err = oldPkg.Update()
		if err != nil {
			return err
		}
		t.PackageId = oldPkg.Id
		slog.Debug(fmt.Sprintf("[Task(%d):performLocalExtraction] Update package %s %s", t.Id, oldPkg.Name, oldPkg.Version))
	} else {
		if pkg.Type == "" {
			pkg.Type = PackageTypes.SERVICE
		}
		pkg.State = PackageStates.Downloaded
		pkg.Downloaded = 1
		err = pkg.Add()
		if err != nil {
			return err
		}
		t.PackageId = pkg.Id
		slog.Debug(fmt.Sprintf("[Task(%d):performLocalExtraction] Add package %s %s", t.Id, pkg.Name, pkg.Version))
	}
	
	elapsedTime := time.Since(startTime)
	slog.Info(fmt.Sprintf("[本地导入] 解压完成 - 任务ID: %d, 包名: %s, 包ID: %d, 耗时: %v", t.Id, pkg.Name, t.PackageId, elapsedTime))
	return nil
}

// copyLocalFile 复制本地文件的辅助函数
func (t *Task) copyLocalFile(src, dst string) error {
	// 确保目标目录存在
	dstDir := filepath.Dir(dst)
	if err := os.MkdirAll(dstDir, 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}
	
	// 打开源文件
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("打开源文件失败: %v", err)
	}
	defer srcFile.Close()
	
	// 获取源文件信息
	srcInfo, err := srcFile.Stat()
	if err != nil {
		return fmt.Errorf("获取源文件信息失败: %v", err)
	}
	
	// 创建目标文件
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer dstFile.Close()
	
	// 使用大缓冲区复制文件，提高性能
	bufferSize := 1024 * 1024 // 1MB缓冲区
	buffer := make([]byte, bufferSize)
	
	totalSize := srcInfo.Size()
	copiedSize := int64(0)
	
	for {
		n, err := srcFile.Read(buffer)
		if err != nil && err != io.EOF {
			return fmt.Errorf("读取源文件失败: %v", err)
		}
		if n == 0 {
			break
		}
		
		_, err = dstFile.Write(buffer[:n])
		if err != nil {
			return fmt.Errorf("写入目标文件失败: %v", err)
		}
		
		copiedSize += int64(n)
		
		// 更新进度
		progress := float64(copiedSize) / float64(totalSize) * 100
		t.Progress = fmt.Sprintf("%.2f", progress)
		t.Update()
		
		// 记录进度日志
		if copiedSize%int64(bufferSize*10) == 0 || copiedSize == totalSize {
			slog.Info(fmt.Sprintf("[本地导入] 复制进度: %.1f%% (%d/%d bytes)", progress, copiedSize, totalSize))
		}
	}
	
	slog.Info(fmt.Sprintf("[本地导入] 文件复制成功: %s, 大小: %d bytes", dst, totalSize))
	return nil
}

func (t *Task) AddForUploadPackage(name string, version string) error {
	pkg := &Package{Name: name, Version: version}
	location, err := pkg.GetLocation()
	t.Name = TaskNames.Upload
	t.Type = TaskTypes.File
	if err != nil {
		t.State = TaskStates.Failed
	} else {
		t.Content = *location
		t.Progress = "0.00"
		t.State = TaskStates.Running
	}
	err = t.Add()
	if err != nil {
		return err
	}
	return nil
}

func (t *Task) UploadPackage(chunkIndex int32, chunkContent []byte, totalChunks int32) error {
	if t.Id == 0 && t.Content == "" {
		return fmt.Errorf("task not exists")
	}
	location := t.Content
	tmpFilename := fmt.Sprintf("%s_%d", location, chunkIndex)
	file, err := os.OpenFile(tmpFilename, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		return err
	}
	writer := bufio.NewWriter(file)
	if _, err := writer.Write(chunkContent); err != nil {
		return err
	}
	writer.Flush()
	file.Close()
	slog.Debug(fmt.Sprintf("[Task(%d):UploadPackage] Write chunk[%d] to %s, total %d", t.Id, chunkIndex, tmpFilename, totalChunks))
	progress := fmt.Sprintf("%.2f", float64(chunkIndex+1)/float64(totalChunks)*100)
	if t.Progress != "" {
		progressFloat64, _ := strconv.ParseFloat(progress, 64)
		oldProgressFloat64, _ := strconv.ParseFloat(t.Progress, 64)
		if progressFloat64 > oldProgressFloat64 {
			t.Progress = progress
		}
	} else {
		t.Progress = progress
	}
	if t.State != TaskStates.Finished && t.State != TaskStates.Failed {
		t.State = TaskStates.Running
	}
	err = t.Save()
	if err != nil {
		return err
	}
	return nil
}

func (t *Task) MergePackage(totalChunks int32) error {
	if t.Id == 0 && t.Content == "" {
		return fmt.Errorf("task not exists")
	}
	uri := t.Content
	if _, err := os.Stat(uri); !os.IsNotExist(err) {
		err = os.Remove(uri)
		if err != nil {
			return err
		} else {
			slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Remove %s", t.Id, uri))
		}
	}
	totalSize := 0
	fullFile, err := os.OpenFile(uri, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		return err
	}
	defer fullFile.Close()
	for i := 0; i < int(totalChunks); i++ {
		tmpFilename := fmt.Sprintf("%s_%d", uri, i)
		if stat, err := os.Stat(tmpFilename); !os.IsNotExist(err) {
			totalSize += int(stat.Size())
		}
		tmpFile, err := os.OpenFile(tmpFilename, os.O_RDONLY, 0600)
		if err != nil {
			return err
		}
		_, err = io.Copy(fullFile, tmpFile)
		if err != nil {
			return err
		}
		tmpFile.Close()
		err = os.Remove(tmpFilename)
		if err != nil {
			return err
		}
		slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Write temp file %s to %s", t.Id, tmpFilename, uri))
	}
	// 判断uri是否存在
	if _, err := os.Stat(uri); os.IsNotExist(err) {
		return fmt.Errorf("package file not exists")
	}
	pkgsDir, err := config.GetPackagesDir()
	if err != nil {
		return err
	}
	// 取目录的最后一级的上级目录名作为包名，例如：/Users/<USER>/.ais/store/mosquitto/1717496482026，包名为mosquitto
	parentDir := filepath.Dir(uri)
	pkgName := filepath.Base(parentDir)
	pkg := &Package{Name: pkgName, Uri: uri, Size: int32(totalSize)}
	// 使用高性能解压，根据文件大小自动选择最优方式
	err = lib.UnzipAuto(uri, pkgsDir)
	if err != nil {
		// 如果高性能解压失败，回退到优化的标准库解压
		slog.Warn(fmt.Sprintf("[解压] 高性能解压失败，回退到优化标准库: %v", err))
		t.ExtractProgress = "5.00"
		t.Update()
		
		// 首先尝试优化的标准库解压
		err = lib.UnzipOptimized(uri, pkgsDir, func(progress float64, message string) {
			t.ExtractProgress = fmt.Sprintf("%.2f", progress)
			t.Update()
			slog.Info(fmt.Sprintf("[解压] 任务ID: %d, 进度: %.1f%% - %s", t.Id, progress, message))
		})
		
		if err != nil {
			// 如果优化解压也失败，使用标准解压
			slog.Warn(fmt.Sprintf("[解压] 优化解压失败，使用标准解压: %v", err))
			err = lib.UnzipWithProgress(uri, pkgsDir, func(progress float64, message string) {
				t.ExtractProgress = fmt.Sprintf("%.2f", progress)
				t.Update()
				slog.Info(fmt.Sprintf("[解压] 任务ID: %d, 进度: %.1f%% - %s", t.Id, progress, message))
			})
			
			if err != nil {
				errorMsg := fmt.Sprintf("解压文件失败: %v", err)
				slog.Error(fmt.Sprintf("[解压] %s - 文件: %s, 目标目录: %s", errorMsg, uri, pkgsDir))
				t.ErrorLog = errorMsg
				t.State = TaskStates.Failed
				t.Update()
				return err
			}
		}
	} else {
		// 高性能解压成功，设置完成进度
		t.ExtractProgress = "100.00"
		t.Update()
	}
	slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Unzip %s to %s", t.Id, uri, pkgsDir))
	err = pkg.Load()
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Load package config successful, %s %s", t.Id, pkg.Name, pkg.Version))
	oldPkg := &Package{Name: pkg.Name}
	err = oldPkg.GetByName()
	if err == nil {
		if uri != oldPkg.Uri {
			Sheller.RemoveAll(oldPkg.Uri)
		}
		oldPkg.Merge(pkg)
		if oldPkg.Type == "" {
			oldPkg.Type = PackageTypes.SERVICE
		}
		err = oldPkg.Update()
		if err != nil {
			return err
		}
		slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Update package %s %s", t.Id, oldPkg.Name, oldPkg.Version))
	} else {
		if pkg.Type == "" {
			pkg.Type = PackageTypes.SERVICE
		}
		pkg.State = PackageStates.Downloaded
		pkg.Downloaded = 1
		err = pkg.Add()
		if err != nil {
			return err
		}
		slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Add package %s %s", t.Id, pkg.Name, pkg.Version))
	}
	t.State = TaskStates.Finished
	t.Progress = "100.00"
	t.PackageId = pkg.Id
	err = t.Update()
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Task(%d):MergePackage] Update successful", t.Id))
	return nil
}
