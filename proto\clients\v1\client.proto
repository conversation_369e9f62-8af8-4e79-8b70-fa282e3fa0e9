syntax = "proto3";

package configs.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service ClientService {
  rpc ListClients(Client) returns (ListClientsResponse) {
    option (google.api.http) = {
      get: "/api/v1/clients"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get clients"
      description: "Get all clients"
      tags: "Clients"
    };
  }

  rpc SaveClient(Client) returns (ClientResponse) {
    option (google.api.http) = {
      post: "/api/v1/clients"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Save client"
      description: "Save a client"
      tags: "Clients"
    };
  }

  rpc GetCurrent(GetCurrentRequest) returns (ClientResponse) {
    option (google.api.http) = {
      get: "/api/v1/clients/current"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get current client"
      description: "Get current client information"
      tags: "Clients"
    };
  }

  rpc SaveCurrent(Client) returns (ClientResponse) {
    option (google.api.http) = {
      post: "/api/v1/clients/current"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Save current client"
      description: "Save current client information"
      tags: "Clients"
    };
  }

  rpc DeleteClient(DeleteClientRequest) returns (ClientResponse) {
    option (google.api.http) = {
      delete: "/api/v1/clients/{id}"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete client"
      description: "Delete a client"
      tags: "Clients"
    };
  }
}

message ClientResponse {
  string message = 1;
  int32 code = 2;
  Client data = 3;
}

message GetCurrentRequest {
}

message ListClientsResponse {
  string message = 1;
  int32 code = 2;
  repeated Client data = 3;
}

message DeleteClientRequest {
  string id = 1;
}

message Client {
  string id = 1;
  string name = 2;
  int32 grpc_port = 3;
  int32 gateway_port = 4;
  int32 ssl = 5;
  string parent_id = 6;
  string parent_host = 7;
  int32 parent_grpc_port = 8;
  int32 parent_gateway_port = 9;
  int32 parent_mqtt_tcp_port = 10;
  int32 parent_mqtt_ws_port = 11;
  int32 parent_ssl = 12;
  string os_type = 13;
  string networks = 14;
  string version = 15;
  string uptime = 16;
  string state = 17;
  string created_at = 18;
  string updated_at = 19;
  int32 is_local = 20;
}