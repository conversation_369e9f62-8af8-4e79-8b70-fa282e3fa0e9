syntax = "proto3";

package programs.v1;

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service PackageService {
  rpc ListPrograms(ListProgramsRequest) returns (ListProgramsResponse) {
    option (google.api.http) = {
      get: "/api/v1/programs"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List programs"
      description: "List programs on the server."
      tags: "Programs"
    };
  }

  rpc ExecuteProgram(ProgramCommandRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/programs/{id}/cmds"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Execute the program"
      description: "Execute the program on the server"
      tags: "Programs"
    };
  }

  rpc DeleteProgram(Program) returns (CommonResponse) {
    option (google.api.http) = {
      delete: "/api/v1/programs/{id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete the program"
      description: "Delete the program from the server."
      tags: "Programs"
    };
  }
}

message CommonResponse {
  int32 code = 1;
  string message = 2;
}

message ListProgramsRequest {
  int32 total = 1;
  int32 page = 2;
  int32 size = 3;
  int64 id = 4;
  string name = 5;
}

message ListProgramsResponseData {
  int32 total = 1;
  int32 page = 2;
  int32 size = 3;
  repeated Program content = 4;
}

message ListProgramsResponse {
  string message = 1;
  int32 code = 2;
  ListProgramsResponseData data = 3;
}

message ProgramCommandRequest {
  int64 id = 1;
  string type = 2;
}

message Program {
  int64 id = 1;
  string name = 2;
  string state = 3;
  string package_path = 4;
  string install_path = 5;
  int64 port = 6;
  bool bootable = 7;
  int32 sort = 8;
}