import{u as e,b as a,j as o,s as m,T as l}from"./index.bbeb3af6.js";import{l as r,D as s}from"./index.6af89ef4.js";import{C as t}from"./index.cf9faf12.js";function i(i){const n=e(r),{data:u}=i,f=[{label:n["os.memory.total"],value:u.memory.total},{label:n["os.memory.free"],value:u.memory.free},{label:n["os.memory.used"],value:u.memory.used},{label:n["os.memory.usage"],value:u.memory.usage}];return a(t,{children:[o(m,{align:"start",children:o(l.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:n["os.memory.title"]})}),o(s,{colon:": ",layout:"horizontal",data:f,column:2})]})}export{i as default};
