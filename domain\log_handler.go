package domain

import (
	"context"
	"fmt"
	"io"
	"log/slog"

	"gitlab.jhonginfo.com/product/ais-server/config"
)

type LogHandler struct {
	handler slog.Handler
}

func NewLogHandler(w io.Writer, opts *slog.HandlerOptions) *LogHandler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}
	return &LogHandler{
		handler: slog.NewTextHandler(w, opts),
	}
}

func GetLogLevel() slog.Level {
	configLogLevel := config.LogLevel()
	switch configLogLevel {
	case LogLevel.Debug:
		return slog.LevelDebug
	case LogLevel.Warn:
		return slog.LevelWarn
	case LogLevel.Error:
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

func (h *LogHandler) Enabled(_ context.Context, level slog.Level) bool {
	return h.handler.Enabled(context.Background(), level)
}

func (h *LogHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return h.handler.WithAttrs(attrs)
}

func (h *LogHandler) WithGroup(name string) slog.Handler {
	return h.handler.WithGroup(name)
}

func (h *LogHandler) Handle(_ context.Context, r slog.Record) error {
	configLogLevel := config.LogLevel()
	persistLogLevel := slog.LevelInfo
	switch configLogLevel {
	case LogLevel.Debug:
		persistLogLevel = slog.LevelDebug
	case LogLevel.Warn:
		persistLogLevel = slog.LevelWarn
	case LogLevel.Error:
		persistLogLevel = slog.LevelError
	default:
		persistLogLevel = slog.LevelInfo
	}
	if r.Level >= persistLogLevel {
		level := r.Level.String()
		message := r.Message
		attrs := attrsSlice(r)
		if len(attrs) > 0 {
			message = fmt.Sprintf("%s %v", message, attrs)
		}
		SaveLog(level, r.Time, message)
	}
	return h.handler.Handle(context.Background(), r)
}

func attrsSlice(r slog.Record) []slog.Attr {
	s := make([]slog.Attr, 0, r.NumAttrs())
	r.Attrs(func(a slog.Attr) bool { s = append(s, a); return true })
	return s
}
