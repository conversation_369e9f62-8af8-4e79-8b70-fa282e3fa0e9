const i18n = {
  'en-US': {
    'menu.main': 'Main',
    'menu.main.os': 'System Information',
    'os.info.title': 'System',
    'os.info.type': 'Type',
    'os.info.version': 'Platform',
    'os.info.arch': 'Architecture',
    'os.cpu.title': 'CPU',
    'os.cpu.cores': 'Cores',
    'os.cpu.usage': 'Usage',
    'os.memory.title': 'Memory',
    'os.memory.total': 'Total',
    'os.memory.free': 'Free',
    'os.memory.used': 'Used',
    'os.memory.usage': 'Usage',
    'os.disk.title': 'Disk',
    'os.disk.total': 'Total',
    'os.disk.free': 'Free',
    'os.disk.used': 'Used',
    'os.disk.usage': 'Usage',
    'os.times.up': 'Up Time',
    'os.network.title': 'Networks',
    'os.network.name': 'Name',
    'os.network.address': 'Address',
    'os.network.family': 'Family',
    'os.network.mac': 'MAC',
  },
  'zh-CN': {
    'menu.main': '主页',
    'menu.main.os': '系统信息',
    'os.info.title': '系统',
    'os.info.type': '类型',
    'os.info.version': '版本',
    'os.info.arch': '架构',
    'os.cpu.title': 'CPU',
    'os.cpu.cores': '核心数',
    'os.cpu.usage': '使用率',
    'os.memory.title': '内存',
    'os.memory.total': '总内存',
    'os.memory.free': '空闲内存',
    'os.memory.used': '已用内存',
    'os.memory.usage': '使用率',
    'os.disk.title': '磁盘',
    'os.disk.total': '总磁盘',
    'os.disk.free': '空闲磁盘',
    'os.disk.used': '已用磁盘',
    'os.disk.usage': '使用率',
    'os.times.up': '运行时间',
    'os.network.title': '网络',
    'os.network.name': '名称',
    'os.network.address': '地址',
    'os.network.family': '协议',
    'os.network.mac': 'MAC',
  },
};

export default i18n;
