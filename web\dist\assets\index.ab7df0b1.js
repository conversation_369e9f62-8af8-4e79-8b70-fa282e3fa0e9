import{o as e,aM as t,u as s,r,af as a,j as o,b as i,aq as n,B as c,Q as p,O as l,i as m,s as d,k as u,l as h}from"./index.6227d37e.js";/* empty css              */import{I as f}from"./index.c12dce75.js";import{U as F}from"./index.176b9b5e.js";import{R as b}from"./index.9e8b20df.js";import{C as I}from"./index.a7402227.js";import{S as k}from"./index.eba3faa0.js";import"./index.182aa2de.js";const y={"en-US":{"menu.form":"Form","menu.form.step":"Step Form","stepForm.title":"Create a channel form","stepForm.next":"Next","stepForm.prev":"Prev","stepForm.title.basicInfo":"Basic Information","stepForm.desc.basicInfo":"Create an package","stepForm.title.attachment":"Attachment Information","stepForm.desc.attachment":"Upload attachment","stepForm.title.channel":"Channel Information","stepForm.desc.channel":"Enter detailed channel content","stepForm.title.created":"Complete creation","stepForm.desc.created":"Created successfully","stepForm.basicInfo.name":"Package name","stepForm.basicInfo.name.required":"Please enter the package name","stepForm.basicInfo.name.placeholder":"Enter letters, numbers, -, _, such as: ais-mes (up to 20 characters)","stepForm.basicInfo.version":"Package version","stepForm.basicInfo.version.required":"Please enter the package version","stepForm.basicInfo.version.placeholder":"Enter the package version, such as: 1.0.0 (up to 20 characters)","stepForm.basicInfo.type":"Package type","stepForm.basicInfo.type.required":"Please select the package type","stepForm.basicInfo.type.placeholder":"Please select the package type","stepForm.basicInfo.types.sdk":"SDK","stepForm.basicInfo.types.svc":"Service","stepForm.basicInfo.types.file":"File","stepForm.basicInfo.description":"Package description","stepForm.basicInfo.description.required":"Please enter the package description","stepForm.basicInfo.description.placeholder":"Please enter the package description (up to 200 words)","stepForm.basicInfo.upgradable":"Auto upgrade","stepForm.attachment.label":"Attachment","stepForm.attachment.required":"Please upload the attachment","stepForm.attachment.support.tips":"Only zip/jar/war can be uploaded","stepForm.attachment.support-error.tips":"Unacceptable file type, please re-upload the specified file type!","stepForm.publish.confirm.title":"Publish confirmation","stepForm.publish.confirm.content":"The published version file is too different from the current version file name or size, do you want to continue publishing?","stepForm.created.fail":"Failed to create","stepForm.created.fail.already-exists":"Package already exists","stepForm.created.fail.already-published":"This version of the package has been published","stepForm.created.success":"Created successfully","stepForm.created.success.title":"Created successfully","stepForm.created.success.desc":"Package created successfully","stepForm.created.success.again":"Create again","stepForm.operations.back":"Back"},"zh-CN":{"menu.form":"表单页","menu.form.step":"分布表单","stepForm.title":"创建渠道表单","stepForm.next":"下一步","stepForm.prev":"上一步","stepForm.title.basicInfo":"基本信息","stepForm.desc.basicInfo":"创建包","stepForm.title.attachment":"附件信息","stepForm.desc.attachment":"上传附件","stepForm.title.created":"完成创建","stepForm.desc.created":"创建成功","stepForm.basicInfo.name":"包名称","stepForm.basicInfo.name.required":"请输入包名称","stepForm.basicInfo.name.placeholder":"输入字母、数字、-、_，如：ais-mes（最多20字符）","stepForm.basicInfo.version":"包版本","stepForm.basicInfo.version.required":"请输入包版本","stepForm.basicInfo.version.placeholder":"输入包版本，如：1.0.0（最多20字符）","stepForm.basicInfo.type":"包类型","stepForm.basicInfo.type.required":"请选择包类型","stepForm.basicInfo.type.placeholder":"请选择包类型","stepForm.basicInfo.types.sdk":"SDK","stepForm.basicInfo.types.svc":"服务","stepForm.basicInfo.types.file":"文件","stepForm.basicInfo.description":"包描述","stepForm.basicInfo.description.required":"请输入包描述","stepForm.basicInfo.description.placeholder":"请输入包描述（最多200字）","stepForm.basicInfo.upgradable":"自动升级","stepForm.attachment.label":"附件","stepForm.attachment.required":"请上传附件","stepForm.attachment.support.tips":"仅支持zip/jar/war上传","stepForm.attachment.support-error.tips":"不接受的文件类型，请重新上传指定文件类型！","stepForm.publish.confirm.title":"发布确认","stepForm.publish.confirm.content":"发布版本文件与当前版本文件名或大小差距过大，是否继续发布？","stepForm.created.fail":"创建失败","stepForm.created.fail.already-exists":"包已存在","stepForm.created.fail.already-published":"此版本包已有过发布","stepForm.created.success":"创建成功","stepForm.created.success.title":"创建成功","stepForm.created.success.desc":"包创建成功","stepForm.created.success.again":"再次创建","stepForm.operations.back":"返回"}};var g="_container_1klic_1",v="_wrapper_1klic_7",x="_form_1klic_13";const C=["sdk","svc","file"];function q(){const[q]=e("store-step-form"),z=t(),S=s(y),[P,w]=r.exports.useState(1),[j,_]=r.exports.useState(!1),[A,E]=r.exports.useState(0),[N,O]=r.exports.useState(0),[R,U]=r.exports.useState(!1),[M]=a.useForm(),T=async(e,t)=>{null!=e.chunkContent&&h("/api/v1/packages/"+e.id+"/chunks",{method:"post",data:{id:e.id,chunkIndex:e.chunkIndex,chunkContent:e.chunkContent,chunkSize:10485760,totalChunks:e.totalChunks},timeout:6e4}).then((s=>{const r=s&&s.data;r&&0===r.code?t&&t({index:e.chunkIndex,uploaded:!0}):t&&t({index:e.chunkIndex,uploaded:!1})})).catch((()=>{t&&t({index:e.chunkIndex,uploaded:!1})}))},D=async(e,t)=>{_(!0),(async(e,t)=>{h("/api/v1/packages",{method:"post",data:{name:e.name,version:e.version,description:e.description,type:e.type,size:e.size}}).then((e=>{const s=e&&e.data;s&&0===s.code?t&&t(s.data):m.error(S["stepForm.created.fail"])})).catch((()=>{m.error(S["stepForm.created.fail"])}))})(e,(e=>{const s=e.id,r=[],a=Math.ceil(t.size/10485760);for(let o=0;o<a;o++){const e=o,i=10485760*o,n=Math.min(t.size,i+10485760),c=t.slice(i,n),p=new FileReader;p.onloadend=function(){let t=p.result;t&&(t=String(t).replace(/^data:.+;base64,/,""),T({id:s,chunkIndex:e,chunkContent:t,totalChunks:a},(e=>{r.push(e),r.length===a&&h("/api/v1/packages/"+s+"/chunks",{method:"post",data:{id:s,totalChunks:a,merged:1}}).then((e=>{const t=e&&e.data;t&&0===t.code?(m.success(S["stepForm.created.success"]),w(P+1)):m.error(S["stepForm.created.fail"])})).catch((()=>{m.error(S["stepForm.created.fail"])})).finally((()=>{_(!1)}))})))},p.readAsDataURL(c)}}))};return r.exports.useEffect((()=>{if(z.state||q){_(!0),U(!0);const e=z.state||JSON.parse(q);E(e.size),O(e.timestamp),M.setFieldsValue(e),_(!1)}}),[]),o("div",{className:g,children:i(I,{children:[o(n,{to:"/main/store",children:o(c,{loading:j,type:"primary",icon:o(f,{}),children:S["stepForm.operations.back"]})}),i("div",{className:v,children:[i(k,{current:P,lineless:!0,children:[o(k.Step,{title:S["stepForm.title.basicInfo"],description:S["stepForm.desc.basicInfo"]}),o(k.Step,{title:S["stepForm.title.attachment"],description:S["stepForm.desc.attachment"]}),o(k.Step,{title:S["stepForm.title.created"],description:S["stepForm.desc.created"]})]}),i(a,{form:M,className:x,children:[1===P&&i(a.Item,{noStyle:!0,children:[o(a.Item,{label:S["stepForm.basicInfo.name"],disabled:R,required:!0,field:"name",rules:[{required:!0,message:S["stepForm.basicInfo.name.required"]},{validator:(e,t)=>{/^[a-zA-Z0-9-_]{1,20}$/g.test(e)||t(S["stepForm.basicInfo.name.placeholder"])}}],children:o(p,{placeholder:S["stepForm.basicInfo.name.placeholder"]})}),o(a.Item,{label:S["stepForm.basicInfo.version"],required:!0,field:"version",rules:[{required:!0,message:S["stepForm.basicInfo.version.required"]}],children:o(p,{placeholder:S["stepForm.basicInfo.version.placeholder"]})}),o(a.Item,{label:S["stepForm.basicInfo.type"],required:!0,field:"type",rules:[{required:!0,message:S["stepForm.basicInfo.type.required"]}],children:o(l,{placeholder:S["stepForm.basicInfo.type.placeholder"],children:C.map((e=>o(l.Option,{value:e,children:S[`stepForm.basicInfo.types.${e}`]},e)))})}),o(a.Item,{label:S["stepForm.basicInfo.description"],field:"description",children:o(p.TextArea,{placeholder:S["stepForm.basicInfo.description.placeholder"]})})]}),2===P&&o(a.Item,{noStyle:!0,children:o(a.Item,{label:S["stepForm.attachment.label"],required:!0,field:"attachment",rules:[{required:!0,message:S["stepForm.attachment.required"]}],children:o(F,{drag:!0,accept:".zip,.jar,.war",autoUpload:!1,limit:1,onDrop:e=>{((e,t)=>{if(t&&e){const s=Array.isArray(t)?t:t.split(",").map((e=>e.trim())).filter((e=>e)),r=e.name.indexOf(".")>-1?e.name.split(".").pop():"";return s.some((t=>{const s=t&&t.toLowerCase(),a=(e.type||"").toLowerCase();if(s===a)return!0;if(new RegExp("/*").test(s)){const e=new RegExp("/.*$");return a.replace(e,"")===s.replace(e,"")}return!!new RegExp("..*").test(s)&&s===`.${r&&r.toLowerCase()}`}))}return!!e})(e.dataTransfer.files[0],["application/zip",".zip"])||m.info(S["stepForm.attachment.support-error.tips"])},tip:S["stepForm.attachment.support.tips"]})})}),o(a.Item,3!==P?{label:" ",children:i(d,{children:[2===P&&o(c,{loading:j,size:"large",onClick:()=>w(P-1),children:S["stepForm.prev"]}),3!==P&&o(c,{loading:j,type:"primary",size:"large",onClick:async()=>{try{if(await M.validate(),2===P){const e=M.getFields(),t={name:e.name,version:e.version,description:e.description,size:null,location:null,timestamp:null,chunkIndex:null,chunkContent:null,chunkSize:null,totalChunks:null};if(e.attachment&&e.attachment.length>0){const s=e.attachment[0].originFile;if(t.size=s.size,t.timestamp=s.lastModified,N===t.timestamp)return void m.error(S["stepForm.created.fail.already-published"]);const r=s.size,a=Math.abs(r-A)/A;R&&a>.1?u.confirm({title:S["stepForm.publish.confirm.title"],content:S["stepForm.publish.confirm.content"],onOk:()=>{D(t,s)}}):D(t,s)}}else w(P+1)}catch(e){}},children:S["stepForm.next"]})]})}:{noStyle:!0,children:o(b,{status:"success",title:S["stepForm.created.success.title"],subTitle:S["stepForm.created.success.desc"]})})]})]})]})})}export{q as default};
