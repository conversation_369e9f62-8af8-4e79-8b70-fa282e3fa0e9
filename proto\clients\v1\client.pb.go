// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: clients/v1/client.proto

package configsv1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string  `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32   `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Data    *Client `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ClientResponse) Reset() {
	*x = ClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_v1_client_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientResponse) ProtoMessage() {}

func (x *ClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_clients_v1_client_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientResponse.ProtoReflect.Descriptor instead.
func (*ClientResponse) Descriptor() ([]byte, []int) {
	return file_clients_v1_client_proto_rawDescGZIP(), []int{0}
}

func (x *ClientResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ClientResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ClientResponse) GetData() *Client {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetCurrentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCurrentRequest) Reset() {
	*x = GetCurrentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_v1_client_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCurrentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentRequest) ProtoMessage() {}

func (x *GetCurrentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_clients_v1_client_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentRequest.ProtoReflect.Descriptor instead.
func (*GetCurrentRequest) Descriptor() ([]byte, []int) {
	return file_clients_v1_client_proto_rawDescGZIP(), []int{1}
}

type ListClientsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string    `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32     `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Data    []*Client `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ListClientsResponse) Reset() {
	*x = ListClientsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_v1_client_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClientsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClientsResponse) ProtoMessage() {}

func (x *ListClientsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_clients_v1_client_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClientsResponse.ProtoReflect.Descriptor instead.
func (*ListClientsResponse) Descriptor() ([]byte, []int) {
	return file_clients_v1_client_proto_rawDescGZIP(), []int{2}
}

func (x *ListClientsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListClientsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListClientsResponse) GetData() []*Client {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteClientRequest) Reset() {
	*x = DeleteClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_v1_client_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClientRequest) ProtoMessage() {}

func (x *DeleteClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_clients_v1_client_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClientRequest.ProtoReflect.Descriptor instead.
func (*DeleteClientRequest) Descriptor() ([]byte, []int) {
	return file_clients_v1_client_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteClientRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Client struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name              string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GrpcPort          int32  `protobuf:"varint,3,opt,name=grpc_port,json=grpcPort,proto3" json:"grpc_port,omitempty"`
	GatewayPort       int32  `protobuf:"varint,4,opt,name=gateway_port,json=gatewayPort,proto3" json:"gateway_port,omitempty"`
	Ssl               int32  `protobuf:"varint,5,opt,name=ssl,proto3" json:"ssl,omitempty"`
	ParentId          string `protobuf:"bytes,6,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	ParentHost        string `protobuf:"bytes,7,opt,name=parent_host,json=parentHost,proto3" json:"parent_host,omitempty"`
	ParentGrpcPort    int32  `protobuf:"varint,8,opt,name=parent_grpc_port,json=parentGrpcPort,proto3" json:"parent_grpc_port,omitempty"`
	ParentGatewayPort int32  `protobuf:"varint,9,opt,name=parent_gateway_port,json=parentGatewayPort,proto3" json:"parent_gateway_port,omitempty"`
	ParentMqttTcpPort int32  `protobuf:"varint,10,opt,name=parent_mqtt_tcp_port,json=parentMqttTcpPort,proto3" json:"parent_mqtt_tcp_port,omitempty"`
	ParentMqttWsPort  int32  `protobuf:"varint,11,opt,name=parent_mqtt_ws_port,json=parentMqttWsPort,proto3" json:"parent_mqtt_ws_port,omitempty"`
	ParentSsl         int32  `protobuf:"varint,12,opt,name=parent_ssl,json=parentSsl,proto3" json:"parent_ssl,omitempty"`
	OsType            string `protobuf:"bytes,13,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	Networks          string `protobuf:"bytes,14,opt,name=networks,proto3" json:"networks,omitempty"`
	Version           string `protobuf:"bytes,15,opt,name=version,proto3" json:"version,omitempty"`
	Uptime            string `protobuf:"bytes,16,opt,name=uptime,proto3" json:"uptime,omitempty"`
	State             string `protobuf:"bytes,17,opt,name=state,proto3" json:"state,omitempty"`
	CreatedAt         string `protobuf:"bytes,18,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         string `protobuf:"bytes,19,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	IsLocal           int32  `protobuf:"varint,20,opt,name=is_local,json=isLocal,proto3" json:"is_local,omitempty"`
}

func (x *Client) Reset() {
	*x = Client{}
	if protoimpl.UnsafeEnabled {
		mi := &file_clients_v1_client_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Client) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Client) ProtoMessage() {}

func (x *Client) ProtoReflect() protoreflect.Message {
	mi := &file_clients_v1_client_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Client.ProtoReflect.Descriptor instead.
func (*Client) Descriptor() ([]byte, []int) {
	return file_clients_v1_client_proto_rawDescGZIP(), []int{4}
}

func (x *Client) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Client) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Client) GetGrpcPort() int32 {
	if x != nil {
		return x.GrpcPort
	}
	return 0
}

func (x *Client) GetGatewayPort() int32 {
	if x != nil {
		return x.GatewayPort
	}
	return 0
}

func (x *Client) GetSsl() int32 {
	if x != nil {
		return x.Ssl
	}
	return 0
}

func (x *Client) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *Client) GetParentHost() string {
	if x != nil {
		return x.ParentHost
	}
	return ""
}

func (x *Client) GetParentGrpcPort() int32 {
	if x != nil {
		return x.ParentGrpcPort
	}
	return 0
}

func (x *Client) GetParentGatewayPort() int32 {
	if x != nil {
		return x.ParentGatewayPort
	}
	return 0
}

func (x *Client) GetParentMqttTcpPort() int32 {
	if x != nil {
		return x.ParentMqttTcpPort
	}
	return 0
}

func (x *Client) GetParentMqttWsPort() int32 {
	if x != nil {
		return x.ParentMqttWsPort
	}
	return 0
}

func (x *Client) GetParentSsl() int32 {
	if x != nil {
		return x.ParentSsl
	}
	return 0
}

func (x *Client) GetOsType() string {
	if x != nil {
		return x.OsType
	}
	return ""
}

func (x *Client) GetNetworks() string {
	if x != nil {
		return x.Networks
	}
	return ""
}

func (x *Client) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Client) GetUptime() string {
	if x != nil {
		return x.Uptime
	}
	return ""
}

func (x *Client) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Client) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Client) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *Client) GetIsLocal() int32 {
	if x != nil {
		return x.IsLocal
	}
	return 0
}

var File_clients_v1_client_proto protoreflect.FileDescriptor

var file_clients_v1_client_proto_rawDesc = []byte{
	0x0a, 0x17, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x66, 0x0a, 0x0e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x13, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x6b, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x25, 0x0a,
	0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x22, 0xeb, 0x04, 0x0a, 0x06, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x70, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x73, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x73, 0x73, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x73,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x48,
	0x6f, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x70, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2e, 0x0a,
	0x13, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2f, 0x0a,
	0x14, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x71, 0x74, 0x74, 0x5f, 0x74, 0x63, 0x70,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4d, 0x71, 0x74, 0x74, 0x54, 0x63, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2d,
	0x0a, 0x13, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x71, 0x74, 0x74, 0x5f, 0x77, 0x73,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x4d, 0x71, 0x74, 0x74, 0x57, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x73, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x73, 0x6c, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x70, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x70, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x32, 0x92, 0x06, 0x0a, 0x0d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x44, 0x92, 0x41, 0x27, 0x0a, 0x07,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x0b, 0x47, 0x65, 0x74, 0x20, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x1a, 0x0f, 0x47, 0x65, 0x74, 0x20, 0x61, 0x6c, 0x6c, 0x20, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x62, 0x01, 0x2a, 0x12, 0x0f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x83, 0x01, 0x0a, 0x0a, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x12,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x45,
	0x92, 0x41, 0x25, 0x0a, 0x07, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x0b, 0x53, 0x61,
	0x76, 0x65, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x0d, 0x53, 0x61, 0x76, 0x65, 0x20,
	0x61, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01,
	0x2a, 0x62, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0xab, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x62, 0x92, 0x41, 0x3d, 0x0a, 0x07, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x47,
	0x65, 0x74, 0x20, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x1a, 0x1e, 0x47, 0x65, 0x74, 0x20, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x20, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x62, 0x01, 0x2a, 0x12, 0x17, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x0b, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x67, 0x92, 0x41, 0x3f, 0x0a, 0x07, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x13, 0x53, 0x61, 0x76, 0x65, 0x20, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x20,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x1f, 0x53, 0x61, 0x76, 0x65, 0x20, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x20, 0x69, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a,
	0x62, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x98, 0x01, 0x0a,
	0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4b, 0x92, 0x41, 0x29, 0x0a,
	0x07, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x1a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x20,
	0x61, 0x20, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x62, 0x01,
	0x2a, 0x2a, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x42, 0xf5, 0x01, 0x92, 0x41, 0x48, 0x12, 0x05, 0x32,
	0x03, 0x31, 0x2e, 0x30, 0x2a, 0x01, 0x02, 0x72, 0x3c, 0x0a, 0x0a, 0x41, 0x49, 0x53, 0x20, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2e, 0x68, 0x74, 0x74, 0x70, 0x3a, 0x2f, 0x2f, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x42, 0x0b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f,
	0x6e, 0x67, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x43, 0x58, 0x58, 0xaa, 0x02,
	0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0a, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x16, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0xea, 0x02, 0x0b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_clients_v1_client_proto_rawDescOnce sync.Once
	file_clients_v1_client_proto_rawDescData = file_clients_v1_client_proto_rawDesc
)

func file_clients_v1_client_proto_rawDescGZIP() []byte {
	file_clients_v1_client_proto_rawDescOnce.Do(func() {
		file_clients_v1_client_proto_rawDescData = protoimpl.X.CompressGZIP(file_clients_v1_client_proto_rawDescData)
	})
	return file_clients_v1_client_proto_rawDescData
}

var file_clients_v1_client_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_clients_v1_client_proto_goTypes = []interface{}{
	(*ClientResponse)(nil),      // 0: configs.v1.ClientResponse
	(*GetCurrentRequest)(nil),   // 1: configs.v1.GetCurrentRequest
	(*ListClientsResponse)(nil), // 2: configs.v1.ListClientsResponse
	(*DeleteClientRequest)(nil), // 3: configs.v1.DeleteClientRequest
	(*Client)(nil),              // 4: configs.v1.Client
}
var file_clients_v1_client_proto_depIdxs = []int32{
	4, // 0: configs.v1.ClientResponse.data:type_name -> configs.v1.Client
	4, // 1: configs.v1.ListClientsResponse.data:type_name -> configs.v1.Client
	4, // 2: configs.v1.ClientService.ListClients:input_type -> configs.v1.Client
	4, // 3: configs.v1.ClientService.SaveClient:input_type -> configs.v1.Client
	1, // 4: configs.v1.ClientService.GetCurrent:input_type -> configs.v1.GetCurrentRequest
	4, // 5: configs.v1.ClientService.SaveCurrent:input_type -> configs.v1.Client
	3, // 6: configs.v1.ClientService.DeleteClient:input_type -> configs.v1.DeleteClientRequest
	2, // 7: configs.v1.ClientService.ListClients:output_type -> configs.v1.ListClientsResponse
	0, // 8: configs.v1.ClientService.SaveClient:output_type -> configs.v1.ClientResponse
	0, // 9: configs.v1.ClientService.GetCurrent:output_type -> configs.v1.ClientResponse
	0, // 10: configs.v1.ClientService.SaveCurrent:output_type -> configs.v1.ClientResponse
	0, // 11: configs.v1.ClientService.DeleteClient:output_type -> configs.v1.ClientResponse
	7, // [7:12] is the sub-list for method output_type
	2, // [2:7] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_clients_v1_client_proto_init() }
func file_clients_v1_client_proto_init() {
	if File_clients_v1_client_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_clients_v1_client_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_v1_client_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCurrentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_v1_client_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClientsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_v1_client_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_clients_v1_client_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Client); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_clients_v1_client_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_clients_v1_client_proto_goTypes,
		DependencyIndexes: file_clients_v1_client_proto_depIdxs,
		MessageInfos:      file_clients_v1_client_proto_msgTypes,
	}.Build()
	File_clients_v1_client_proto = out.File
	file_clients_v1_client_proto_rawDesc = nil
	file_clients_v1_client_proto_goTypes = nil
	file_clients_v1_client_proto_depIdxs = nil
}
