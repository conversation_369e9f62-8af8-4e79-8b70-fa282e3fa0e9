import{u as e,r as a,b as t,j as o,k as r,l as n,i as s}from"./index.6227d37e.js";import{C as i}from"./index.a7402227.js";import{T as m}from"./index.5990d978.js";import{i as l,S as p}from"./search-form.d1109672.js";import{getColumns as d}from"./constants.e454189a.js";import"./index.be17d0db.js";function c(){const c=e(l),g=async(e,a)=>{if("delete"===a)r.confirm({title:c["program.tables.main.modal.delete.title"],content:c["program.tables.main.modal.delete.content"],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}`,{method:"delete"}).then((()=>{s.success(c["program.tables.main.modal.delete.operation.success"]),$()})).catch((()=>{s.error(c["program.tables.main.modal.delete.operation.fail"])})).finally((()=>{z(!1)}))}});else r.confirm({title:c[`program.tables.main.modal.${a}.title`],content:c[`program.tables.main.modal.${a}.content`],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}/cmds`,{method:"post",data:{type:a},timeout:6e4}).then((e=>{const t=e&&e.data;(t||t.data)&&(0!==t.code?s.error(c[`program.tables.main.modal.${a}.operation.fail`]):s.success(c[`program.tables.main.modal.${a}.operation.success`]),$())})).catch((()=>{s.error(c[`program.tables.main.modal.${a}.operation.fail`])})).finally((()=>{z(!1)}))}})},u=a.exports.useMemo((()=>d(c,g)),[c]),[f,S]=a.exports.useState([]),[h,b]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[x,z]=a.exports.useState(!0),[j,y]=a.exports.useState({});function $(){const{current:e,pageSize:a}=h;z(!0),n("/api/v1/programs",{data:{page:e,size:a,...j}}).then((t=>{const o=t&&t.data;(o||o.data||o.data.content)&&(S(o.data.content),b({...h,current:e,pageSize:a,total:o.data.total}))})).finally((()=>{z(!1)}))}return a.exports.useEffect((()=>{$()}),[h.current,h.pageSize,JSON.stringify(j)]),t(i,{children:[o(p,{loading:x,onSearch:function(e){b({...h,current:1}),y(e)}}),o(m,{rowKey:"id",loading:x,onChange:function({current:e,pageSize:a}){b({...h,current:e,pageSize:a})},pagination:h,columns:u,data:f})]})}export{c as default};
