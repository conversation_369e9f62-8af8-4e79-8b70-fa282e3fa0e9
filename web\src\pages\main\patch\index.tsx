import React, { useState, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import {
  Table,
  Card,
  Message,
  Modal,
} from '@arco-design/web-react';

import api from '@/utils/api';
import useLocale from '@/utils/useLocale';

import SearchForm from './search-form';
import locale from './locale';
import { getColumns } from './constants';

function ITable() {
  const t = useLocale(locale);

  const tableCallback = async (record, type) => {
    switch (type) {
      case 'delete':
        Modal.confirm({
          title: t['patch.tables.main.modal.delete.title'],
          content: t['patch.tables.main.modal.delete.content'],
          onOk: () => {
            setLoading(true);
            api('/api/v1/patches', {
              method: 'delete',
              data: {
                name: record.name,
                packageName: record.packageName,
              },
            }).then(() => {
              Message.success(t['patch.tables.main.modal.delete.operation.success']);
              fetchData();
            }).catch(() => {
              Message.error(t['patch.tables.main.modal.delete.operation.fail']);
            }).finally(() => {
              setLoading(false);
            });
          },
        });
        break;
      default:
        Message.warning(t['patch.tables.main.operation.no-support']);
        break;
    }
  };

  const columns = useMemo(() => getColumns(t, tableCallback), [t]);

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formParams, setFormParams] = useState({});

  function fetchData() {
    setLoading(true);
    api('/api/v1/patches', {
      data: {
        ...formParams,
      }
    })
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data && !resData.data) {
          return;
        }
        setData(resData.data);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function handleSearch(params) {
    setFormParams(params);
  }

  useEffect(() => {
    fetchData();
}, [JSON.stringify(formParams)]);

  return (
    <Card>
      <SearchForm
        loading={loading}
        onSearch={handleSearch}
      />
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        data={data}
        pagination={false}
      />
    </Card>
  );
}

export default ITable;
