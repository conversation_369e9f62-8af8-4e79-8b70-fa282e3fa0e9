const i18n = {
  'en-US': {
    'main.mode.user': 'Client',
    'main.mode.admin': 'Server',
    'main.message.mode.tips': 'Current mode is ',
    'main.message.server-not-set.tips': 'No server is set',
    'main.message.server-not-found.tips': 'Server not found, please check it.',
    'main.message.app-not-found.tips':
      'No available app found, please check it.',
    'main.message.auto-scan.tips': 'automatic scan in 5 seconds.',
    'main.message.server-configured.tips': 'Server is configured',
    'main.message.operation.success': 'Operation succeeded',
    'main.message.operation.fail': 'Operation failed',
    'main.modal.download.title': 'Download confirmation',
    'main.modal.download.content': 'Are you sure you want to download?',
    'main.modal.download.operation.success': 'Download successfully',
    'main.modal.download.operation.fail': 'Download failed',
    'menu.welcome': 'Welcome',
    'menu.dashboard': 'Dashboard',
    'menu.list': 'List',
    'menu.result': 'Result',
    'menu.exception': 'Exception',
    'menu.form': 'Form',
    'menu.profile': 'Profile',
    'menu.visualization': 'Data Visualization',
    'menu.user': 'User Center',
    'menu.exception.403': '403',
    'menu.exception.404': '404',
    'menu.exception.500': '500',
    'menu.profile.basic': 'Basic Profile',
    'menu.list.cardList': 'Card List',
    'menu.visualization.dataAnalysis': 'Analysis',
    'menu.result.error': 'Error',
    'menu.form.group': 'Group Form',
    'menu.dashboard.monitor': 'Real-time Monitor',
    'menu.visualization.multiDimensionDataAnalysis': 'Multi-D Analysis',
    'menu.list.searchTable': 'Search Table',
    'menu.form.step': 'Step Form',
    'menu.result.success': 'Success',
    'menu.user.info': 'User Info',
    'menu.user.setting': 'User Setting',
    'menu.user.switchRoles': 'Switch Roles',
    'menu.user.role.admin': 'Admin',
    'menu.user.role.user': 'General User',
    'menu.dashboard.workplace': 'Workplace',
    'menu.main': 'Main',
    'menu.main.center': 'Control Center',
    'menu.main.log': 'Log List',
    'menu.main.os': 'System Information',
    'menu.main.patch': 'Patch Management',
    'menu.main.program': 'Program Monitor',
    'menu.main.store': 'App Store',
    'menu.main.store.importForm': 'App Import',
    'menu.main.store.localImport': 'Local Import',
    'menu.main.store.stepForm': 'App Publish',
    'menu.main.store.updateForm': 'App Update',
    'menu.main.store.upgradeForm': 'App Upgrade',
    'menu.main.setting': 'Software Settings',
    'navbar.logout': 'Logout',
    'settings.title': 'Settings',
    'settings.themeColor': 'Theme Color',
    'settings.content': 'Content Setting',
    'settings.navbar': 'Navbar',
    'settings.menuWidth': 'Menu Width (px)',
    'settings.navbar.theme.toLight': 'Click to use light mode',
    'settings.navbar.theme.toDark': 'Click to use dark mode',
    'settings.navbar.mode.title': 'Software Mode',
    'settings.navbar.mode.toClient': 'Click to use client mode',
    'settings.navbar.mode.toServer': 'Click to use server mode',
    'settings.menu': 'Menu',
    'settings.footer': 'Footer',
    'settings.otherSettings': 'Other Settings',
    'settings.colorWeek': 'Color Week',
    'settings.alertContent':
      'After the configuration is only temporarily effective, if you want to really affect the project, click the "Copy Settings" button below and replace the configuration in settings.json.',
    'settings.copySettings': 'Copy Settings',
    'settings.copySettings.message':
      'Copy succeeded, please paste to file src/settings.json.',
    'settings.close': 'Close',
    'settings.color.tooltip':
      '10 gradient colors generated according to the theme color',
    'message.tab.title.message': 'Message',
    'message.tab.title.notice': 'Notice',
    'message.tab.title.todo': 'ToDo',
    'message.allRead': 'All Read',
    'message.seeMore': 'SeeMore',
    'message.empty': 'Empty',
    'message.empty.tips': 'No Content',
    'message.lang.tips': 'Language switch to ',
    'message.mqtt.connection.success': 'Connected to the server',
    'message.mqtt.connection.lost': 'Connection to the server is lost',
    'message.mqtt.operations.subscribe.fail': 'Subscribe failed',
    'message.mqtt.operations.connect.fail': 'Connect failed',
    'message.mqtt.operations.control.success': 'Operation succeeded',
    'message.mqtt.operations.control.fail': 'Operation failed',
    'navbar.search.placeholder': 'Please search',
    'system.title': 'CCMS',
  },
  'zh-CN': {
    'main.mode.user': '客户端模式',
    'main.mode.admin': '服务端模式',
    'main.message.mode.tips': '当前为',
    'main.message.server-not-set.tips': '未设置服务端',
    'main.message.server-not-found.tips': '未找到服务端，请检查。',
    'main.message.app-not-found.tips': '未找到可用应用，请检查。',
    'main.message.auto-scan.tips': '5秒后自动扫描',
    'main.message.server-configured.tips': '服务端已配置',
    'main.message.operation.success': '操作成功',
    'main.message.operation.fail': '操作失败',
    'main.modal.download.title': '下载确认',
    'main.modal.download.content': '确定下载吗？',
    'main.modal.download.operation.success': '下载成功',
    'main.modal.download.operation.fail': '下载失败',
    'main.operation.download': '下载',
    'menu.dashboard': '仪表盘',
    'menu.list': '列表页',
    'menu.result': '结果页',
    'menu.exception': '异常页',
    'menu.form': '表单页',
    'menu.profile': '详情页',
    'menu.visualization': '数据可视化',
    'menu.user': '个人中心',
    'menu.exception.403': '403',
    'menu.exception.404': '404',
    'menu.exception.500': '500',
    'menu.profile.basic': '基础详情页',
    'menu.list.cardList': '卡片列表',
    'menu.visualization.dataAnalysis': '分析页',
    'menu.result.error': '失败页',
    'menu.form.group': '分组表单',
    'menu.dashboard.monitor': '实时监控',
    'menu.visualization.multiDimensionDataAnalysis': '多维数据分析',
    'menu.list.searchTable': '查询表格',
    'menu.form.step': '分步表单',
    'menu.result.success': '成功页',
    'menu.user.info': '用户信息',
    'menu.user.setting': '设置',
    'menu.user.switchRoles': '切换角色',
    'menu.user.role.admin': '管理员',
    'menu.user.role.user': '普通用户',
    'menu.dashboard.workplace': '工作台',
    'menu.main': '主页',
    'menu.main.center': '控制中心',
    'menu.main.log': '日志列表',
    'menu.main.os': '系统信息',
    'menu.main.patch': '补丁管理',
    'menu.main.program': '程序监控',
    'menu.main.store': '应用仓库',
    'menu.main.store.importForm': '应用导入',
    'menu.main.store.localImport': '本地导入',
    'menu.main.store.stepForm': '应用发布',
    'menu.main.store.updateForm': '应用更新',
    'menu.main.store.upgradeForm': '应用升级',
    'menu.main.setting': '软件设置',
    'navbar.logout': '登出',
    'settings.title': '页面配置',
    'settings.themeColor': '主题色',
    'settings.content': '内容区域',
    'settings.navbar': '导航栏',
    'settings.menuWidth': '菜单宽度 (px)',
    'settings.navbar.theme.toLight': '点击切换为亮色模式',
    'settings.navbar.theme.toDark': '点击切换为暗黑模式',
    'settings.navbar.mode.title': '软件模式',
    'settings.navbar.mode.toClient': '点击切换为客户端模式',
    'settings.navbar.mode.toServer': '点击切换为服务端模式',
    'settings.menu': '菜单栏',
    'settings.footer': '底部',
    'settings.otherSettings': '其他设置',
    'settings.colorWeek': '色弱模式',
    'settings.alertContent':
      '配置之后仅是临时生效，要想真正作用于项目，点击下方的 "复制配置" 按钮，将配置替换到 settings.json 中即可。',
    'settings.copySettings': '复制配置',
    'settings.copySettings.message':
      '复制成功，请粘贴到 src/settings.json 文件中',
    'settings.close': '关闭',
    'settings.color.tooltip':
      '根据主题颜色生成的 10 个梯度色（将配置复制到项目中，主题色才能对亮色 / 暗黑模式同时生效）',
    'message.tab.title.message': '消息',
    'message.tab.title.notice': '通知',
    'message.tab.title.todo': '待办',
    'message.allRead': '全部已读',
    'message.seeMore': '查看更多',
    'message.empty': '清空',
    'message.empty.tips': '暂无内容',
    'message.lang.tips': '语言切换至 ',
    'message.mqtt.connection.success': '与服务器已建立连接',
    'message.mqtt.connection.lost': '与服务器连接已断开',
    'message.mqtt.operations.subscribe.fail': '订阅失败',
    'message.mqtt.operations.connect.fail': '连接失败',
    'message.mqtt.operations.control.success': '操作成功',
    'message.mqtt.operations.control.fail': '操作失败',
    'navbar.search.placeholder': '输入内容查询',
    'system.title': 'CCMS',
  },
};

export default i18n;
