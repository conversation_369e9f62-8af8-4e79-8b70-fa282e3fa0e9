import{u as a,b as e,j as o,s,T as t}from"./index.e8bac691.js";import{l as n,D as l}from"./index.b8bf3dbf.js";import{C as r}from"./index.a97db34b.js";function i(i){const d=a(n),{data:m}=i,u=[];if(m.networks)for(const[,a]of Object.entries(m.networks))u.push({label:d["os.network.name"],value:a.name}),u.push({label:d["os.network.family"],value:a.family}),u.push({label:d["os.network.address"],value:a.address}),u.push({label:d["os.network.mac"],value:a.mac});return e(r,{children:[o(s,{align:"start",children:o(t.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:d["os.network.title"]})}),o(l,{colon:": ",layout:"horizontal",data:u,column:4})]})}export{i as default};
