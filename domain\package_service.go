package domain

import (
	"fmt"
	"log/slog"
	"os"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
)

func CheckPackageScheduleTask() {
	slog.Debug("Check package schedule task")
	do := &Package{}
	pkgs, err := do.List()
	if err != nil {
		slog.Error("List packages error: " + err.<PERSON><PERSON>r())
		return
	}
	if len(pkgs) > 0 {
		changedPkgs := make([]*Package, 0)
		for i := range pkgs {
			if !pkgs[i].IsDownloaded() || !pkgs[i].IsInstalled() {
				continue
			}
			pkgs[i].State = PackageStates.Unknown
			appDir, err := pkgs[i].GetAppDir()
			if err != nil {
				slog.Warn(fmt.Sprintf("Package(%s) get app dir error: %s", pkgs[i].Name, err.<PERSON><PERSON><PERSON>()))
				pkgs[i].Installed = 0
			} else {
				if _, err := os.Stat(appDir); os.IsNotExist(err) {
					pkgs[i].Installed = 0
				} else {
					pkgs[i].Installed = 1
				}
				err = pkgs[i].Health(false)
				if err != nil {
					slog.Debug(fmt.Sprintf("Package(%s) health check error: %s", pkgs[i].Name, err.Error()))
				}
			}
			changedPkgs = append(changedPkgs, pkgs[i])
		}
		if len(changedPkgs) > 0 {
			err := do.SaveAll(changedPkgs)
			if err != nil {
				slog.Error("Save packages error: " + err.Error())
			}
		}
	}
	slog.Debug("Check package schedule task successful")
}

func PackageVO2PackagePO(vo *packagesv1.Package) *Package {
	if vo == nil {
		return nil
	}
	po := &Package{
		Id:          vo.Id,
		Name:        vo.Name,
		Version:     vo.Version,
		Type:        vo.Type,
		Size:        vo.Size,
		State:       vo.State,
		Uri:         vo.Uri,
		PackagePath: vo.PackagePath,
		InstallPath: vo.InstallPath,
		Support:     vo.Support,
		Description: vo.Description,
		Installed:   vo.Installed,
		Downloaded:  vo.Downloaded,
		Upgradable:  vo.Upgradable,
	}
	if vo.CreatedAt != "" {
		createdAt, err := time.Parse("2006-01-02 15:04:05", vo.CreatedAt)
		if err == nil {
			po.CreatedAt = createdAt
		}
	}
	if vo.UpdatedAt != "" {
		updatedAt, err := time.Parse("2006-01-02 15:04:05", vo.UpdatedAt)
		if err == nil {
			po.UpdatedAt = updatedAt
		}
	}
	return po
}

func PackageVOs2PackagePOs(vos []*packagesv1.Package) []*Package {
	pos := make([]*Package, 0)
	for _, vo := range vos {
		po := PackageVO2PackagePO(vo)
		pos = append(pos, po)
	}
	return pos
}

func PackagePOs2PackageVOs(pos []*Package) []*packagesv1.Package {
	vos := make([]*packagesv1.Package, 0)
	for _, po := range pos {
		vo := po.ToVO()
		vos = append(vos, vo)
	}
	return vos
}

func GetPackageById(id int32) (*Package, error) {
	pkg := &Package{
		Id: id,
	}
	err := pkg.GetById()
	if err != nil {
		return nil, err
	}
	return pkg, nil
}

func GetPackageByName(name string) (*Package, error) {
	do := &Package{
		Name: name,
	}
	pkgs, err := do.ListByNameAndVersion()
	if err != nil {
		return nil, err
	}
	if len(pkgs) == 0 {
		return nil, fmt.Errorf("package(%s) not found", name)
	}
	return pkgs[0], nil
}

func DownloadPackage(pkg *Package) bool {
	if pkg.IsDownloading() {
		slog.Warn(fmt.Sprintf("Package(%s) is downloading", pkg.Name))
		return false
	}
	downloaded := true
	slog.Info(fmt.Sprintf("Download package(%s)", pkg.Name))
	current, err := GetAndSaveCurrent()
	if err != nil {
		slog.Error(fmt.Sprintf("Get current err: " + err.Error()))
		return false
	}
	addr := current.GetParentGrpcServer()
	if addr == "" {
		slog.Error("Parent server is empty")
		return false
	}
	err = pkg.DownloadFrom(addr)
	if err != nil {
		slog.Error(fmt.Sprintf("Package(%s) download error: %s", pkg.Name, err.Error()))
		if _, err := os.Stat(pkg.Uri); !os.IsNotExist(err) {
			os.Remove(pkg.Uri)
			slog.Info("Remove " + pkg.Uri)
			pkg.Uri = ""
			pkg.Downloaded = 0
		}
		downloaded = false
	} else {
		slog.Info(fmt.Sprintf("Download package(%s) to %s successful", pkg.Name, pkg.Uri))
	}
	err = pkg.Update()
	if err != nil {
		slog.Error(fmt.Sprintf("Save package(%s) error: %s", pkg.Name, err.Error()))
		return false
	}
	return downloaded
}

func UnzipPackage(pkg *Package) bool {
	slog.Info("Unzip " + pkg.Name)
	pkgsDir, err := config.GetPackagesDir()
	if err != nil {
		slog.Error("Failed to get packages dir, " + err.Error())
	}
	err = pkg.UnzipTo(pkgsDir)
	if err != nil {
		slog.Error(fmt.Sprintf("Package(%s) unzip error: %s", pkg.Name, err.Error()))
		return false
	}
	slog.Info(fmt.Sprintf("Extract %s to %s successful", pkg.Uri, pkgsDir))
	return true
}

func DownloadPatch(pkg *Package, patchName string) bool {
	downloaded := true
	slog.Info(fmt.Sprintf("Download patch(%s)", patchName))
	current, err := GetAndSaveCurrent()
	if err != nil {
		slog.Error(fmt.Sprintf("Get current err: " + err.Error()))
		return false
	}
	addr := current.GetParentGrpcServer()
	if addr == "" {
		slog.Error("Parent server is empty")
		return false
	}
	err = pkg.DownloadPatchFrom(addr, patchName)
	if err != nil {
		slog.Error(fmt.Sprintf("Patch(%s) download error: %s", patchName, err.Error()))
		downloaded = false
	} else {
		slog.Info(fmt.Sprintf("Download patch(%s) successful", patchName))
	}
	return downloaded
}
