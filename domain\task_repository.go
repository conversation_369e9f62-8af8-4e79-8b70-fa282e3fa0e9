package domain

import (
	"fmt"
	"time"
)

func (t *Task) Add() error {
	if t.CreatedAt == (time.Time{}) {
		t.CreatedAt = time.Now()
	}
	if t.UpdatedAt == (time.Time{}) {
		t.UpdatedAt = time.Now()
	}
	return Add(t)
}

func (t *Task) DeleteByID() error {
	return DeleteByID(t.Id, t)
}

func (t *Task) GetById() error {
	err := FindOneByID(t.Id, t)
	if err != nil {
		return err
	}
	return nil
}

func (t *Task) List() ([]*Task, error) {
	db, err := GetDBInstance()
	if err != nil {
		return nil, err
	}
	var clients []*Task
	err = db.Find(&clients).
		Order("updated_at DESC").Error
	if err != nil {
		return nil, err
	}
	return clients, nil
}

func (t *Task) Update() error {
	if t.Id == 0 {
		return fmt.Errorf("id is required")
	}
	if t.UpdatedAt == (time.Time{}) {
		t.UpdatedAt = time.Now()
	}
	return t.Save()
}

func (t *Task) Save() error {
	return Save(t)
}
