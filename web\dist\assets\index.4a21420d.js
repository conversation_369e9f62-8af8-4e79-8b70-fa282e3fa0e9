import{R as e,r as t,e as r,j as a,_ as o,an as n,ao as s,F as i,ap as c,u as l,b as p,s as d,aq as m,B as u,k as f,l as h,i as b,ar as g}from"./index.e8bac691.js";import{C as O}from"./index.a97db34b.js";import{T as y}from"./index.acd49a0a.js";import{i as j,S as v,s as x}from"./search-form.974d6437.js";import{getColumns as P}from"./constants.53f4502d.js";import"./index.dde5f5a0.js";function w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function k(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function S(e,o){var n=t.exports.useContext(r).prefixCls,s=void 0===n?"arco":n,i=e.spin,c=e.className,l=k(k({"aria-hidden":!0,focusable:!1,ref:o},e),{},{className:"".concat(c?c+" ":"").concat(s,"-icon ").concat(s,"-icon-import")});return i&&(l.className="".concat(l.className," ").concat(s,"-icon-loading")),delete l.spin,delete l.isIcon,a("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:a("path",{d:"m27.929 33.072-9.071-9.07 9.07-9.072M43 24H19m12 17H7V7h24"})})}var C=e.forwardRef(S);C.defaultProps={isIcon:!0},C.displayName="IconImport";var z=C;function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function D(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function I(e,o){var n=t.exports.useContext(r).prefixCls,s=void 0===n?"arco":n,i=e.spin,c=e.className,l=D(D({"aria-hidden":!0,focusable:!1,ref:o},e),{},{className:"".concat(c?c+" ":"").concat(s,"-icon ").concat(s,"-icon-folder")});return i&&(l.className="".concat(l.className," ").concat(s,"-icon-loading")),delete l.spin,delete l.isIcon,a("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:a("path",{d:"M6 13h18l-2.527-3.557a1.077 1.077 0 0 0-.88-.443H7.06C6.474 9 6 9.448 6 10v3Zm0 0h33.882c1.17 0 2.118.895 2.118 2v21c0 1.105-.948 3-2.118 3H8.118C6.948 39 6 38.105 6 37V13Z"})})}var $=e.forwardRef(I);$.defaultProps={isIcon:!0},$.displayName="IconFolder";var E=$;const q=e=>{const{backup:r,requiredPermissions:o,oneOfPerm:c}=e,l=n((e=>e.userInfo));return t.exports.useMemo((()=>s({requiredPermissions:o,oneOfPerm:c},l.permissions)),[c,o,l.permissions])?a(i,{children:H(e.children)}):r?a(i,{children:H(r)}):null};function H(t){return e.isValidElement(t)?t:a(i,{children:t})}function M(){const e=c(),r=l(j),o=async(t,o)=>{switch(o){case"upgrade":e.push({pathname:"/main/store-upgrade-form",state:t});break;case"update":e.push({pathname:"/main/store-update-form",state:t});break;case"delete":f.confirm({title:r["store.tables.main.modal.delete.title"],content:r["store.tables.main.modal.delete.content"],onOk:()=>{C(!0),h(`/api/v1/packages/${t.id}`,{method:"delete"}).then((()=>{b.success(r["store.tables.main.modal.delete.operation.success"]),I()})).catch((()=>{b.error(r["store.tables.main.modal.delete.operation.fail"])})).finally((()=>{C(!1)}))}});break;default:f.confirm({title:r[`store.tables.main.modal.${o}.title`],content:r[`store.tables.main.modal.${o}.content`],onOk:()=>{C(!0),h("/api/v1/tasks",{method:"post",data:{name:o,packageId:t.id},timeout:60*("export"===o?5:1)*1e3}).then((e=>{const t=e&&e.data;if(t||t.data){if("failed"===t.data.state)b.error(r[`store.tables.main.modal.${o}.operation.fail`]);else{if("export"===o){const e=`${Date.now()}`,o=t.data.content;g.info({id:e,title:r["store.notifications.export.title"],content:`${r["store.notifications.export.location"]}: ${o}`,duration:0,btn:p("span",{children:[a(u,{type:"secondary",size:"small",onClick:()=>g.remove(e),style:{margin:"0 12px"},children:r["store.const.cancel"]}),a(u,{type:"primary",size:"small",onClick:()=>g.remove(e),children:r["store.const.ok"]})]})})}b.success(r[`store.tables.main.modal.${o}.operation.success`])}I()}})).catch((()=>{b.error(r[`store.tables.main.modal.${o}.operation.fail`])})).finally((()=>{C(!1)}))}})}},n=t.exports.useMemo((()=>P(r,o)),[r]),[s,i]=t.exports.useState([]),[w,k]=t.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[S,C]=t.exports.useState(!0),[N,D]=t.exports.useState({});function I(){const{current:e,pageSize:t}=w;C(!0),h("/api/v1/packages",{data:{page:e,size:t,...N}}).then((r=>{const a=r&&r.data;(a||a.data||a.data.content)&&(i(a.data.content),k({...w,current:e,pageSize:t,total:a.data.total}))})).finally((()=>{C(!1)}))}return t.exports.useEffect((()=>{I()}),[w.current,w.pageSize,JSON.stringify(N)]),p(O,{children:[a(v,{loading:S,onSearch:function(e){k({...w,current:1}),D(e)}}),a(q,{requiredPermissions:[{resource:"menu.main.store",actions:["write"]}],children:a("div",{className:x["button-group"],children:p(d,{children:[a(m,{to:"/main/store-import-form",children:a(u,{loading:S,type:"primary",icon:a(z,{}),children:r["store.tables.main.operation.import"]})}),a(m,{to:"/main/store-local-import",children:a(u,{loading:S,type:"outline",icon:a(E,{}),children:"本地导入"})})]})})}),a(y,{rowKey:"id",loading:S,onChange:function({current:e,pageSize:t}){k({...w,current:e,pageSize:t})},pagination:w,columns:n,data:s})]})}export{M as default};
