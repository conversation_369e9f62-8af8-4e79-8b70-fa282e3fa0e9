import{u as a,o as t,p as e,r,l as s,j as o,i as n}from"./index.6227d37e.js";import{C as i}from"./index.a7402227.js";import{i as d,S as c}from"./software.2ebb4665.js";/* empty css              */function p(){const p=a(d),[l,h]=t(e.KEY_AIS_SERVER_NAME),[S,f,E]=t(e.KEY_AIS_SERVER_PARENT_HOST),[u,m,_]=t(e.KEY_AIS_SERVER_PARENT_SSL),[,v,R]=t(e.KEY_AIS_SERVER_PARENT_API_SERVER),[g,w]=r.exports.useState(!1),[P,A]=r.exports.useState({name:l,host:S,ssl:"true"===u,cacheDir:"",storageDir:"",version:"1.0.3"});return r.exports.useEffect((()=>{w(!0),s("/api/v1/clients/current").then((a=>{const t=a&&a.data;if(!t&&!t.data)return;const{name:e,parentHost:r,parentSsl:o}=t.data;w(!0),s("/api/v1/config").then((a=>{const t=a&&a.data;if(!t&&!t.data)return;const{cacheDir:s,storageDir:n,version:i}=t.data;A({...P,name:e,host:r||S,ssl:1===o||"true"===u,cacheDir:s,storageDir:n,version:i})})).finally((()=>{w(!1)}))})).finally((()=>{w(!1)}))}),[]),o("div",{children:o(i,{style:{marginTop:"16px"},children:o(c,{loading:g,data:P,callback:a=>{A({...P,...a}),h(a.name),a.host.length>0?(f(a.host),m(a.ssl),v(`http${a.ssl?"s":""}://${a.host}:5888`)):(E(),_(),R()),s("/api/v1/clients/current").then((t=>{const e=t&&t.data;if(e.data&&e.data.id){e.data.name=a.name||e.data.id,e.data.parentHost=a.host||"localhost",e.data.parentSSL="true"===a.ssl?1:0,e.data.parentGrpcPort=5555,e.data.parentGatewayPort=5888;if(""===e.data.parentHost||"localhost"===e.data.parentHost||"127.0.0.1"===e.data.parentHost)e.data.parentId=e.data.id,s("/api/v1/clients/current",{method:"post",data:e.data}).then((()=>{window.location.reload()})).catch((()=>{n.error(p["softwareSetting.operations.save.fail"])}));else{const a=`http${e.data.parentSSL?"s":""}://${e.data.parentHost}:${e.data.parentGatewayPort}`;s("/api/v1/clients/current",{serverUrl:a}).then((a=>{const t=a&&a.data&&a.data.data;t&&(e.data.parentId=t.id,e.data.parentGrpcPort=t.grpcPort,e.data.parentGatewayPort=t.gatewayPort),s("/api/v1/clients/current",{method:"post",data:e.data}).then((()=>{window.location.reload()})).catch((()=>{n.error(p["softwareSetting.operations.save.fail"])}))})).catch((()=>{n.error(p["softwareSetting.operations.save.fail"])}))}}}))}})})})}export{p as default};
