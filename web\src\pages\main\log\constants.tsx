import React from 'react';

import IconText from './icons/text.svg';
import IconHorizontalVideo from './icons/horizontal.svg';
import IconVerticalVideo from './icons/vertical.svg';

const ContentIcon = [
  <IconText key={0} />,
  <IconHorizontalVideo key={1} />,
  <IconVerticalVideo key={2} />,
];

export function getColumns(
  t: any
) {
  return [
    {
      title: t['searchTable.columns.createdAt'],
      dataIndex: 'createdAt',
    },
    {
      title: t['searchTable.columns.level'],
      dataIndex: 'level',
      render: (x) => {
        let color = '';
        switch (x) {
          case 'DEBUG':
            color = 'gray';
            break;
          case 'INFO':
            color = 'blue';
            break;
          case 'WARN':
            color = 'orange';
            break;
          case 'ERROR':
            color = 'red';
            break;
          default:
            color = 'blue';
            break;
        }
        return <span style={{ color }}>{x}</span>;
      },
    },
    {
      title: t['searchTable.columns.message'],
      dataIndex: 'message',
    }
  ];
}

export default () => ContentIcon;
