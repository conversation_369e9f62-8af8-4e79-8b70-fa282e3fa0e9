# This file is a template, and might need editing before it works on your project.
image: grpc/go

before_script:
  - export REPO_NAME=`echo $CI_PROJECT_URL|sed 's/.*:\/\///g;'`
  - mkdir -p $GOPATH/src/$(dirname $REPO_NAME)
  - ln -svf $CI_PROJECT_DIR $GOPATH/src/$REPO_NAME
  - cd $GOPATH/src/$REPO_NAME
  - go get

stages:
    - test
    - build

format:
    stage: test
    script:
      - go fmt $(go list ./... | grep -v /vendor/)
      - go vet $(go list ./... | grep -v /vendor/)
    
test:     
    stage: test
    script:
      - go test -race $(go list ./... | grep -v /vendor/)

compile:
    stage: build
    script:
      - go get github.com/micro/protoc-gen-micro
      - protoc --proto_path=$GOPATH:. --micro_out=. --go_out=. $GOPATH/src/$REPO_NAME/proto/greeter.proto
      - go build -race -ldflags "-extldflags '-static'" -o $CI_PROJECT_DIR/service
    artifacts:
      paths:
        - service
