package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"

	"gitlab.jhonginfo.com/product/ais-server/lib"
	configsv1 "gitlab.jhonginfo.com/product/ais-server/proto/configs/v1"
	"gopkg.in/yaml.v3"
)

type Config struct {
	Id           string `yaml:"-" json:"id"`
	Name         string `yaml:"-" json:"name" default:"ais-server"`
	Version      string `yaml:"-" json:"version" default:"1.0.0"`
	GrpcPort     int32  `yaml:"grpc_port" json:"grpc_port" default:"5555"`
	GatewayPort  int32  `yaml:"gateway_port" json:"gateway_port" default:"5888"`
	MqttTcpPort  int32  `yaml:"mqtt_tcp_port" json:"mqtt_tcp_port" default:"5883"`
	MqttWsPort   int32  `yaml:"mqtt_ws_port" json:"mqtt_ws_port" default:"5884"`
	SSL          bool   `yaml:"ssl" json:"ssl" default:"false"`
	LogLevel     string `env:"AIS_LOG_LEVEL" yaml:"log_level" json:"log_level" default:"INFO"`
	CacheDir     string `env:"AIS_CACHE_DIR" yaml:"cache_dir" json:"cache_dir" default:"/tmp"`
	StorageDir   string `env:"AIS_STORAGE_DIR" yaml:"storage_dir" json:"storage_dir" default:"~/.ais"`
	DatabaseFile string `env:"AIS_DATABASE_FILE" yaml:"database_file" json:"database_file" default:"~/.ais/ais.db"`

	// 解压缩性能配置
	ExtractThreads    int32 `yaml:"extract_threads" json:"extract_threads" default:"0"`         // 解压缩线程数，0=使用所有CPU核心
	ExtractQueueSize  int32 `yaml:"extract_queue_size" json:"extract_queue_size" default:"10"`  // 解压缩队列大小
	ExtractLargeFile  int64 `yaml:"extract_large_file" json:"extract_large_file" default:"104857600"` // 大文件阈值(100MB)
	ExtractHighPerf   bool  `yaml:"extract_high_perf" json:"extract_high_perf" default:"true"`  // 启用高性能解压缩

	// 文件复制性能配置
	CopyBufferSize    int32 `yaml:"copy_buffer_size" json:"copy_buffer_size" default:"1048576"`     // 复制缓冲区大小(1MB)
	CopyWorkers       int32 `yaml:"copy_workers" json:"copy_workers" default:"4"`                  // 并发复制工作线程数
	CopyBatchSize     int32 `yaml:"copy_batch_size" json:"copy_batch_size" default:"100"`          // 批量复制文件数
}

var defaultConfig = &Config{
	Id:           "",
	Name:         "ais-server",
	Version:      "1.1.3",
	LogLevel:     "INFO",
	GrpcPort:     5555,
	GatewayPort:  5888,
	MqttTcpPort:  5883,
	MqttWsPort:   5884,
	SSL:          false,
	CacheDir:     "",
	StorageDir:   "",
	DatabaseFile: "ais.db",

	// 性能优化默认配置
	ExtractThreads:   0,        // 使用所有CPU核心
	ExtractQueueSize: 10,       // 队列大小
	ExtractLargeFile: 104857600, // 100MB阈值
	ExtractHighPerf:  true,     // 启用高性能模式
	CopyBufferSize:   1048576,  // 1MB缓冲区
	CopyWorkers:      4,        // 4个并发工作线程
	CopyBatchSize:    100,      // 批量处理100个文件
}

func (t *Config) Load() error {
	exec, err := os.Executable()
	if err != nil {
		return err
	}
	confDir := filepath.Dir(exec)
	confFile := filepath.Join(confDir, "config.yml")
	if _, err := os.Stat(confFile); os.IsNotExist(err) {
		return err
	}
	// 读取配置文件
	content, err := os.ReadFile(confFile)
	if err != nil {
		return err
	}
	conf := &Config{}
	// 解析配置文件
	err = yaml.Unmarshal(content, &conf)
	if err != nil {
		return err
	}
	t.Merge(conf)
	return nil
}

func (t *Config) Save() error {
	exec, err := os.Executable()
	if err != nil {
		return err
	}
	confDir := filepath.Dir(exec)
	confFile := filepath.Join(confDir, "config.yml")
	// 序列化配置文件
	content, err := yaml.Marshal(t)
	if err != nil {
		return err
	}
	// 写入配置文件
	err = os.WriteFile(confFile, content, 0644)
	if err != nil {
		return err
	}
	return nil
}

func (t *Config) Merge(s *Config) {
	if s.GrpcPort != 0 {
		t.GrpcPort = s.GrpcPort
	}
	if s.GatewayPort != 0 {
		t.GatewayPort = s.GatewayPort
	}
	if s.MqttTcpPort != 0 {
		t.MqttTcpPort = s.MqttTcpPort
	}
	if s.MqttWsPort != 0 {
		t.MqttWsPort = s.MqttWsPort
	}
	if s.SSL {
		t.SSL = s.SSL
	}
	if s.LogLevel != "" {
		t.LogLevel = s.LogLevel
	}
	if s.CacheDir != "" {
		t.CacheDir = s.CacheDir
	}
	if s.StorageDir != "" {
		t.StorageDir = s.StorageDir
	}
	if s.DatabaseFile != "" {
		t.DatabaseFile = s.DatabaseFile
	}
}

func Get() *Config {
	if defaultConfig.StorageDir == "" {
		defaultConfig.Load()
	}
	envGrpcPort := os.Getenv("AIS_GRPC_PORT")
	if envGrpcPort != "" {
		GrpcPort, err := strconv.ParseInt(envGrpcPort, 10, 32)
		if err == nil {
			defaultConfig.GrpcPort = int32(GrpcPort)
		}
	}
	envGatewayPort := os.Getenv("AIS_GATEWAY_PORT")
	if envGatewayPort != "" {
		GatewayPort, err := strconv.ParseInt(envGatewayPort, 10, 32)
		if err == nil {
			defaultConfig.GatewayPort = int32(GatewayPort)
		}
	}
	envMqttTcpPort := os.Getenv("AIS_MQTT_TCP_PORT")
	if envMqttTcpPort != "" {
		MQTTTCPPort, err := strconv.ParseInt(envMqttTcpPort, 10, 32)
		if err == nil {
			defaultConfig.MqttTcpPort = int32(MQTTTCPPort)
		}
	}
	envMqttWsPort := os.Getenv("AIS_MQTT_WS_PORT")
	if envMqttWsPort != "" {
		MQTTWSPort, err := strconv.ParseInt(envMqttWsPort, 10, 32)
		if err == nil {
			defaultConfig.MqttWsPort = int32(MQTTWSPort)
		}
	}
	envCacheDir := os.Getenv("AIS_CACHE_DIR")
	if envCacheDir != "" {
		defaultConfig.CacheDir = envCacheDir
	}
	if defaultConfig.CacheDir == "" {
		cacheDir, _ := GetCacheDir()
		defaultConfig.CacheDir = cacheDir
	}
	envStorageDir := os.Getenv("AIS_STORAGE_DIR")
	if envStorageDir != "" {
		defaultConfig.StorageDir = envStorageDir
	}
	if defaultConfig.StorageDir == "" {
		storageDir, _ := GetStorageDir()
		defaultConfig.StorageDir = storageDir
	}
	envDatabaseFile := os.Getenv("AIS_DATABASE_FILE")
	if envDatabaseFile != "" {
		defaultConfig.DatabaseFile = envDatabaseFile
	}
	if defaultConfig.StorageDir != "" && defaultConfig.DatabaseFile == "ais.db" {
		defaultConfig.DatabaseFile = filepath.Join(defaultConfig.StorageDir, "ais.db")
	}
	envLogLevel := os.Getenv("AIS_LOG_LEVEL")
	if envLogLevel != "" {
		defaultConfig.LogLevel = envLogLevel
	}
	return defaultConfig
}

func Name() string {
	return Get().Name
}

func Version() string {
	return Get().Version
}

func LogLevel() string {
	return Get().LogLevel
}

func IsSSL() bool {
	return Get().SSL
}

func GetAddress() string {
	return fmt.Sprintf("0.0.0.0:%d", Get().GrpcPort)
}

func GetGatewayAddress() string {
	return fmt.Sprintf("0.0.0.0:%d", Get().GatewayPort)
}

func GetMqttTcpAddress() string {
	return fmt.Sprintf("0.0.0.0:%d", Get().MqttTcpPort)
}

func GetMqttWsAddress() string {
	return fmt.Sprintf("0.0.0.0:%d", Get().MqttWsPort)
}

func GetCacheDir() (string, error) {
	if defaultConfig.CacheDir != "" {
		return defaultConfig.CacheDir, nil
	}
	dir, err := os.UserCacheDir()
	if err != nil {
		return os.TempDir(), err
	}
	return dir, nil
}

func GetStorageDir() (string, error) {
	if defaultConfig.StorageDir != "" {
		return defaultConfig.StorageDir, nil
	}
	var storageDir string
	dir, err := os.UserHomeDir()
	if err != nil {
		// 获取当前二进制文件的路径
		exe, err := os.Executable()
		if err != nil {
			return "", err
		}
		// 使用二进制文件所在的目录作为默认的路径
		dir = filepath.Dir(exe)
	}
	storageDir = filepath.Join(dir, ".ais")
	if _, err := os.Stat(storageDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(storageDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return storageDir, nil
}

func GetLogDir() (string, error) {
	dir := Get().StorageDir
	logDir := filepath.Join(dir, "log")
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(logDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return logDir, nil
}

func GetStoreDir() (string, error) {
	dir := Get().StorageDir
	storeDir := filepath.Join(dir, "store")
	if _, err := os.Stat(storeDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(storeDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return storeDir, nil
}

func GetPackagesDir() (string, error) {
	dir := Get().StorageDir
	pkgsDir := filepath.Join(dir, "packages")
	if _, err := os.Stat(pkgsDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(pkgsDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return pkgsDir, nil
}

func GetAppsDir() (string, error) {
	dir := Get().StorageDir
	appsDir := filepath.Join(dir, "apps")
	if _, err := os.Stat(appsDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(appsDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return appsDir, nil
}

func GetScriptsDir() (string, error) {
	dir := Get().StorageDir
	storeDir := filepath.Join(dir, "scripts")
	if _, err := os.Stat(storeDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(storeDir, 0755)
		if err != nil {
			return "", err
		}
	}
	return storeDir, nil
}

func GetId() (*string, error) {
	conf := Get()
	if conf.Id != "" {
		return &conf.Id, nil
	}
	var id string
	idFile := filepath.Join(conf.StorageDir, ".id")
	if _, err := os.Stat(idFile); os.IsNotExist(err) {
		id = ""
	} else {
		idBytes, err := os.ReadFile(idFile)
		if err != nil {
			return nil, err
		}
		id = string(idBytes)
	}
	if id == "" {
		id = lib.NewUUID()
		err := os.WriteFile(idFile, []byte(id), 0644)
		if err != nil {
			return nil, err
		}
		// return nil, fmt.Errorf("id is empty")
	}
	conf.Id = id
	return &conf.Id, nil
}

func (c *Config) Show() {
	jsonStr, _ := json.MarshalIndent(c, "", "  ")
	fmt.Println(string(jsonStr))
}

func (c *Config) ToVO() *configsv1.Config {
	return &configsv1.Config{
		CacheDir:     c.CacheDir,
		StorageDir:   c.StorageDir,
		DatabaseFile: c.DatabaseFile,
		Version:      c.Version,
	}
}
