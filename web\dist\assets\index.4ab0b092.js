import{R as e,r,e as t,j as n,_ as a,k as s,B as c,b as o,aK as l,s as i,T as d,aA as u,l as p}from"./index.e8bac691.js";import{C as f}from"./index.a97db34b.js";import{P as h,a as m}from"./index.0218e733.js";function y(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function v(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?y(Object(t),!0).forEach((function(r){a(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):y(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function g(e,a){var s=r.exports.useContext(t).prefixCls,c=void 0===s?"arco":s,o=e.spin,l=e.className,i=v(v({"aria-hidden":!0,focusable:!1,ref:a},e),{},{className:"".concat(l?l+" ":"").concat(c,"-icon ").concat(c,"-icon-check-circle")});return o&&(i.className="".concat(i.className," ").concat(c,"-icon-loading")),delete i.spin,delete i.isIcon,n("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...i,children:n("path",{d:"m15 22 7 7 11.5-11.5M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z"})})}var b=e.forwardRef(g);b.defaultProps={isIcon:!0},b.displayName="IconCheckCircle";var O=b;function w(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,n)}return t}function j(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?w(Object(t),!0).forEach((function(r){a(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):w(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function x(e,a){var s=r.exports.useContext(t).prefixCls,c=void 0===s?"arco":s,o=e.spin,l=e.className,i=j(j({"aria-hidden":!0,focusable:!1,ref:a},e),{},{className:"".concat(l?l+" ":"").concat(c,"-icon ").concat(c,"-icon-close-circle")});return o&&(i.className="".concat(i.className," ").concat(c,"-icon-loading")),delete i.spin,delete i.isIcon,n("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...i,children:n("path",{d:"m17.643 17.643 6.364 6.364m0 0 6.364 6.364m-6.364-6.364 6.364-6.364m-6.364 6.364-6.364 6.364M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6s18 8.059 18 18Z"})})}var _=e.forwardRef(x);_.defaultProps={isIcon:!0},_.displayName="IconCloseCircle";var C=_;var P="_container_h0myz_1",N="_progressCard_h0myz_1",I="_statusHeader_h0myz_9",S="_timeInfo_h0myz_15",k="_detailsCard_h0myz_19",D="_detailItem_h0myz_26";const{Text:z,Title:E}=d,T=({visible:e,taskId:t,title:a="任务进度",onComplete:d,onCancel:y,autoClose:v=!0,showDetails:g=!0})=>{const[b,w]=r.exports.useState(null),[j,x]=r.exports.useState(!1),[_,T]=r.exports.useState(null),B=r.exports.useRef(null),[M,R]=r.exports.useState(null),[F,$]=r.exports.useState(0),W=async()=>{if(t)try{x(!0),T(null);const e=await p(`/api/v1/tasks/${t}`,{method:"get"});if(0!==e.data.code)throw new Error(e.data.message||"获取任务信息失败");{const r=e.data.data;w(r),"finished"===r.state?(B.current&&(clearInterval(B.current),B.current=null),d&&d(!0,"任务完成"),v&&setTimeout((()=>{y&&y()}),2e3)):"failed"===r.state&&(B.current&&(clearInterval(B.current),B.current=null),T(r.error_log||"任务执行失败"),d&&d(!1,r.error_log||"任务执行失败"))}}catch(e){console.error("获取任务信息失败:",e),T(e.message||"获取任务信息失败"),B.current&&(clearInterval(B.current),B.current=null)}finally{x(!1)}},Z=()=>{B.current&&(clearInterval(B.current),B.current=null)};r.exports.useEffect((()=>{if(M&&b&&"running"===b.state){const e=setInterval((()=>{$(Math.floor((Date.now()-M.getTime())/1e3))}),1e3);return()=>clearInterval(e)}}),[M,b]),r.exports.useEffect((()=>(e&&t?t&&(R(new Date),W(),B.current=setInterval((()=>{W()}),1e3)):(Z(),w(null),T(null),R(null),$(0)),()=>{Z()})),[e,t]);const A=()=>b?b.extract_progress?parseFloat(b.extract_progress):parseFloat(b.progress||"0"):0;return n(s,{title:a,visible:e,onCancel:y,footer:"finished"===(null==b?void 0:b.state)||"failed"===(null==b?void 0:b.state)?n(c,{type:"primary",onClick:y,children:"关闭"}):n(c,{onClick:y,children:"取消"}),width:500,maskClosable:!1,children:o("div",{className:P,children:[_&&n(l,{type:"error",message:"错误",description:_,style:{marginBottom:16}}),o(f,{className:N,children:[o("div",{className:I,children:[o(i,{children:[(()=>{if(!b)return n(u,{});switch(b.state){case"finished":return n(O,{style:{color:"var(--color-success-6)"}});case"failed":return n(C,{style:{color:"var(--color-danger-6)"}});case"running":return n(u,{style:{color:"var(--color-primary-6)"}});default:return n(m,{})}})(),n(z,{strong:!0,children:(()=>{if(!b)return"准备中...";switch(b.state){case"finished":return"任务完成";case"failed":return"任务失败";case"running":return b.extract_progress?"正在解压...":"正在处理...";default:return"准备中..."}})()})]}),b&&o(z,{type:"secondary",children:[A().toFixed(1),"%"]})]}),n(h,{percent:A(),status:b?"finished"===b.state?"success":"failed"===b.state?"error":"normal":"normal",showText:!1,style:{marginTop:12}}),M&&"running"===(null==b?void 0:b.state)&&n("div",{className:S,children:o(z,{type:"secondary",style:{fontSize:12},children:["已用时间: ",(e=>{const r=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`})(F)]})})]}),g&&b&&o(f,{className:k,children:[n(E,{heading:6,children:"任务详情"}),o("div",{className:D,children:[n(z,{type:"secondary",children:"任务ID:"}),n(z,{children:b.id})]}),o("div",{className:D,children:[n(z,{type:"secondary",children:"任务类型:"}),n(z,{children:b.name})]}),o("div",{className:D,children:[n(z,{type:"secondary",children:"创建时间:"}),n(z,{children:b.created_at})]}),b.content&&o("div",{className:D,children:[n(z,{type:"secondary",children:"文件路径:"}),n(z,{style:{wordBreak:"break-all",fontSize:12},children:b.content})]})]})]})})};export{T as P};
