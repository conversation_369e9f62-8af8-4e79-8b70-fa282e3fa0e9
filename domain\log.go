package domain

import (
	"fmt"
	"time"
)

type Log struct {
	Id        int64     `json:"id" gorm:"primary_key"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`

	Tag string `json:"-" gorm:"-"`
}

var LogLevel = struct {
	Info  string
	Warn  string
	Error string
	Debug string
}{
	Info:  "INFO",
	Warn:  "WARN",
	Error: "ERROR",
	Debug: "DEBUG",
}

func SaveLog(level string, time time.Time, message string) {
	t := &Log{
		Level:     level,
		Message:   message,
		CreatedAt: time,
	}
	err := t.Add()
	if err != nil {
		fmt.Printf("%s %s: Failed to save log: %s\n", time.Format("2006-01-02 15:04:05"), level, err.Error())
	}
}
