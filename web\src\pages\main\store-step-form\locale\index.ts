const i18n = {
  'en-US': {
    'menu.form': 'Form',
    'menu.form.step': 'Step Form',
    'stepForm.title': 'Create a channel form',
    'stepForm.next': 'Next',
    'stepForm.prev': 'Prev',
    'stepForm.title.basicInfo': 'Basic Information',
    'stepForm.desc.basicInfo': 'Create an package',
    'stepForm.title.attachment': 'Attachment Information',
    'stepForm.desc.attachment': 'Upload attachment',
    'stepForm.title.channel': 'Channel Information',
    'stepForm.desc.channel': 'Enter detailed channel content',
    'stepForm.title.created': 'Complete creation',
    'stepForm.desc.created': 'Created successfully',
    'stepForm.basicInfo.name': 'Package name',
    'stepForm.basicInfo.name.required': 'Please enter the package name',
    'stepForm.basicInfo.name.placeholder':
      'Enter letters, numbers, -, _, such as: ais-mes (up to 20 characters)',
    'stepForm.basicInfo.version': 'Package version',
    'stepForm.basicInfo.version.required': 'Please enter the package version',
    'stepForm.basicInfo.version.placeholder': 'Enter the package version, such as: 1.0.0 (up to 20 characters)',
    'stepForm.basicInfo.type': 'Package type',
    'stepForm.basicInfo.type.required': 'Please select the package type',
    'stepForm.basicInfo.type.placeholder': 'Please select the package type',
    'stepForm.basicInfo.types.sdk': 'SDK',
    'stepForm.basicInfo.types.svc': 'Service',
    'stepForm.basicInfo.types.file': 'File',
    'stepForm.basicInfo.description': 'Package description',
    'stepForm.basicInfo.description.required': 'Please enter the package description',
    'stepForm.basicInfo.description.placeholder':
      'Please enter the package description (up to 200 words)',
    'stepForm.basicInfo.upgradable': 'Auto upgrade',
    'stepForm.attachment.label': 'Attachment',
    'stepForm.attachment.required': 'Please upload the attachment',
    'stepForm.attachment.support.tips': 'Only zip/jar/war can be uploaded',
    'stepForm.attachment.support-error.tips':
      'Unacceptable file type, please re-upload the specified file type!',
    'stepForm.publish.confirm.title': 'Publish confirmation',
    'stepForm.publish.confirm.content':
      'The published version file is too different from the current version file name or size, do you want to continue publishing?',
    'stepForm.created.fail': 'Failed to create',
    'stepForm.created.fail.already-exists': 'Package already exists',
    'stepForm.created.fail.already-published': 'This version of the package has been published',
    'stepForm.created.success': 'Created successfully',
    'stepForm.created.success.title': 'Created successfully',
    'stepForm.created.success.desc': 'Package created successfully',
    'stepForm.created.success.again': 'Create again',
    'stepForm.operations.back': 'Back',
  },
  'zh-CN': {
    'menu.form': '表单页',
    'menu.form.step': '分布表单',
    'stepForm.title': '创建渠道表单',
    'stepForm.next': '下一步',
    'stepForm.prev': '上一步',
    'stepForm.title.basicInfo': '基本信息',
    'stepForm.desc.basicInfo': '创建包',
    'stepForm.title.attachment': '附件信息',
    'stepForm.desc.attachment': '上传附件',
    'stepForm.title.created': '完成创建',
    'stepForm.desc.created': '创建成功',
    'stepForm.basicInfo.name': '包名称',
    'stepForm.basicInfo.name.required': '请输入包名称',
    'stepForm.basicInfo.name.placeholder': '输入字母、数字、-、_，如：ais-mes（最多20字符）',
    'stepForm.basicInfo.version': '包版本',
    'stepForm.basicInfo.version.required': '请输入包版本',
    'stepForm.basicInfo.version.placeholder': '输入包版本，如：1.0.0（最多20字符）',
    'stepForm.basicInfo.type': '包类型',
    'stepForm.basicInfo.type.required': '请选择包类型',
    'stepForm.basicInfo.type.placeholder': '请选择包类型',
    'stepForm.basicInfo.types.sdk': 'SDK',
    'stepForm.basicInfo.types.svc': '服务',
    'stepForm.basicInfo.types.file': '文件',
    'stepForm.basicInfo.description': '包描述',
    'stepForm.basicInfo.description.required': '请输入包描述',
    'stepForm.basicInfo.description.placeholder': '请输入包描述（最多200字）',
    'stepForm.basicInfo.upgradable': '自动升级',
    'stepForm.attachment.label': '附件',
    'stepForm.attachment.required': '请上传附件',
    'stepForm.attachment.support.tips': '仅支持zip/jar/war上传',
    'stepForm.attachment.support-error.tips': '不接受的文件类型，请重新上传指定文件类型！',
    'stepForm.publish.confirm.title': '发布确认',
    'stepForm.publish.confirm.content': '发布版本文件与当前版本文件名或大小差距过大，是否继续发布？',
    'stepForm.created.fail': '创建失败',
    'stepForm.created.fail.already-exists': '包已存在',
    'stepForm.created.fail.already-published': '此版本包已有过发布',
    'stepForm.created.success': '创建成功',
    'stepForm.created.success.title': '创建成功',
    'stepForm.created.success.desc': '包创建成功',
    'stepForm.created.success.again': '再次创建',
    'stepForm.operations.back': '返回',
  },
};

export default i18n;
