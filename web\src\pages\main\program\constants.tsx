import React from 'react';
import { Tag, Button, Typography, Space } from '@arco-design/web-react';

const { Text } = Typography;

const isRunning = (record) => {
  return record.state === 'running';
};

const canStart = (record) => {
  return record.bootable && !isRunning(record);
};

const canStop = (record) => {
  return record.bootable && isRunning(record);
};

const canDelete = (record) => {
  return !isRunning(record);
};

export function getColumns(
  t: any,
  callback: (record: Record<string, any>, type: string) => Promise<void>,
) {
  return [
    {
      title: t['program.columns.id'],
      dataIndex: 'id',
    },
    {
      title: t['program.columns.name'],
      dataIndex: 'name',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['program.columns.installPath'],
      dataIndex: 'installPath',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['program.columns.state'],
      dataIndex: 'state',
      render: (_, record) => {
        switch (record.state) {
          case 'running':
            return (
              <Tag color="green" size="small">
                {t[`program.columns.state.running`]}
              </Tag>
            );
          case 'exited':
            return (
              <Tag color="red" size="small">
                {t[`program.columns.state.exited`]}
              </Tag>
            );
          default:
            return record.state ? (
              <Tag color="gray" size="small">
                {t[`program.columns.state.${record.state}`] || record.state}
              </Tag>
            ) : null;
        }
      },
    },
    {
      title: t['program.columns.operation'],
      dataIndex: 'operation',
      headerCellStyle: { paddingLeft: '15px' },
      render: (_, record) => (
        <Space>
          {canStart(record) ? (
            <Button
              type="text"
              size="small"
              onClick={() => callback(record, 'start')}
            >
              {t['program.columns.operation.start']}
            </Button>
          ) : canStop(record) ? (
            <Button
              type="text"
              size="small"
              onClick={() => callback(record, 'stop')}
            >
              {t['program.columns.operation.stop']}
            </Button>
          ) : null}
          {canDelete(record) ? (
            <Button
              type="text"
              size="small"
              onClick={() => callback(record, 'delete')}
            >
              {t['program.columns.operation.delete']}
            </Button>
          ) : null}
        </Space>
      ),
    },
  ];
}
