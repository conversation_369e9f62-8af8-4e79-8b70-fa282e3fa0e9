import{u as a,b as e,j as o,s,T as t}from"./index.bbeb3af6.js";import{l,D as r}from"./index.6af89ef4.js";import{C as i}from"./index.cf9faf12.js";function n(n){const c=a(l),{data:u}=n,f=[{label:c["os.cpu.cores"],value:u.cpu.cores},{label:c["os.cpu.usage"],value:u.cpu.usage}];return e(i,{children:[o(s,{align:"start",children:o(t.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:c["os.cpu.title"]})}),o(r,{colon:": ",layout:"horizontal",data:f,column:2})]})}export{n as default};
