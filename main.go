package main

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"

	"github.com/urfave/cli/v3"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/domain"
)

func main() {
	// Initialize the database
	err := domain.InitializeDB()
	if err != nil {
		log.Fatalln("Failed to initialize database: " + err.Error())
	}
	slog.SetDefault(slog.New(domain.NewLogHandler(
		os.Stdout,
		&slog.HandlerOptions{
			AddSource: true,
			Level:     domain.GetLogLevel(),
		},
	)))
	name := config.Name()
	version := config.Version()
	service, err := domain.NewSystemService()
	if err != nil {
		log.Fatalln("Failed to create service: " + err.Error())
	}
	cmd := &cli.Command{
		Name:   name,
		Usage:  fmt.Sprintf("%s v%s", name, version),
		Action: service.Default,
		Commands: []*cli.Command{
			{
				Name:   "config",
				Usage:  "Show the configuration",
				Action: service.Control,
			},
			{
				Name:   "install",
				Usage:  "Write the files required for startup",
				Action: service.Control,
			},
			{
				Name:   "uninstall",
				Usage:  "Remove startup files",
				Action: service.Control,
			},
			{
				Name:   "start",
				Usage:  "Start the service",
				Action: service.Control,
			},
			{
				Name:   "stop",
				Usage:  "Stop the service",
				Action: service.Control,
			},
			{
				Name:   "health",
				Usage:  "Check the service",
				Action: service.Control,
			},
		},
	}

	if err := cmd.Run(context.Background(), os.Args); err != nil {
		log.Fatalln(err)
	}
}
