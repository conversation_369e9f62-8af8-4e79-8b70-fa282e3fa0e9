import{u as e,r as a,b as t,j as s,s as n,B as o,l as r,i}from"./index.bbeb3af6.js";import{C as c}from"./index.cf9faf12.js";import{T as p}from"./index.b90074ae.js";import{i as d,s as u}from"./index.module.cf3602d3.js";import{getColumns as l}from"./constants.73f53a4f.js";import{I as f}from"./index.9dee27c6.js";function g(){const g=e(d),m=a.exports.useMemo((()=>l(g)),[g]),[h,x]=a.exports.useState([]),[S,z]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[b,j]=a.exports.useState(!0);function C(){const{current:e,pageSize:a}=S;j(!0),r("/api/v1/logs",{data:{page:e,size:a}}).then((t=>{const s=t&&t.data;(s||s.data||s.data.content)&&(x(s.data.content),z({...S,current:e,pageSize:a,total:s.data.total}))})).finally((()=>{j(!1)}))}return a.exports.useEffect((()=>{C()}),[S.current,S.pageSize]),t(c,{children:[s("div",{className:u["button-group"],children:s(n,{children:s(o,{type:"primary",status:"danger",icon:s(f,{}),onClick:function(){r("/api/v1/logs",{method:"delete"}).then((()=>{i.success(g["searchTable.operations.clear.success"]),C()}))},children:g["searchTable.operations.clear"]})})}),s(p,{rowKey:"id",loading:b,onChange:function({current:e,pageSize:a}){z({...S,current:e,pageSize:a})},pagination:S,columns:m,data:h})]})}export{g as default};
