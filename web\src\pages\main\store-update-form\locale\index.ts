const i18n = {
  'en-US': {
    'form.name.label': 'Name',
    'form.name.required': 'Please enter the name',
    'form.name.placeholder': 'Please enter the name',
    'form.attachment.label': 'Attachment',
    'form.attachment.required': 'Please upload the attachment',
    'form.attachment.support.tips': 'Only zip can be uploaded',
    'form.attachment.support-error.tips':
      'Unacceptable file type, please re-upload the specified file type!',
    'form.patch.label': 'Download from server',
    'form.patch.placeholder': 'Please select the corresponding resource file',
    'form.confirm.title': 'Update confirmation',
    'form.confirm.content':
      'Do you want to confirm the update?',
    'form.updated.fail': 'Update failed',
    'form.updated.success': 'Update success',
    'form.operations.back': 'Back',
    'form.operations.submit': 'Submit',
    'form.validation.bothEmpty.error': 'Selected resource and attachment cannot be empty at the same time',
  },
  'zh-CN': {
    'form.name.label': '名称',
    'form.name.required': '请输入名称',
    'form.name.placeholder': '请输入名称',
    'form.attachment.label': '附件',
    'form.attachment.required': '请上传附件',
    'form.attachment.support.tips': '仅支持zip上传',
    'form.attachment.support-error.tips': '不接受的文件类型，请重新上传指定文件类型！',
    'form.patch.label': '从服务器下载',
    'form.patch.placeholder': '请选择相应的资源文件',
    'form.confirm.title': '更新确认',
    'form.confirm.content': '是否确认更新？',
    'form.updated.fail': '更新失败',
    'form.updated.success': '更新成功',
    'form.operations.back': '返回',
    'form.operations.submit': '提交',
    'form.validation.bothEmpty.error': '选中资源和附件不能同时为空',
  },
};

export default i18n;
