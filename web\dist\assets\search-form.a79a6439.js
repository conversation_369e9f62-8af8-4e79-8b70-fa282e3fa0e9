import{u as e,b as a,j as t,af as l,Q as o,B as s,ag as c,G as r}from"./index.e8bac691.js";import{I as n}from"./index.dde5f5a0.js";const i={"en-US":{"menu.main":"Main","menu.main.store":"Store","menu.list":"List","menu.list.searchTable":"Search Table","patch.const.answer.yes":"Yes","patch.const.answer.no":"No","patch.columns.id":"ID","patch.columns.name":"Name","patch.columns.version":"Version","patch.columns.uri":"URI","patch.columns.attribution":"Attribution","patch.columns.operation":"Operation","patch.columns.operation.delete":"Delete","patch.tables.main.form.search":"Search","patch.tables.main.form.reset":"Reset","patch.tables.main.operation.add":"New","patch.tables.main.operation.import":"Import","patch.tables.main.operation.upload":"Upload","patch.tables.main.operation.delete.success":"Delete successfully","patch.tables.main.operation.no-support":"No support","patch.tables.main.modal.delete.title":"Delete confirmation","patch.tables.main.modal.delete.content":"Are you sure you want to delete this record?","patch.tables.main.modal.delete.operation.success":"Delete successfully","patch.tables.main.modal.delete.operation.fail":"Delete failed","patch.tables.main.modal.update.title":"Update confirmation","patch.tables.main.modal.update.content":"Are you sure you want to update?","patch.tables.main.modal.update.operation.success":"Update successfully","patch.tables.main.modal.update.operation.fail":"Update failed","patch.forms.search.id.placeholder":"Please enter the ID","patch.forms.search.name.placeholder":"Please enter the name","patch.forms.search.uri.placeholder":"Please enter the uri","patch.forms.search.attribution.placeholder":"Please enter the attribution","patch.forms.search.all.placeholder":"all"},"zh-CN":{"menu.main":"主页","menu.main.store":"应用仓库","menu.list":"列表页","menu.list.searchTable":"查询表格","patch.const.answer.yes":"是","patch.const.answer.no":"否","patch.columns.id":"ID","patch.columns.name":"名称","patch.columns.uri":"URI","patch.columns.attribution":"归属","patch.columns.operation":"操作","patch.columns.operation.delete":"删除","patch.tables.main.form.search":"查询","patch.tables.main.form.reset":"重置","patch.tables.main.operation.delete.success":"删除成功","patch.tables.main.operation.no-support":"暂不支持","patch.tables.main.modal.delete.title":"删除确认","patch.tables.main.modal.delete.content":"确定删除该条记录吗？","patch.tables.main.modal.delete.operation.success":"删除成功","patch.tables.main.modal.delete.operation.fail":"删除失败","patch.forms.search.id.placeholder":"请输入ID","patch.forms.search.name.placeholder":"请输入名称","patch.forms.search.uri.placeholder":"请输入URI","patch.forms.search.attribution.placeholder":"请输入归属","patch.forms.search.all.placeholder":"全部"}};var p="_search-form-wrapper_pbsw0_16",h="_right-button_pbsw0_21",m="_search-form_pbsw0_16";const{Row:d,Col:u}=r,{useForm:b}=l;function f(r){const f=e(i),{loading:w}=r,[y]=b();return a("div",{className:p,children:[t(l,{form:y,className:m,labelAlign:"left",labelCol:{span:4},wrapperCol:{span:20},children:a(d,{gutter:24,children:[t(u,{span:12,children:t(l.Item,{label:f["patch.columns.attribution"],field:"packageName",children:t(o,{placeholder:f["patch.forms.search.attribution.placeholder"],allowClear:!0})})}),t(u,{span:12,children:t(l.Item,{label:f["patch.columns.name"],field:"name",children:t(o,{allowClear:!0,placeholder:f["patch.forms.search.name.placeholder"]})})}),t(u,{span:12,children:t(l.Item,{label:f["patch.columns.uri"],field:"uri",children:t(o,{allowClear:!0,placeholder:f["patch.forms.search.uri.placeholder"]})})})]})}),a("div",{className:h,children:[t(s,{loading:w,type:"primary",icon:t(c,{}),onClick:()=>{const e=y.getFieldsValue();r.onSearch(e)},children:f["patch.tables.main.form.search"]}),t(s,{loading:w,icon:t(n,{}),onClick:()=>{y.resetFields(),r.onSearch({})},children:f["patch.tables.main.form.reset"]})]})]})}var w=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"}));export{f as S,i,w as s};
