import React, { useEffect } from 'react';
// import { HashRouter, Routes, Route } from 'react-router-dom';
import { HashRouter, Switch, Route } from 'react-router-dom';
import { createStore } from 'redux';
import { Provider } from 'react-redux';
import { ConfigProvider } from '@arco-design/web-react';
import zhCN from '@arco-design/web-react/es/locale/zh-CN';
import enUS from '@arco-design/web-react/es/locale/en-US';
import Mock from 'mockjs';

import { generatePermission } from '@/routes';

import { GlobalContext, MQTTProvider } from './context';
import PageLayout from './layout';
import './mock';
import RootReducer from './store';

import Login from './pages/login';

import checkLogin from './utils/checkLogin';
import changeTheme from './utils/changeTheme';
import useStorage from './utils/useStorage';

import './style/global.less';

const store = createStore(RootReducer);

function App() {
    // const rootRef = useRef(null);

    const [lang, setLang] = useStorage('arco-lang', 'zh-CN');
    const [theme, setTheme] = useStorage('arco-theme', 'light');

    function getArcoLocale() {
        switch (lang) {
        case 'zh-CN':
            return zhCN;
        case 'en-US':
            return enUS;
        default:
            return zhCN;
        }
    }

    function fetchUserInfoAndCheck() {
        store.dispatch({
            type: 'update-userInfo',
            payload: { userLoading: true },
        });
        const userRole = 'admin';
        // const userRole = window.localStorage.getItem('userRole') || 'user';
        // axios.get('/api/user/userInfo').then((res) => {
        //   store.dispatch({
        //     type: 'update-userInfo',
        //     payload: { userInfo: res.data, userLoading: false },
        //   });
        // });
        const data = Mock.mock({
            name: Mock.Random.cname(),
            avatar: './images/avatar.jpg',
            email: /[a-z]{4}\@jhong\.info/,
            job: '管理员',
            jobName: '系统管理员',
            organization: 'jhong',
            organizationName: '长沙京鸿',
            location: 'changsha',
            locationName: '长沙',
            introduction: '无',
            personalWebsite: 'https://www.arco.design',
            verified: true,
            phoneNumber: /132[*]{6}[0-9]{2}/,
            accountId: /[a-z]{4}[-][0-9]{8}/,
            registrationTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
            permissions: generatePermission(userRole),
        });
        store.dispatch({
            type: 'update-userInfo',
            payload: { userInfo: data, userLoading: false },
        });
    }

    useEffect(() => {
        if (checkLogin()) {
        fetchUserInfoAndCheck();
        } else {
        window.location.href = '#/login';
        }
    }, []);

    useEffect(() => {
        changeTheme(theme);
    }, [theme]);

    const contextValue = {
        lang,
        setLang,
        theme,
        setTheme,
    };

    return (
        <HashRouter>
        <ConfigProvider
            locale={getArcoLocale()}
            componentConfig={{
            Card: {
                bordered: false,
            },
            List: {
                bordered: false,
            },
            Table: {
                border: false,
            },
            }}
        >
            <Provider store={store}>
            <GlobalContext.Provider value={contextValue}>
                <MQTTProvider>
                {/* <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route path="/" element={<PageLayout />} />
                </Routes> */}
                    <Switch>
                        <Route path="/login" component={Login} />
                        <Route path="/" component={PageLayout} />
                    </Switch>
                </MQTTProvider>
            </GlobalContext.Provider>
            </Provider>
        </ConfigProvider>
        </HashRouter>
    );
}

export default App;