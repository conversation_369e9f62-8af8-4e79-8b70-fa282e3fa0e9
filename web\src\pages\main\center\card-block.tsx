import React, { useEffect, useState } from 'react';
import cs from 'classnames';
import {
  Button,
  Dropdown,
  Tag,
  Card,
  Typography,
  Modal,
  Message,
  Menu,
} from '@arco-design/web-react';
import {
  IconInfoCircleFill,
  IconMore,
  IconArrowRight
} from '@arco-design/web-react/icon';

import IconButton from '@/components/NavBar/IconButton';
import api from '@/utils/api';
import useLocale from '@/utils/useLocale';

import locale from './locale';
import { Client } from './interface';
import styles from './style/index.module.less';
import useStorage from '@/utils/useStorage';

interface CardBlockType {
  card: Client;
  loading?: boolean;
  control?: (action) => void;
}

const { Paragraph } = Typography;
function CardBlock(props: CardBlockType) {
  const { card = {} } = props;
  const control = props.control;
  const id = card.id;
  const [state, setState] = useState(card.state.toLowerCase());
  const [ networks ] = useState(card.networks);
  const [ uptime ] = useState(card.uptime);
  const [ version ] = useState(card.version);
  const [ ssl ] = useState(card.ssl);
  const [ gatewayPort ] = useState(card.gatewayPort);
  const [ networksDesc, setNetworksDesc] = useState("");
  const [deleted, setDeleted] = useState(false);
  const [loading, setLoading] = useState(props.loading);

  const t = useLocale(locale);

  const isLoacl = () => {
    return card.isLocal === 1;
  }

  const isOnline = () => {
    return state === 'online';
  }

  const handleOpen = () => {
    if (isLoacl()) {
      return;
    }
    const protocol = ssl === 1 ? 'https' : 'http';
    const url = `${protocol}://${networksDesc}:${gatewayPort}`;
    window.open(url, '_blank');
  }

  const handleDelete = () => {
    if (isLoacl()) {
      Message.warning(t[`center.cardBlock.delete.operation.disabled`]);
      return;
    }
    Modal.confirm({
      title: t[`center.cardBlock.delete.title`],
      content: t[`center.cardBlock.delete.content`],
      onOk: () => {
        api(`/api/v1/clients/${card.id}`, {
          method: 'delete'
        }).then(() => {
          Message.success(t[`center.cardBlock.delete.operation.success`]);
          setDeleted(true);
        }).catch(() => {
          Message.error(t[`center.cardBlock.delete.operation.fail`]);
        });
      }
    });
  }

  const handleSync = async () => {
    if (state === 'offline') {
      Message.warning(t[`center.cardBlock.sync.operation.offline`]);
      return;
    }
    control && control({
      to: card.id,
      type: 'sync'
    });
  }

  useEffect(() => {
    setLoading(props.loading);
    if (networks)
    {
      const networksObj = JSON.parse(networks);
      const networksArr = [];
      for (const network of networksObj)
      { 
        networksArr.push(network.address);
      }
      setNetworksDesc(networksArr.join(","));
    }
  }, [props.loading]);

  const getState = () => {
    switch (state) {
      case 'online':
        return (
          <Tag
            color="green"
            icon={<IconInfoCircleFill />}
            className={styles.status}
            size="small"
          >
            {t[`center.cardBlock.state.${state}`]}
          </Tag>
        );
      default:
        return (
          <Tag
            color="gray"
            icon={<IconInfoCircleFill />}
            className={styles.status}
            size="small"
          >
            {t[`center.cardBlock.state.${state}`]}
          </Tag>
        )
    }
  };

  const menus = (
    <Menu>
      <Menu.Item key="sync">
        <Button type="text" onClick={handleSync}>{ t['center.cardBlock.head.extra.sync'] }</Button>
      </Menu.Item>
      <Menu.Item key="delete">
        <Button type="text" onClick={handleDelete}>{ t['center.cardBlock.head.extra.delete'] }</Button>
      </Menu.Item>
    </Menu>
  );

  return (
      <Card
        hidden={deleted}
        bordered={true}
        size="small"
        style={{ marginBottom: '10px', minHeight: '150px', width: '100%' }}
        title={
          <div
              className={cs(styles.title)}
            >
            {getState()}
            {/* {!card.name || card.name === '' ? card.id : card.name} */}
            <span style={{ color: 'gray', fontSize: 10, marginLeft: '5px' }}>v{version}</span>
          </div>
        }
        extra={
          <Dropdown droplist={menus} trigger='click' position='br'>
            <IconButton
              loading={loading}
              icon={<IconMore />}
              size="small"
            />
          </Dropdown>
        }
        actions={[
          <IconButton
            key={"more_"+id}
            type='outline'
            loading={loading}
            icon={<IconArrowRight />}
            size="small"
            disabled={isLoacl() || !isOnline()}
            onClick={handleOpen}
          />
        ]}
      >
        <div 
          className={styles.content}
        >
          <Paragraph>{ t['center.cardBlock.content.name'] + ': ' + (!card.name || card.name === '' ? card.id : card.name) }</Paragraph>
          <Paragraph>{ t['center.cardBlock.content.network'] + ': ' + networksDesc }</Paragraph>
          <Paragraph>{ t['center.cardBlock.content.uptime'] + ': ' + uptime }</Paragraph>
        </div>
      </Card>
    );
}

export default CardBlock;
