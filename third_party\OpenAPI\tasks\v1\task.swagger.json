{"swagger": "2.0", "info": {"title": "tasks/v1/task.proto", "version": "1.0"}, "tags": [{"name": "TaskService"}], "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/browse-files": {"get": {"summary": "Browse local files", "description": "Browse local file system to select files", "operationId": "TaskService_BrowseLocalFiles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1BrowseFilesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "path", "in": "query", "required": false, "type": "string"}], "tags": ["Files"]}}, "/api/v1/local-import-tasks": {"post": {"summary": "Create a local import task", "description": "Create a local import task to import local files directly", "operationId": "TaskService_CreateTaskForLocalImport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1LocalImportRequest"}}], "tags": ["Tasks"]}}, "/api/v1/system-drives": {"get": {"summary": "Get system drives", "description": "Get available system drives/volumes", "operationId": "TaskService_GetSystemDrives", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetDrivesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["Files"]}}, "/api/v1/tasks": {"post": {"summary": "Execute a task", "description": "Execute a task on the server", "operationId": "TaskService_ExecuteTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1Task"}}], "tags": ["Tasks"]}}, "/api/v1/tasks/{id}": {"get": {"summary": "Get a task", "description": "Get a task from the server", "operationId": "TaskService_GetTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "integer", "format": "int32"}], "tags": ["Tasks"]}}, "/api/v1/upload-package-tasks": {"post": {"summary": "Create a upload package task", "description": "Create a upload package task on the server", "operationId": "TaskService_CreateTaskForUploadPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1UploadRequest"}}], "tags": ["Tasks"]}}, "/api/v1/upload-package-tasks/{id}/chunks": {"post": {"summary": "Execute a upload package task", "description": "Execute a upload package task on the server", "operationId": "TaskService_ExecuteTaskForUploadPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"packageName": {"type": "string"}, "packageVersion": {"type": "string"}, "chunkIndex": {"type": "integer", "format": "int32"}, "chunkSize": {"type": "integer", "format": "int32"}, "chunkContent": {"type": "string", "format": "byte"}, "totalChunks": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int32"}}}}], "tags": ["Tasks"]}}, "/api/v1/upload-tasks": {"post": {"summary": "Create a upload task", "description": "Create a upload task on the server", "operationId": "TaskService_CreateTaskForUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1UploadRequest"}}], "tags": ["Tasks"]}}, "/api/v1/upload-tasks/{id}/chunks": {"post": {"summary": "Execute a upload task", "description": "Execute a upload task on the server", "operationId": "TaskService_ExecuteTaskForUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1TaskResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"packageName": {"type": "string"}, "packageVersion": {"type": "string"}, "chunkIndex": {"type": "integer", "format": "int32"}, "chunkSize": {"type": "integer", "format": "int32"}, "chunkContent": {"type": "string", "format": "byte"}, "totalChunks": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int32"}}}}], "tags": ["Tasks"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com. As of May 2023, there are no widely used type server\nimplementations and no plans to implement one.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n    // or ...\n    if (any.isSameTypeAs(Foo.getDefaultInstance())) {\n      foo = any.unpack(Foo.getDefaultInstance());\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1BrowseFilesResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "currentPath": {"type": "string"}, "files": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1FileInfo"}}}}, "v1DriveInfo": {"type": "object", "properties": {"letter": {"type": "string"}, "path": {"type": "string"}, "label": {"type": "string"}, "driveType": {"type": "string"}, "available": {"type": "boolean"}}}, "v1FileInfo": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}, "isDir": {"type": "boolean"}, "size": {"type": "string", "format": "int64"}, "modTime": {"type": "string"}, "readable": {"type": "boolean"}}}, "v1GetDrivesResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "drives": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1DriveInfo"}}}}, "v1LocalImportRequest": {"type": "object", "properties": {"packageName": {"type": "string"}, "packageVersion": {"type": "string"}, "localPath": {"type": "string"}}}, "v1Task": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "type": {"type": "string"}, "content": {"type": "string"}, "state": {"type": "string"}, "packageId": {"type": "integer", "format": "int32"}, "patchName": {"type": "string"}, "progress": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}}, "v1TaskResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1Task"}}}, "v1UploadRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "packageName": {"type": "string"}, "packageVersion": {"type": "string"}, "chunkIndex": {"type": "integer", "format": "int32"}, "chunkSize": {"type": "integer", "format": "int32"}, "chunkContent": {"type": "string", "format": "byte"}, "totalChunks": {"type": "integer", "format": "int32"}, "totalSize": {"type": "integer", "format": "int32"}}}}, "externalDocs": {"description": "AIS Server", "url": "http://gitlab.jhonginfo.com/product/ais-server"}}