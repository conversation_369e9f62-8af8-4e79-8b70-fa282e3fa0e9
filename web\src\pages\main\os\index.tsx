import { Empty, Space } from '@arco-design/web-react';
import React, { useEffect, useState } from 'react';

import api from '@/utils/api';

import styles from './style/index.module.less';
import OSStatusCPU from './os-status-cpu';
import OSStatusMemory from './os-status-memory';
import OSInformation from './os-information';
import OSNetwork from './os-network';

export default function OS() {
  const [ data, setData ] = useState({
    os: {
      type: '',
      version: '',
      arch: ''
    },
    cpu: {
      cores: '',
      usage: ''
    },
    memory: {
      total: '',
      free: '',
      used: '',
      usage: ''
    },
    disk: {
      total: '',
      free: '',
      used: '',
      usage: ''
    },
    times: {
      up: ''
    },
    networks: []
  });

  const fetchData = () => {
    api('/api/v1/sysinfo')
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data) {
          return;
        }
        const newNetworks = [];
        for (const [, value] of Object.entries(resData.data.networks)) {
          const isIPv4 = value['family'] === "IPv4";
          const hasMAC = value['mac'] != null && value['mac'] !== '';
          if (isIPv4 && hasMAC) {
            newNetworks.push(value);
          }
        }
        resData.data.networks = newNetworks;
        setData(resData.data);
      });
  };

  useEffect(() => {
    fetchData();
  }, []);
  
  return (
    <div>
      <div className={styles.layout}>
        <div className={styles['layout-content']}>
          <Space size={16} direction="vertical" style={{ width: '100%' }}>
            <OSInformation data={data} />
            <OSStatusCPU data={data} />
            <OSStatusMemory data={data} />
            {
              (data.networks != null && data.networks.length > 0)
                ? (<OSNetwork data={data} />)
                : null
            }
          </Space>
        </div>
      </div>
    </div>
  );
}
