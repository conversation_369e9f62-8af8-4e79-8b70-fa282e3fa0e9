import{r as e,C as s,a,ah as t,ai as n,y as o,a2 as r,x as l,c as i,b as c,j as u,F as m}from"./index.fe31dd41.js";var d=globalThis&&globalThis.__assign||function(){return d=Object.assign||function(e){for(var s,a=1,t=arguments.length;a<t;a++)for(var n in s=arguments[a])Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n]);return e},d.apply(this,arguments)},p=globalThis&&globalThis.__read||function(e,s){var a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;var t,n,o=a.call(e),r=[];try{for(;(void 0===s||s-- >0)&&!(t=o.next()).done;)r.push(t.value)}catch(l){n={error:l}}finally{try{t&&!t.done&&(a=o.return)&&a.call(o)}finally{if(n)throw n.error}}return r},f=function(e){return l(e)?e.reduce((function(e,s){return e+(s.span||1)}),0):0},y={layout:"horizontal",column:3,tableLayout:"auto"};function h(h){var v,b=e.exports.useContext(s),k=b.getPrefixCls,g=b.componentConfig,N=b.rtl,w=b.size,S=a(h,y,null==g?void 0:g.Descriptions),x=S.style,C=S.className,U=S.column,T=S.title,z=S.data,P=S.border,_=S.labelStyle,j=S.valueStyle,A=S.colon,D=S.layout,F=S.size,M=S.tableLayout,O=k("descriptions"),E=p(e.exports.useState(),2),L=E[0],I=E[1],R=e.exports.useRef(null);e.exports.useEffect((function(){return R.current=t.subscribe((function(e){for(var s=0;s<n.length;s++){var a=n[s];if(e[a]){I(a);break}}})),function(){t.unsubscribe(R.current)}}),[]);var q=3;o(U)&&(q=U[L]||3),r(U)&&U>0&&(q=U);var B=[];if(l(z)&&z.length>0&&q){z.forEach((function(e){var s=B[B.length-1],a=f(s);0===a||a===q?B.push([d(d({},e),{span:e.span?e.span>q?q:e.span:1})]):s.push(d(d({},e),{span:e.span?e.span+a>q?q-a:e.span:1}))}));var G=B[B.length-1],H=f(G);H<q&&(G[G.length-1].span=G[G.length-1].span+q-H)}function J(e,s){return"inline-vertical"===D||"inline-horizontal"===D?function(e,s){return u("tr",{className:O+"-row",children:e.map((function(e,s){var a=e.span>1?{colSpan:e.span}:{};return c("td",{...d({key:e.key||s},a,{className:O+"-item"}),children:[c("div",{className:O+"-item-label-inline",style:_,children:[e.label,A]}),u("div",{className:O+"-item-value-inline",style:j,children:e.value})]})}))},s)}(e,s):"vertical"===D?function(e,s){return c(m,{children:[u("tr",{className:O+"-row",children:e.map((function(e,s){var a=e.span>1?{colSpan:e.span}:{};return c("td",{...d({key:(e.key||s)+"_label",className:O+"-item-label"},a,{style:_}),children:[e.label,A]})}))}),u("tr",{className:O+"-row",children:e.map((function(e,s){var a=e.span>1?{colSpan:e.span}:{};return u("td",{...d({key:(e.key||s)+"_value",className:O+"-item-value"},a,{style:j}),children:e.value})}))})]})}(e):function(e,s){return u("tr",{className:O+"-row",children:e.map((function(e,s){var a=e.span>1?{colSpan:2*e.span-1}:{};return c(m,{children:[c("td",{className:O+"-item-label",style:_,children:[e.label,A]}),u("td",{...d({className:O+"-item-value"},a,{style:j}),children:e.value})]})}))},s)}(e,s)}var K=i(O,((v={})[O+"-border"]=P,v[O+"-layout-"+D]=D,v[O+"-size-"+(F||w)]=F||w,v[O+"-table-layout-fixed"]="fixed"===M,v[O+"-rtl"]=N,v),C);return c("div",{className:K,style:x,children:[T&&u("div",{className:O+"-title",children:T}),u("div",{className:O+"-body",children:u("table",{className:O+"-table",cellPadding:0,cellSpacing:0,children:u("tbody",{children:B.map((function(e,s){return J(e,s)}))})})})]})}h.displayName="Descriptions";var v={"en-US":{"menu.main":"Main","menu.main.os":"System Information","os.info.title":"System","os.info.type":"Type","os.info.version":"Platform","os.info.arch":"Architecture","os.cpu.title":"CPU","os.cpu.cores":"Cores","os.cpu.usage":"Usage","os.memory.title":"Memory","os.memory.total":"Total","os.memory.free":"Free","os.memory.used":"Used","os.memory.usage":"Usage","os.disk.title":"Disk","os.disk.total":"Total","os.disk.free":"Free","os.disk.used":"Used","os.disk.usage":"Usage","os.times.up":"Up Time","os.network.title":"Networks","os.network.name":"Name","os.network.address":"Address","os.network.family":"Family","os.network.mac":"MAC"},"zh-CN":{"menu.main":"主页","menu.main.os":"系统信息","os.info.title":"系统","os.info.type":"类型","os.info.version":"版本","os.info.arch":"架构","os.cpu.title":"CPU","os.cpu.cores":"核心数","os.cpu.usage":"使用率","os.memory.title":"内存","os.memory.total":"总内存","os.memory.free":"空闲内存","os.memory.used":"已用内存","os.memory.usage":"使用率","os.disk.title":"磁盘","os.disk.total":"总磁盘","os.disk.free":"空闲磁盘","os.disk.used":"已用磁盘","os.disk.usage":"使用率","os.times.up":"运行时间","os.network.title":"网络","os.network.name":"名称","os.network.address":"地址","os.network.family":"协议","os.network.mac":"MAC"}};export{h as D,v as l};
