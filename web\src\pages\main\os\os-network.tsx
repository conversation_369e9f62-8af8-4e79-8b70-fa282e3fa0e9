import React from 'react';
import {
  Card,
  Typography,
  Space,
  Descriptions,
} from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import locale from './locale';

export default function OSNetwork(props) {
  const t = useLocale(locale);
  const { data } = props;
  const dataNetworkStatus = [];

  if (data.networks) {
    for (const [, value] of Object.entries(data.networks)) {
      dataNetworkStatus.push({
        label: t['os.network.name'],
        value: value['name'],
      });
      dataNetworkStatus.push({
        label: t['os.network.family'],
        value: value['family'],
      });
      dataNetworkStatus.push({
        label: t['os.network.address'],
        value: value['address'],
      });
      dataNetworkStatus.push({
        label: t['os.network.mac'],
        value: value['mac'],
      });
    }
  }

  return (
    <Card>
      <Space align="start">
        <Typography.Title
          style={{ marginTop: 0, marginBottom: 16 }}
          heading={6}
        >
          {t['os.network.title']}
        </Typography.Title>
      </Space>
      <Descriptions
        colon=": "
        layout="horizontal"
        data={dataNetworkStatus}
        column={4}
      />
    </Card>
  );
}
