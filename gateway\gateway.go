package gateway

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"io/fs"
	"log/slog"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/insecure"
	clientsv1 "gitlab.jhonginfo.com/product/ais-server/proto/clients/v1"
	configsv1 "gitlab.jhonginfo.com/product/ais-server/proto/configs/v1"
	logsv1 "gitlab.jhonginfo.com/product/ais-server/proto/logs/v1"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
	patchesv1 "gitlab.jhonginfo.com/product/ais-server/proto/patches/v1"
	programsv1 "gitlab.jhonginfo.com/product/ais-server/proto/programs/v1"
	sysinfosv1 "gitlab.jhonginfo.com/product/ais-server/proto/sysinfos/v1"
	tasksv1 "gitlab.jhonginfo.com/product/ais-server/proto/tasks/v1"
	"gitlab.jhonginfo.com/product/ais-server/web"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/grpclog"
)

// getOpenAPIHandler serves an OpenAPI UI.
// Adapted from https://github.com/philips/grpc-gateway-example/blob/a269bcb5931ca92be0ceae6130ac27ae89582ecc/cmd/serve.go#L63
// func getOpenAPIHandler() http.Handler {
// 	mime.AddExtensionType(".svg", "image/svg+xml")
// 	// Use subdirectory in embedded files
// 	subFS, err := fs.Sub(third_party.OpenAPI, "OpenAPI")
// 	if err != nil {
// 		panic("couldn't create sub filesystem: " + err.Error())
// 	}
// 	return http.FileServer(http.FS(subFS))
// }

func AdminHandler() http.Handler {
	mime.AddExtensionType(".svg", "image/svg+xml")
	c := config.Get()
	storageDir := c.StorageDir
	webDir := filepath.Join(storageDir, "dist")
	if _, err := os.Stat(webDir); os.IsNotExist(err) {
		// os.MkdirAll(webDir, os.ModePerm)
		// Use subdirectory in embedded files
		subFS, err := fs.Sub(web.Dist, "dist")
		if err != nil {
			panic("couldn't create sub filesystem: " + err.Error())
		}
		return http.FileServer(http.FS(subFS))
	}
	return http.FileServer(http.Dir(webDir))
}

// Run runs the gRPC-Gateway, dialling the provided address.
func Run(dialAddr string) error {
	// Adds gRPC internal logs. This is quite verbose, so adjust as desired!
	log := grpclog.NewLoggerV2(os.Stdout, io.Discard, io.Discard)
	grpclog.SetLoggerV2(log)

	// Create a client connection to the gRPC Server we just started.
	// This is where the gRPC-Gateway proxies the requests.
	conn, err := grpc.DialContext(
		context.Background(),
		dialAddr,
		grpc.WithTransportCredentials(credentials.NewClientTLSFromCert(insecure.CertPool, "")),
		grpc.WithBlock(),
	)
	if err != nil {
		return fmt.Errorf("failed to dial server: %w", err)
	}

	gwmux := runtime.NewServeMux()

	// Register the gRPC-Gateway handler endpoint
	err = clientsv1.RegisterClientServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = configsv1.RegisterConfigServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = logsv1.RegisterLogServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = packagesv1.RegisterPackageServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = patchesv1.RegisterPatchServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = programsv1.RegisterPackageServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = sysinfosv1.RegisterSysinfoServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}
	err = tasksv1.RegisterTaskServiceHandler(context.Background(), gwmux, conn)
	if err != nil {
		return fmt.Errorf("failed to register gateway: %w", err)
	}

	// oa := getOpenAPIHandler()
	admin := AdminHandler()

	gatewayAddr := config.GetGatewayAddress()
	gwServer := &http.Server{
		Addr: gatewayAddr,
		Handler: http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if strings.HasPrefix(r.URL.Path, "/api") {
				origins := r.Header.Values("Origin")
				if len(origins) > 0 {
					w.Header().Set("Access-Control-Allow-Origin", origins[0])
				} else {
					w.Header().Set("Access-Control-Allow-Origin", "*")
				}
				keys := r.Header.Values("Access-Control-Request-Headers")
				if len(keys) > 0 {
					w.Header().Set("Access-Control-Allow-Headers", strings.Join(keys, ","))
				} else {
					w.Header().Set("Access-Control-Allow-Headers", "*")
				}
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, HEAD, UPDATE, OPTIONS")
				w.Header().Set("Access-Control-Allow-Private-Network", "true")
				w.Header().Set("Access-Control-Allow-Credentials", "true")
				if r.Method == "OPTIONS" {
					w.Header().Set("Access-Control-Max-Age", "3600")
					w.WriteHeader(http.StatusNoContent)
					return
				}
				gwmux.ServeHTTP(w, r)
				return
			}
			admin.ServeHTTP(w, r)
		}),
	}

	// Empty parameters mean use the TLS Config specified with the server.
	if !config.IsSSL() {
		slog.Info("Serving gRPC-Gateway on http://" + gatewayAddr)
		return fmt.Errorf("serving gRPC-Gateway server: %w", gwServer.ListenAndServe())
	}

	gwServer.TLSConfig = &tls.Config{
		Certificates: []tls.Certificate{insecure.Cert},
	}
	slog.Info("Serving gRPC-Gateway on https://" + gatewayAddr)
	return fmt.Errorf("serving gRPC-Gateway server: %w", gwServer.ListenAndServeTLS("", ""))
}
