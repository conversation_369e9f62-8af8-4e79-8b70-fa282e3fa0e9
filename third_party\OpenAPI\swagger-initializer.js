window.onload = function() {
  //<editor-fold desc="Changeable Configuration Block">

  // the following lines will be replaced by docker/configurator, when it runs in a docker-container
  window.ui = SwaggerUIBundle({
    urls: [{"url":"clients/v1/client.swagger.json","name":"clients/v1/client.swagger.json"},{"url":"tasks/v1/task.swagger.json","name":"tasks/v1/task.swagger.json"},{"url":"patches/v1/patch.swagger.json","name":"patches/v1/patch.swagger.json"},{"url":"patches/","name":"patches/"},{"url":"v1/patch.swagger.json","name":"v1/patch.swagger.json"},{"url":"sysinfos/v1/sysinfo.swagger.json","name":"sysinfos/v1/sysinfo.swagger.json"},{"url":"programs/v1/program.swagger.json","name":"programs/v1/program.swagger.json"},{"url":"logs/v1/log.swagger.json","name":"logs/v1/log.swagger.json"},{"url":"configs/v1/config.swagger.json","name":"configs/v1/config.swagger.json"},{"url":"packages/v1/package.swagger.json","name":"packages/v1/package.swagger.json"},{"url":"channels/v1/channel.swagger.json","name":"channels/v1/channel.swagger.json"}],
    dom_id: '#swagger-ui',
    deepLinking: true,
    presets: [
      SwaggerUIBundle.presets.apis,
      SwaggerUIStandalonePreset
    ],
    plugins: [
      SwaggerUIBundle.plugins.DownloadUrl
    ],
    layout: "StandaloneLayout"
  });

  //</editor-fold>
};
