import{j as e,m as t,b as a,s as r,B as l,T as n}from"./index.e8bac691.js";const{Text:o}=n,s=e=>"running"===e.state,i=e=>e.bootable&&!s(e),d=e=>e.bootable&&s(e),c=e=>!s(e);function m(n,s){return[{title:n["program.columns.id"],dataIndex:"id"},{title:n["program.columns.name"],dataIndex:"name",render:t=>e(o,{copyable:!0,children:t})},{title:n["program.columns.installPath"],dataIndex:"installPath",render:t=>e(o,{copyable:!0,children:t})},{title:n["program.columns.state"],dataIndex:"state",render:(a,r)=>{switch(r.state){case"running":return e(t,{color:"green",size:"small",children:n["program.columns.state.running"]});case"exited":return e(t,{color:"red",size:"small",children:n["program.columns.state.exited"]});default:return r.state?e(t,{color:"gray",size:"small",children:n[`program.columns.state.${r.state}`]||r.state}):null}}},{title:n["program.columns.operation"],dataIndex:"operation",headerCellStyle:{paddingLeft:"15px"},render:(t,o)=>a(r,{children:[i(o)?e(l,{type:"text",size:"small",onClick:()=>s(o,"start"),children:n["program.columns.operation.start"]}):d(o)?e(l,{type:"text",size:"small",onClick:()=>s(o,"stop"),children:n["program.columns.operation.stop"]}):null,c(o)?e(l,{type:"text",size:"small",onClick:()=>s(o,"delete"),children:n["program.columns.operation.delete"]}):null]})}]}export{m as getColumns};
