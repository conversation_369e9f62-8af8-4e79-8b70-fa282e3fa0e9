import{r as e,z as r,b as t,j as a,as as o,c as n,y as s,at as i,W as l,C as c,a as p,A as u,R as f,e as d,_ as h}from"./index.6227d37e.js";import{a as m,I as y}from"./index.182aa2de.js";var v=globalThis&&globalThis.__assign||function(){return v=Object.assign||function(e){for(var r,t=1,a=arguments.length;t<a;t++)for(var o in r=arguments[t])Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o]);return e},v.apply(this,arguments)},b=function(e,r){if(s(e)){var t=Object.keys(e).map((function(r){return e[r]+" "+r})).join(",");return v({backgroundImage:"linear-gradient(to right, "+t+")"},r?{backgroundSize:1e4/r+"%"}:{})}return{backgroundColor:e}},g={small:3,default:4,large:8};function O(s){var i,l,c=s.type,p=s.prefixCls,u=s.buffer,f=s.percent,d=s.color,h=s.animation,m=s.bufferColor,y=s.formatText,O=s.trailColor,w=s.showText,x=void 0===w||w,j=s.size,k=void 0===j?"default":j,C=s.status,P=void 0===C?"normal":C,N=p+"-"+c,T=s.strokeWidth||g[k],S="success"===P||"error"===P||f>=100,D=e.exports.useCallback((function(){return r(y)?y(f):"error"===P?t("span",{children:[f,"% ",a(o,{})]}):f+"%"}),[y,f,P]);return t("div",{className:N+"-wrapper",children:[t("div",{className:N+"-outer",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":f,style:{height:T,backgroundColor:O},children:[u&&!S&&a("div",{className:N+"-inner-buffer",style:v({width:(f>0?f+10:0)+"%"},b(m))}),a("div",{className:n(N+"-inner",(i={},i[N+"-inner-animate"]=h,i)),style:v({width:f+"%"},b(d,f))})]}),x&&a("div",{className:n(N+"-text",(l={},l[N+"-text-with-icon"]=P,l)),children:D()})]})}var w={mini:4,small:3,default:4,large:4},x={mini:16,small:48,default:64,large:80},j=function(o){var n=o.size,c=o.percent,p=void 0===c?0:c,u=o.prefixCls,f=o.showText,d=o.status,h=o.formatText,v=s(o.color),b=o.width||x[n],g=o.strokeWidth||("mini"===n?b/2:w[n]),O=(b-g)/2,j=2*Math.PI*O,k=b/2,C=u+"-circle",P=C+"-svg",N=e.exports.useCallback((function(e){if(r(h))return h(p);switch(e){case"success":return a(y,{});case"error":return a(m,{});default:return p+"%"}}),[h,p]),T=i(u+"-linear-gradient-"),S=v?"url(#"+T+")":o.color,D=t("div",{className:C+"-wrapper",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":p,style:{width:b,height:b},children:[t("svg",{viewBox:"0 0 "+b+" "+b,className:""+P,children:[v&&a("defs",{children:a("linearGradient",{id:T,x1:"0",y1:"1",x2:"0",y2:"0",children:Object.keys(o.color).sort().map((function(e){return a("stop",{offset:e,stopColor:o.color[e]},e)}))})}),a("circle",{className:C+"-mask",fill:"none",cx:k,cy:k,r:O,strokeWidth:o.pathStrokeWidth||("mini"===n?g:Math.max(2,g-2)),style:{stroke:o.pathStrokeColor}}),a("circle",{className:C+"-path",fill:"none",cx:k,cy:k,r:O,strokeWidth:g,style:{stroke:S,strokeDasharray:j,strokeDashoffset:(p>100?100:1-p/100)*j}})]}),f&&"mini"!==n&&a("div",{className:C+"-text",children:N(d)})]});return"mini"===n&&"success"===d&&"circle"===o.type&&(D=a("div",{className:C+"-wrapper",style:{width:b,height:b},children:a(y,{style:{fontSize:b-2,color:S}})})),"mini"===n&&f?a(l,{content:a("div",{className:C+"-text",children:N("normal")}),children:D}):D},k=globalThis&&globalThis.__read||function(e,r){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var a,o,n=t.call(e),s=[];try{for(;(void 0===r||r-- >0)&&!(a=n.next()).done;)s.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(o)throw o.error}}return s},C=globalThis&&globalThis.__spreadArray||function(e,r,t){if(t||2===arguments.length)for(var a,o=0,n=r.length;o<n;o++)!a&&o in r||(a||(a=Array.prototype.slice.call(r,0,o)),a[o]=r[o]);return e.concat(a||Array.prototype.slice.call(r))},P=function(s){var i,l=s.prefixCls,c=s.percent,p=s.color,u=s.type,f=s.formatText,d=s.trailColor,h=s.showText,m=void 0===h||h,y=s.size,v=void 0===y?"default":y,b=s.status,g=void 0===b?"normal":b,O=s.strokeWidth||("small"===v?8:4),w=l+"-"+u,x=O,j=e.exports.useCallback((function(){return r(f)?f(c):"error"===g?t("span",{children:[c,"% ",a(o,{})]}):c+"%"}),[f,c,g]);return t("div",{className:w+"-wrapper",children:[a("div",{className:w+"-outer",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":c,style:{height:x},children:C([],k(new Array(s.steps)),!1).map((function(e,r){var t,o=c>100/s.steps*r;return a("div",{className:n(w+"-item",(t={},t[w+"-item-active"]=o,t)),style:{backgroundColor:o?p:d||""}},r)}))}),m&&a("div",{className:n(w+"-text",(i={},i[w+"-text-with-icon"]=g,i)),children:j()})]})},N=globalThis&&globalThis.__assign||function(){return N=Object.assign||function(e){for(var r,t=1,a=arguments.length;t<a;t++)for(var o in r=arguments[t])Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o]);return e},N.apply(this,arguments)},T=globalThis&&globalThis.__rest||function(e,r){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&r.indexOf(a)<0&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)r.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(t[a[o]]=e[a[o]])}return t},S={type:"line",showText:!0,percent:0,size:"default"};var D=e.exports.forwardRef((function(r,o){var s,i=e.exports.useContext(c),l=i.getPrefixCls,f=i.componentConfig,d=i.rtl,h=p(r,S,null==f?void 0:f.Progress),m=h.className,y=h.style,v=h.size,b=h.width,g=h.strokeWidth,w=h.steps,x=h.percent,k=T(h,["className","style","size","width","strokeWidth","steps","percent"]),C=w&&"circle"!==h.type?"steps":h.type,D=l("progress"),I="status"in h?h.status:x>=100?"success":"normal",W={width:b};return"mini"===v&&"line"===C&&(W.width=b||16,W.height=b||16),t("div",{...N({ref:o,className:n(D,D+"-"+C,D+"-"+v,(s={},s[D+"-is-"+I]="normal"!==I,s[D+"-rtl"]=d,s),m),style:N(N({},W),y)},u(k,["type","animation","status","color","trailColor","showText","formatText","buffer","bufferColor"])),children:["steps"===C&&a(P,{...N({},h,{type:C,status:I,prefixCls:D})}),"circle"===C&&a(j,{...N({width:h.width},h,{pathStrokeColor:h.trailColor,status:I,prefixCls:D})}),"line"===C&&("mini"===v?a(j,{...N({pathStrokeColor:h.trailColor},h,{pathStrokeWidth:g||4,width:b||16,strokeWidth:g||4,prefixCls:D,status:I})}):a(O,{...N({},h,{status:I,prefixCls:D})}))]})}));D.displayName="Progress";var I=D;function W(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,a)}return t}function _(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?W(Object(t),!0).forEach((function(r){h(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):W(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function z(r,t){var o=e.exports.useContext(d).prefixCls,n=void 0===o?"arco":o,s=r.spin,i=r.className,l=_(_({"aria-hidden":!0,focusable:!1,ref:t},r),{},{className:"".concat(i?i+" ":"").concat(n,"-icon ").concat(n,"-icon-file")});return s&&(l.className="".concat(l.className," ").concat(n,"-icon-loading")),delete l.spin,delete l.isIcon,a("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:a("path",{d:"M16 21h16m-16 8h10m11 13H11a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h21l7 7v27a2 2 0 0 1-2 2Z"})})}var E=f.forwardRef(z);E.defaultProps={isIcon:!0},E.displayName="IconFile";var M=E;function A(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,a)}return t}function R(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?A(Object(t),!0).forEach((function(r){h(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):A(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function B(r,t){var o=e.exports.useContext(d).prefixCls,n=void 0===o?"arco":o,s=r.spin,i=r.className,l=R(R({"aria-hidden":!0,focusable:!1,ref:t},r),{},{className:"".concat(i?i+" ":"").concat(n,"-icon ").concat(n,"-icon-upload")});return s&&(l.className="".concat(l.className," ").concat(n,"-icon-loading")),delete l.spin,delete l.isIcon,a("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:a("path",{d:"M14.93 17.071 24.001 8l9.071 9.071m-9.07 16.071v-25M40 35v6H8v-6"})})}var H=f.forwardRef(B);H.defaultProps={isIcon:!0},H.displayName="IconUpload";var F=H;function G(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,a)}return t}function L(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?G(Object(t),!0).forEach((function(r){h(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):G(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function U(r,t){var o=e.exports.useContext(d).prefixCls,n=void 0===o?"arco":o,s=r.spin,i=r.className,l=L(L({"aria-hidden":!0,focusable:!1,ref:t},r),{},{className:"".concat(i?i+" ":"").concat(n,"-icon ").concat(n,"-icon-arrow-left")});return s&&(l.className="".concat(l.className," ").concat(n,"-icon-loading")),delete l.spin,delete l.isIcon,a("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:a("path",{d:"M20.272 11.27 7.544 23.998l12.728 12.728M43 24H8.705"})})}var V=f.forwardRef(U);V.defaultProps={isIcon:!0},V.displayName="IconArrowLeft";var Z=V;export{Z as I,I as P,F as a,M as b};
