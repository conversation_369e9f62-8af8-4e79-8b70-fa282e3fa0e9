import{r as t,j as s,b as e,s as a,l as o}from"./index.6227d37e.js";import r from"./os-status-cpu.f6f70f2b.js";import n from"./os-status-memory.6acb464c.js";import i from"./os-information.c9301557.js";import c from"./os-network.e8729c14.js";import"./index.b18348d9.js";import"./index.a7402227.js";var d="_layout_khazc_1",m="_layout-content_khazc_7";function l(){const[l,u]=t.exports.useState({os:{type:"",version:"",arch:""},cpu:{cores:"",usage:""},memory:{total:"",free:"",used:"",usage:""},disk:{total:"",free:"",used:"",usage:""},times:{up:""},networks:[]});return t.exports.useEffect((()=>{o("/api/v1/sysinfo").then((t=>{const s=t&&t.data;if(!s&&!s.data)return;const e=[];for(const[,a]of Object.entries(s.data.networks)){const t="IPv4"===a.family,s=null!=a.mac&&""!==a.mac;t&&s&&e.push(a)}s.data.networks=e,u(s.data)}))}),[]),s("div",{children:s("div",{className:d,children:s("div",{className:m,children:e(a,{size:16,direction:"vertical",style:{width:"100%"},children:[s(i,{data:l}),s(r,{data:l}),s(n,{data:l}),null!=l.networks&&l.networks.length>0?s(c,{data:l}):null]})})})})}export{l as default};
