const i18n = {
  'en-US': {
    'importForm.name.label': 'Name',
    'importForm.name.required': 'Please enter the name',
    'importForm.name.placeholder': 'Please enter the name',
    'importForm.attachment.label': 'Attachment',
    'importForm.attachment.required': 'Please upload the attachment',
    'importForm.attachment.support.tips': 'Only zip can be uploaded',
    'importForm.attachment.support-error.tips':
      'Unacceptable file type, please re-upload the specified file type!',
    'importForm.publish.confirm.title': 'Publish confirmation',
    'importForm.publish.confirm.content':
      'Do you want to confirm the publication?',
    'importForm.created.fail': 'Failed to create',
    'importForm.created.fail.already-exists': 'Package already exists',
    'importForm.created.fail.already-published': 'This version of the package has been published',
    'importForm.created.success': 'Created successfully',
    'importForm.created.success.title': 'Created successfully',
    'importForm.created.success.desc': 'Package created successfully',
    'importForm.created.success.again': 'Create again',
    'importForm.operations.back': 'Back',
    'importForm.operations.submit': 'Submit',
  },
  'zh-CN': {
    'importForm.name.label': '名称',
    'importForm.name.required': '请输入名称',
    'importForm.name.placeholder': '请输入名称',
    'importForm.attachment.label': '附件',
    'importForm.attachment.required': '请上传附件',
    'importForm.attachment.support.tips': '仅支持zip上传',
    'importForm.attachment.support-error.tips': '不接受的文件类型，请重新上传指定文件类型！',
    'importForm.publish.confirm.title': '发布确认',
    'importForm.publish.confirm.content': '是否确认发布？',
    'importForm.created.fail': '创建失败',
    'importForm.created.fail.already-exists': '包已存在',
    'importForm.created.fail.already-published': '此版本包已有过发布',
    'importForm.created.success': '创建成功',
    'importForm.created.success.title': '创建成功',
    'importForm.created.success.desc': '包创建成功',
    'importForm.created.success.again': '再次创建',
    'importForm.operations.back': '返回',
    'importForm.operations.submit': '提交',
  },
};

export default i18n;
