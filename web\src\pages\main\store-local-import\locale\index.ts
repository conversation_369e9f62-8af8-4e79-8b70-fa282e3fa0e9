const i18n = {
  'en-US': {
    'menu.main.store.localImport': 'Local Import',
    'localImport.title': 'Local File Import',
    'localImport.description': 'Import application packages directly from local files, bypassing network upload for faster deployment.',
    'localImport.form.packageName': 'Package Name',
    'localImport.form.packageName.placeholder': 'Enter package name, e.g., ais-cell',
    'localImport.form.localPath': 'Local File Path',
    'localImport.form.localPath.placeholder': 'Enter the absolute path of the compressed file, e.g., D:\\Programs\\20250815-Vietnam-Foxconn\\ais-cell.zip',
    'localImport.form.localPath.required': 'Please enter the local file path',
    'localImport.form.localPath.zipOnly': 'Please enter a .zip format compressed file path',
    'localImport.button.import': 'Start Import',
    'localImport.button.importing': 'Importing...',
    'localImport.button.reset': 'Reset',
    'localImport.button.back': 'Back',
    'localImport.success': 'Local import successful',
    'localImport.failed': 'Local import failed',
    'localImport.progress.title': 'Import Progress',
    'localImport.progress.importing': 'Importing file...',
    'localImport.progress.completed': 'Import completed',
    'localImport.progress.extracting': 'Extracting files...',
    'localImport.progress.copying': 'Copying files...',
    'localImport.progress.processing': 'Processing package...',
    'localImport.info.alert': 'Local file import allows you to directly enter the absolute path of local compressed files for import, bypassing network upload. The system will automatically parse version information from the compressed package, suitable for large files or local development environments.',
    'localImport.task.created': 'Import task created successfully',
    'localImport.task.failed': 'Failed to create import task',
    'localImport.monitor.title': 'Local File Import Progress'
  },
  'zh-CN': {
    'menu.main.store.localImport': '本地导入',
    'localImport.title': '本地文件导入',
    'localImport.description': '直接从本地文件导入应用包，跳过网络上传过程，实现快速部署。',
    'localImport.form.packageName': '包名称',
    'localImport.form.packageName.placeholder': '请输入包名称，如：ais-cell',
    'localImport.form.localPath': '本地文件路径',
    'localImport.form.localPath.placeholder': '请输入压缩文件的绝对路径，如：D:\\最新程序\\20250815-越南富士康\\ais-cell.zip',
    'localImport.form.localPath.required': '请输入本地文件路径',
    'localImport.form.localPath.zipOnly': '请输入.zip格式的压缩文件路径',
    'localImport.button.import': '开始导入',
    'localImport.button.importing': '导入中...',
    'localImport.button.reset': '重置',
    'localImport.button.back': '返回',
    'localImport.success': '本地导入成功',
    'localImport.failed': '本地导入失败',
    'localImport.progress.title': '导入进度',
    'localImport.progress.importing': '正在导入文件...',
    'localImport.progress.completed': '导入完成',
    'localImport.progress.extracting': '正在解压文件...',
    'localImport.progress.copying': '正在复制文件...',
    'localImport.progress.processing': '正在处理包...',
    'localImport.info.alert': '本地文件导入功能允许您直接输入本地压缩文件的绝对路径进行导入，跳过网络上传过程。系统将自动从压缩包中解析版本信息，适用于大文件或本地开发环境。',
    'localImport.task.created': '导入任务创建成功',
    'localImport.task.failed': '创建导入任务失败',
    'localImport.monitor.title': '本地文件导入进度'
  }
};

export default i18n;