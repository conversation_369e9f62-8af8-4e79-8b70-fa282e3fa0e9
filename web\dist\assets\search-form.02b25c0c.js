import{u as a,b as e,j as r,af as o,Q as t,B as s,ag as l,G as m}from"./index.e8bac691.js";import{I as n}from"./index.dde5f5a0.js";const p={"en-US":{"menu.main":"Main","menu.main.store":"Store","menu.list":"List","menu.list.searchTable":"Search Table","program.const.answer.yes":"Yes","program.const.answer.no":"No","program.const.ok":"Ok","program.const.cancel":"Cancel","program.columns.id":"Id","program.columns.name":"Name","program.columns.state":"State","store.columns.state.installing":"Installing","store.columns.state.installed":"Installed","store.columns.state.exited":"Exited","store.columns.state.running":"Running","store.columns.state.unknown":"Unknown","program.columns.installPath":"Install Path","program.columns.sort":"Sort","program.columns.operation":"Operation","program.columns.operation.delete":"Delete","program.columns.operation.start":"Start","program.columns.operation.stop":"Stop","program.tables.main.form.search":"Search","program.tables.main.form.reset":"Reset","program.tables.main.operation.delete.success":"Delete successfully","program.tables.main.operation.no-support":"No support","program.tables.main.modal.delete.title":"Delete confirmation","program.tables.main.modal.delete.content":"Are you sure you want to delete this record?","program.tables.main.modal.delete.operation.success":"Delete successfully","program.tables.main.modal.delete.operation.fail":"Delete failed","program.tables.main.modal.stop.title":"Stop confirmation","program.tables.main.modal.stop.content":"Are you sure you want to stop?","program.tables.main.modal.stop.operation.success":"Stop successfully","program.tables.main.modal.stop.operation.fail":"Stop failed","program.tables.main.modal.start.title":"Start confirmation","program.tables.main.modal.start.content":"Are you sure you want to start?","program.tables.main.modal.start.operation.success":"Start successfully","program.tables.main.modal.start.operation.fail":"Start failed","program.forms.search.id.placeholder":"Please enter the ID","program.forms.search.name.placeholder":"Please enter the name","program.forms.search.sort.placeholder":"Please enter the sort","program.forms.search.all.placeholder":"all"},"zh-CN":{"menu.main":"主页","menu.main.store":"应用仓库","menu.main.program":"程序监控","menu.list":"列表页","menu.list.searchTable":"查询表格","program.const.answer.yes":"是","program.const.answer.no":"否","program.const.ok":"确定","program.const.cancel":"取消","program.columns.id":"ID","program.columns.name":"名称","program.columns.state":"状态","program.columns.state.installing":"安装中","program.columns.state.installed":"已安装","program.columns.state.exited":"已退出","program.columns.state.running":"运行中","program.columns.state.unknown":"未知","program.columns.installPath":"路径","program.columns.sort":"排序","program.columns.operation":"操作","program.columns.operation.delete":"删除","program.columns.operation.start":"启动","program.columns.operation.stop":"停止","program.tables.main.form.search":"查询","program.tables.main.form.reset":"重置","program.tables.main.operation.delete.success":"删除成功","program.tables.main.operation.no-support":"暂不支持","program.tables.main.modal.delete.title":"删除确认","program.tables.main.modal.delete.content":"确定删除该条记录吗？","program.tables.main.modal.delete.operation.success":"删除成功","program.tables.main.modal.delete.operation.fail":"删除失败","program.tables.main.modal.stop.title":"停用确认","program.tables.main.modal.stop.content":"确定停用吗？","program.tables.main.modal.stop.operation.success":"停用成功","program.tables.main.modal.stop.operation.fail":"停用失败","program.tables.main.modal.start.title":"启用确认","program.tables.main.modal.start.content":"确定启用吗？","program.tables.main.modal.start.operation.success":"启用成功","program.tables.main.modal.start.operation.fail":"启用失败","program.forms.search.id.placeholder":"请输入ID","program.forms.search.name.placeholder":"请输入名称","program.forms.search.sort.placeholder":"请输入排序","program.forms.search.all.placeholder":"全部"}};var i="_search-form-wrapper_pbsw0_16",c="_right-button_pbsw0_21",d="_search-form_pbsw0_16";const{Row:g,Col:u}=m,{useForm:h}=o;function b(m){const b=a(p),{loading:f}=m,[w]=h();return e("div",{className:i,children:[r(o,{form:w,className:d,labelAlign:"left",labelCol:{span:4},wrapperCol:{span:20},children:e(g,{gutter:24,children:[r(u,{span:12,children:r(o.Item,{label:b["program.columns.id"],field:"id",children:r(t,{placeholder:b["program.forms.search.id.placeholder"],allowClear:!0})})}),r(u,{span:12,children:r(o.Item,{label:b["program.columns.name"],field:"name",children:r(t,{allowClear:!0,placeholder:b["program.forms.search.name.placeholder"]})})}),r(u,{span:12,children:r(o.Item,{label:b["program.columns.sort"],field:"sort",children:r(t,{allowClear:!0,placeholder:b["program.forms.search.sort.placeholder"]})})})]})}),e("div",{className:c,children:[r(s,{loading:f,type:"primary",icon:r(l,{}),onClick:()=>{const a=w.getFieldsValue();m.onSearch(a)},children:b["program.tables.main.form.search"]}),r(s,{loading:f,icon:r(n,{}),onClick:()=>{w.resetFields(),m.onSearch({})},children:b["program.tables.main.form.reset"]})]})]})}var f=Object.freeze(Object.defineProperty({__proto__:null,default:b},Symbol.toStringTag,{value:"Module"}));export{b as S,p as i,f as s};
