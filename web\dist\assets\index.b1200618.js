import{u as e,r as a,b as t,j as o,k as r,l as n,i as s}from"./index.fe31dd41.js";import{C as i}from"./index.41001b26.js";import{T as m}from"./index.a7c49f62.js";import{i as l,S as c}from"./search-form.ca2efb04.js";import{getColumns as p}from"./constants.6a89dd08.js";import"./index.64abbf40.js";function d(){const d=e(l),g=async(e,a)=>{if("delete"===a)r.confirm({title:d["program.tables.main.modal.delete.title"],content:d["program.tables.main.modal.delete.content"],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}`,{method:"delete"}).then((()=>{s.success(d["program.tables.main.modal.delete.operation.success"]),$()})).catch((()=>{s.error(d["program.tables.main.modal.delete.operation.fail"])})).finally((()=>{z(!1)}))}});else r.confirm({title:d[`program.tables.main.modal.${a}.title`],content:d[`program.tables.main.modal.${a}.content`],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}/cmds`,{method:"post",data:{type:a},timeout:6e4}).then((e=>{const t=e&&e.data;(t||t.data)&&(0!==t.code?s.error(d[`program.tables.main.modal.${a}.operation.fail`]):s.success(d[`program.tables.main.modal.${a}.operation.success`]),$())})).catch((()=>{s.error(d[`program.tables.main.modal.${a}.operation.fail`])})).finally((()=>{z(!1)}))}})},f=a.exports.useMemo((()=>p(d,g)),[d]),[u,S]=a.exports.useState([]),[b,h]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[x,z]=a.exports.useState(!0),[j,y]=a.exports.useState({});function $(){const{current:e,pageSize:a}=b;z(!0),n("/api/v1/programs",{data:{page:e,size:a,...j}}).then((t=>{const o=t&&t.data;(o||o.data||o.data.content)&&(S(o.data.content),h({...b,current:e,pageSize:a,total:o.data.total}))})).finally((()=>{z(!1)}))}return a.exports.useEffect((()=>{$()}),[b.current,b.pageSize,JSON.stringify(j)]),t(i,{children:[o(c,{loading:x,onSearch:function(e){h({...b,current:1}),y(e)}}),o(m,{rowKey:"id",loading:x,onChange:function({current:e,pageSize:a}){h({...b,current:e,pageSize:a})},pagination:b,columns:f,data:u})]})}export{d as default};
