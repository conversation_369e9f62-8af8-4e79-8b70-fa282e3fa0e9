GOPATH:=$(shell go env GOPATH)
BUF_VERSION:=v1.29.0
SWAGGER_UI_VERSION:=v5.11.7

C_NAME=ais-server
C_VERSION=1.1.2

generate: generate/proto generate/swagger-ui
generate/proto:
	go run github.com/bufbuild/buf/cmd/buf@$(BUF_VERSION) generate
	# buf generate
generate/swagger-ui:
	SWAGGER_UI_VERSION=$(SWAGGER_UI_VERSION) ./scripts/generate-swagger-ui.sh

.PHONY: init
init:
	@go get -u google.golang.org/protobuf/proto
	@go install github.com/golang/protobuf/protoc-gen-go@latest
	@go install github.com/go-micro/generator/cmd/protoc-gen-micro@latest

.PHONY: proto
proto:
	@protoc --proto_path=. --micro_out=. --go_out=:. proto/health.proto
	
.PHONY: update
update:
	@go get -u

.PHONY: tidy
tidy:
	@go mod tidy

.PHONY: build
build:
	# @go build -o ais-server *.go
    # C_VERSION=`curl -k https://localhost:5800/api/v1/config | jq -r .version`
	# GOOS=linux GOARCH=amd64 go build -o ${C_NAME}-${C_VERSION}-linux-amd64
	# GOOS=linux GOARCH=arm64 go build -o ${C_NAME}-${C_VERSION}-linux-arm64
	GOOS=windows GOARCH=amd64 go build -o ${C_NAME}-${C_VERSION}-windows-amd64.exe
	# GOOS=darwin GOARCH=amd64 go build -o ${C_NAME}-${C_VERSION}-darwin-amd64
	# echo ${C_VERSION} > latest

.PHONY: test
test:
	@go test -v ./... -cover

.PHONY: docker
docker:
	@docker build -t ais-server:latest .