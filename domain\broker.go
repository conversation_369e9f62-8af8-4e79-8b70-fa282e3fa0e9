package domain

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/hooks/auth"
	"github.com/mochi-mqtt/server/v2/listeners"
	"github.com/mochi-mqtt/server/v2/packets"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/insecure"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	sysinfov1 "gitlab.jhonginfo.com/product/ais-server/proto/sysinfos/v1"
)

type MQTTServer struct {
	Id         string
	TCPAddress string
	WSAddress  string

	Started bool
}

var server = mqtt.New(&mqtt.Options{
	InlineClient: true, // you must enable inline client to use direct publishing and subscribing.
})

var currentHeartbeat = 0
var maxHeartbeat = 10

func (t *MQTTServer) Run() {
	server.Log = slog.Default()

	sigs := make(chan os.Signal, 1)
	done := make(chan bool, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigs
		done <- true
	}()

	_ = server.AddHook(new(auth.AllowHook), nil)

	// Basic TLS Config
	var tlsConfig *tls.Config
	tcpProtocol := "tcp"
	wsProtocol := "ws"
	if config.IsSSL() {
		tlsConfig = &tls.Config{
			Certificates: []tls.Certificate{insecure.Cert},
		}
		tcpProtocol = "tls"
		wsProtocol = "wss"
	}

	tcp := listeners.NewTCP(listeners.Config{
		ID:        "tcp_" + t.Id,
		Address:   t.TCPAddress,
		TLSConfig: tlsConfig,
	})
	err := server.AddListener(tcp)
	if err != nil {
		slog.Error("MQTT server listen to err: " + err.Error())
		return
	}

	ws := listeners.NewWebsocket(listeners.Config{
		ID:      "ws_" + t.Id,
		Address: t.WSAddress,
	})

	err = server.AddListener(ws)
	if err != nil {
		slog.Error("MQTT server listen to err: " + err.Error())
		return
	}

	err = server.Serve()
	if err != nil {
		slog.Error("Failed to serve MQTT: " + err.Error())
	}
	slog.Info(fmt.Sprintf("Serving MQTT on %s://%s", tcpProtocol, t.TCPAddress))
	slog.Info(fmt.Sprintf("Serving MQTT on %s://%s", wsProtocol, t.WSAddress))
	t.Started = true

	t.Subscribe(TOPIC_SYS_BROKER_CLIENTS_TOTAL, 1, func(topic string, payload []byte) {
		total, err := strconv.Atoi(string(payload))
		if err != nil {
			slog.Error("Parse total err: " + err.Error())
			return
		}
		currentHeartbeat++
		if total > 1 && currentHeartbeat >= maxHeartbeat {
			ids := make([]string, 0)
			clientsMap := server.Clients.GetAll()
			for k := range clientsMap {
				client := clientsMap[k]
				ids = append(ids, client.ID)
			}
			err = UpdateClientsStateToOfflineByNotIn(ids)
			if err != nil {
				slog.Error("Update clients state to offline err: " + err.Error())
			}
			currentHeartbeat = 0
		}
	})

	t.Subscribe(TOPIC_ALL_CLIENTS_HEARTBEAT, 2, func(topic string, payload []byte) {
		var client *Client
		err := json.Unmarshal(payload, &client)
		if err != nil {
			slog.Error("Unmarshal client err: " + err.Error())
			return
		}
		client.IsLocal = 0
		if current.Id == client.Id {
			client.IsLocal = 1
		}
		if client.Networks != "" {
			var newNetworks []*sysinfov1.Network
			err = json.Unmarshal([]byte(client.Networks), &newNetworks)
			if err == nil && len(newNetworks) > 1 {
				for _, network := range newNetworks {
					protocol := "http"
					isSSL := client.Ssl == 1
					if isSSL {
						protocol = "https"
					}
					server := fmt.Sprintf("%s://%s:%d", protocol, network.Address, client.GatewayPort)
					err := lib.CheckServer(server, CHECK_SERVER_TIMEOUT)
					if err == nil {
						networks := make([]*sysinfov1.Network, 0)
						networks = append(networks, network)
						networksBytes, _ := json.Marshal(networks)
						client.Networks = string(networksBytes)
						break
					}
				}
			}
		}
		old := &Client{
			Id: client.Id,
		}
		err = old.GetById()
		if err != nil {
			client.State = ClientState.Online
			err = client.Add()
		} else {
			client.CreatedAt = old.CreatedAt
			err = client.Online()
		}
		if err != nil {
			slog.Error("Save client err: " + err.Error())
		}
	})

	<-done
	slog.Warn("MQTT server caught signal, stopping...")
	_ = server.Close()
	slog.Warn("MQTT server stopped")
	t.Started = false
}

func (t *MQTTServer) Get() *mqtt.Server {
	return server
}

func (t *MQTTServer) Subscribe(filter string, subscriptionId int, handle func(topic string, payload []byte)) error {
	if !t.Started {
		return fmt.Errorf("server not started")
	}
	err := server.Subscribe(filter, subscriptionId, func(cl *mqtt.Client, sub packets.Subscription, pk packets.Packet) {
		slog.Debug(fmt.Sprintf("Server(%s) receive message from topic(%s): %s", t.Id, pk.TopicName, string(pk.Payload)))
		handle(pk.TopicName, pk.Payload)
	})
	if err != nil {
		slog.Error(fmt.Sprintf("Server(%s) subscribe topic(%s) error: %s", t.Id, filter, err.Error()))
		return err
	} else {
		slog.Info(fmt.Sprintf("Server(%s) subscribe topic(%s) successful", t.Id, filter))
	}
	return nil
}
