import{u as e,r as a,b as t,j as s,s as n,B as o,l as r,i}from"./index.6227d37e.js";import{C as c}from"./index.a7402227.js";import{T as p}from"./index.5990d978.js";import{i as d,s as u}from"./index.module.cf3602d3.js";import{getColumns as l}from"./constants.1e035696.js";import{I as g}from"./index.be17d0db.js";function m(){const m=e(d),f=a.exports.useMemo((()=>l(m)),[m]),[h,x]=a.exports.useState([]),[S,z]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:100,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","30","40","50","100","200"]}),[j,C]=a.exports.useState(!0);function b(){const{current:e,pageSize:a}=S;C(!0),r("/api/v1/logs",{data:{page:e,size:a}}).then((t=>{const s=t&&t.data;(s||s.data||s.data.content)&&(x(s.data.content),z({...S,current:e,pageSize:a,total:s.data.total}))})).finally((()=>{C(!1)}))}return a.exports.useEffect((()=>{b()}),[S.current,S.pageSize]),t(c,{children:[s("div",{className:u["button-group"],children:s(n,{children:s(o,{type:"primary",status:"danger",icon:s(g,{}),onClick:function(){r("/api/v1/logs",{method:"delete"}).then((()=>{i.success(m["searchTable.operations.clear.success"]),b()}))},children:m["searchTable.operations.clear"]})})}),s(p,{rowKey:"id",loading:j,onChange:function({current:e,pageSize:a}){z({...S,current:e,pageSize:a})},pagination:S,columns:f,data:h})]})}export{m as default};
