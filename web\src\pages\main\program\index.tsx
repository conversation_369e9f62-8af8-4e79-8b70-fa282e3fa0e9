import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  Card,
  PaginationProps,
  Button,
  Message,
  Modal,
  Notification,
} from '@arco-design/web-react';

import api from '@/utils/api';
import useLocale from '@/utils/useLocale';

import SearchForm from './search-form';
import locale from './locale';
import { getColumns } from './constants';

function ITable() {
  const t = useLocale(locale);

  const tableCallback = async (record, type) => {
    switch (type) {
      case 'delete':
        Modal.confirm({
          title: t['program.tables.main.modal.delete.title'],
          content: t['program.tables.main.modal.delete.content'],
          onOk: () => {
            setLoading(true);
            api(`/api/v1/programs/${record.id}`, {
              method: 'delete',
            })
              .then(() => {
                Message.success(
                  t['program.tables.main.modal.delete.operation.success'],
                );
                fetchData();
              })
              .catch(() => {
                Message.error(
                  t['program.tables.main.modal.delete.operation.fail'],
                );
              })
              .finally(() => {
                setLoading(false);
              });
          },
        });
        break;
      default:
        Modal.confirm({
          title: t[`program.tables.main.modal.${type}.title`],
          content: t[`program.tables.main.modal.${type}.content`],
          onOk: () => {
            setLoading(true);
            api(`/api/v1/programs/${record.id}/cmds`, {
              method: 'post',
              data: {
                type: type,
              },
              timeout: 60 * 1000, // 1 min
            })
              .then((res) => {
                const resData = res && res.data;
                if (!resData && !resData.data) {
                  return;
                }
                if (resData.code !== 0) {
                  Message.error(
                    t[`program.tables.main.modal.${type}.operation.fail`],
                  );
                } else {
                  Message.success(
                    t[`program.tables.main.modal.${type}.operation.success`],
                  );
                }
                fetchData();
              })
              .catch(() => {
                Message.error(
                  t[`program.tables.main.modal.${type}.operation.fail`],
                );
              })
              .finally(() => {
                setLoading(false);
              });
          },
        });
        break;
    }
  };

  const columns = useMemo(() => getColumns(t, tableCallback), [t]);

  const [data, setData] = useState([]);
  const [pagination, setPatination] = useState<PaginationProps>({
    sizeCanChange: true,
    showTotal: true,
    pageSize: 50,
    current: 1,
    pageSizeChangeResetCurrent: true,
    pageSizeOptions: ['10', '20', '50', '100', '200'],
  });
  const [loading, setLoading] = useState(true);
  const [formParams, setFormParams] = useState({});

  function fetchData() {
    const { current, pageSize } = pagination;
    setLoading(true);
    api('/api/v1/programs', {
      data: {
        page: current,
        size: pageSize,
        ...formParams,
      },
    })
      .then((res) => {
        const resData = res && res.data;
        if (!resData && !resData.data && !resData.data.content) {
          return;
        }
        setData(resData.data.content);
        setPatination({
          ...pagination,
          current,
          pageSize,
          total: resData.data.total,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }

  function onChangeTable({ current, pageSize }) {
    setPatination({
      ...pagination,
      current,
      pageSize,
    });
  }

  function handleSearch(params) {
    setPatination({ ...pagination, current: 1 });
    setFormParams(params);
  }

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, JSON.stringify(formParams)]);

  return (
    <Card>
      <SearchForm loading={loading} onSearch={handleSearch} />
      <Table
        rowKey="id"
        loading={loading}
        onChange={onChangeTable}
        pagination={pagination}
        columns={columns}
        data={data}
      />
    </Card>
  );
}

export default ITable;
