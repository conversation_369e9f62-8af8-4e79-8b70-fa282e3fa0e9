package domain

import (
	_ "embed"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
)

//go:embed scripts/darwin/health.sh
var healthShellBytesOfDarwin []byte

//go:embed scripts/darwin/install.sh
var installShellBytesOfDarwin []byte

//go:embed scripts/darwin/uninstall.sh
var uninstallShellBytesOfDarwin []byte

//go:embed scripts/linux/health.sh
var healthShellBytesOfLinux []byte

//go:embed scripts/linux/install.sh
var installShellBytesOfLinux []byte

//go:embed scripts/linux/uninstall.sh
var uninstallShellBytesOfLinux []byte

//go:embed scripts/linux/start.sh
var startShellBytesOfLinux []byte

//go:embed scripts/linux/stop.sh
var stopShellBytesOfLinux []byte

//go:embed scripts/windows/handle
var hanldeExeBytes []byte

//go:embed scripts/windows/health.bat
var healthShellBytesOfWin []byte

//go:embed scripts/windows/start.bat
var startShellBytesOfWin []byte

//go:embed scripts/windows/stop.bat
var stopShellBytesOfWin []byte

//go:embed scripts/windows/install.bat
var installShellBytesOfWin []byte

//go:embed scripts/windows/uninstall.bat
var uninstallShellBytesOfWin []byte

//go:embed scripts/windows/rm.bat
var rmShellBytesOfWin []byte

var shellBytesMap = map[string]*map[string]*[]byte{
	OsType.Darwin: {
		PackageConfigScriptNames.Health:    &healthShellBytesOfDarwin,
		PackageConfigScriptNames.Install:   &installShellBytesOfDarwin,
		PackageConfigScriptNames.Uninstall: &uninstallShellBytesOfDarwin,
	},
	OsType.Linux: {
		PackageConfigScriptNames.Health:    &healthShellBytesOfLinux,
		PackageConfigScriptNames.Install:   &installShellBytesOfLinux,
		PackageConfigScriptNames.Uninstall: &uninstallShellBytesOfLinux,
		PackageConfigScriptNames.Start:     &startShellBytesOfLinux,
		PackageConfigScriptNames.Stop:      &stopShellBytesOfLinux,
	},
	OsType.Windows: {
		PackageConfigScriptNames.Health:    &healthShellBytesOfWin,
		PackageConfigScriptNames.Start:     &startShellBytesOfWin,
		PackageConfigScriptNames.Stop:      &stopShellBytesOfWin,
		PackageConfigScriptNames.Install:   &installShellBytesOfWin,
		PackageConfigScriptNames.Uninstall: &uninstallShellBytesOfWin,
	},
}

type Shell struct{}

func (t *Shell) Remove(path string) error {
	osType := runtime.GOOS
	if OsType.Windows == osType {
		return t.RemoveAll(path)
	} else {
		err := os.Remove(path)
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *Shell) RemoveAll(path string) error {
	osType := runtime.GOOS
	if OsType.Windows == osType {
		shellDir, err := config.GetScriptsDir()
		if err != nil {
			return err
		}
		handleExe := filepath.Join(shellDir, "handle.exe")
		// 检查 handle.exe 是否存在
		if _, err := os.Stat(handleExe); err != nil {
			// 不存在则写入
			if err := os.WriteFile(handleExe, hanldeExeBytes, 0755); err != nil {
				return err
			}
		} else {
			// 将脚本写入到临时文件
			tmpfile, err := os.CreateTemp("", "script.*.bat")
			if err != nil {
				return err
			}
			defer os.Remove(tmpfile.Name()) // 清理
			if _, err := tmpfile.Write(rmShellBytesOfWin); err != nil {
				return err
			}
			if err := tmpfile.Close(); err != nil {
				return err
			}
			cmdStr := fmt.Sprintf("cmd /c %s %s %s", tmpfile.Name(), path, handleExe)
			// 运行脚本
			_, err = lib.ExecuteScript(cmdStr)
			if err != nil {
				return err
			}
		}
	} else {
		err := os.RemoveAll(path)
		if err != nil {
			return err
		}
	}
	return nil
}

var Sheller = &Shell{}
