import{R as e,r as t,e as c,j as r,_ as n,u as a,b as o,M as l,B as d,f as i,D as s,g as p,h as u,T as k,i as g,k as m,l as f,m as B,n as _}from"./index.6227d37e.js";import{C as b}from"./index.a7402227.js";function w(e,t){var c=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),c.push.apply(c,r)}return c}function y(e){for(var t=1;t<arguments.length;t++){var c=null!=arguments[t]?arguments[t]:{};t%2?w(Object(c),!0).forEach((function(t){n(e,t,c[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(c)):w(Object(c)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(c,t))}))}return e}function h(e,n){var a=t.exports.useContext(c).prefixCls,o=void 0===a?"arco":a,l=e.spin,d=e.className,i=y(y({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(d?d+" ":"").concat(o,"-icon ").concat(o,"-icon-arrow-right")});return l&&(i.className="".concat(i.className," ").concat(o,"-icon-loading")),delete i.spin,delete i.isIcon,r("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...i,children:r("path",{d:"m27.728 11.27 12.728 12.728-12.728 12.728M5 24h34.295"})})}var x=e.forwardRef(h);x.defaultProps={isIcon:!0},x.displayName="IconArrowRight";var z=x;var O={"en-US":{"menu.main":"Main","menu.main.center":"Control Center","menu.list":"List","menu.list.card":"Card List","main.message.app-not-found.tips":"No available app found, please check it.","cardList.tag.upgraded":"Upgraded","cardList.tag.upgradable":"Upgradable","cardList.tag.upgradable.confirm":"Confirm upgrade?","cardList.upgrade.failed":"Upgrade failed","cardList.upgrade.success":"Upgrade success","center.cardBlock.delete.title":"Delete confirmation","center.cardBlock.delete.content":"Are you sure you want to delete?","center.cardBlock.delete.operation.success":"Delete success","center.cardBlock.delete.operation.fail":"Delete failed","center.cardBlock.delete.operation.disabled":"Delete disabled","center.cardBlock.sync.operation.fail":"Sync failed","center.cardBlock.sync.operation.success":"Sync success","center.cardBlock.sync.operation.noData":"No data","center.cardBlock.sync.operation.offline":"Sync failed, client is offline","center.cardBlock.head.extra.sync":"Sync","center.cardBlock.head.extra.delete":"Delete","center.cardBlock.content.name":"Name","center.cardBlock.content.network":"Network","center.cardBlock.content.uptime":"Uptime","center.cardBlock.state.waiting":"Waiting","center.cardBlock.state.executing":"Executing","center.cardBlock.state.running":"Running","center.cardBlock.state.stopped":"Stopped","center.cardBlock.state.exited":"Exited","center.cardBlock.state.unknown":"Unknown","center.cardBlock.state.offline":"Offline","center.cardBlock.state.online":"Online","center.cardBlock.modal.update.title":"Update confirmation","center.cardBlock.modal.update.content":"Are you sure you want to update?","center.cardBlock.modal.update.operation.success":"Update successfully","center.cardBlock.modal.update.operation.fail":"Update failed","center.controlClient.error":"Control client error","center.controlClient.error.noTarget":"No target found"},"zh-CN":{"menu.main":"主页","menu.main.center":"控制中心","menu.list":"列表页","menu.list.card":"卡片列表","main.message.app-not-found.tips":"未找到可用应用，请检查。","cardList.tag.upgraded":"已升级","cardList.tag.upgradable":"可升级","cardList.tag.upgradable.confirm":"确认升级?","cardList.upgrade.failed":"升级失败","cardList.upgrade.success":"升级成功","center.cardBlock.delete.title":"删除确认","center.cardBlock.delete.content":"确定要删除吗？","center.cardBlock.delete.operation.success":"删除成功","center.cardBlock.delete.operation.fail":"删除失败","center.cardBlock.delete.operation.disabled":"禁止删除","center.cardBlock.sync.operation.fail":"同步失败","center.cardBlock.sync.operation.success":"同步成功","center.cardBlock.sync.operation.noData":"无数据","center.cardBlock.sync.operation.offline":"同步失败，客户端已离线","center.cardBlock.head.extra.sync":"同步","center.cardBlock.head.extra.delete":"删除","center.cardBlock.content.name":"名称","center.cardBlock.content.network":"网络","center.cardBlock.content.uptime":"运行时间","center.cardBlock.state.waiting":"等待中","center.cardBlock.state.executing":"执行中","center.cardBlock.state.running":"运行中","center.cardBlock.state.stopped":"已停止","center.cardBlock.state.exited":"已退出","center.cardBlock.state.unknown":"未知","center.cardBlock.state.offline":"离线","center.cardBlock.state.online":"在线","center.cardBlock.modal.update.title":"更新确认","center.cardBlock.modal.update.content":"确定要更新吗？","center.cardBlock.modal.update.operation.success":"更新成功","center.cardBlock.modal.update.operation.fail":"更新失败","center.controlClient.error":"控制客户端错误","center.controlClient.error.noTarget":"未找到目标"}};var v={container:"_container_lzgbw_1","card-content":"_card-content_lzgbw_12","single-content":"_single-content_lzgbw_16","card-block":"_card-block_lzgbw_19",title:"_title_lzgbw_28",icon:"_icon_lzgbw_35",status:"_status_lzgbw_45",more:"_more_lzgbw_48","title-more":"_title-more_lzgbw_56",time:"_time_lzgbw_59",content:"_content_lzgbw_60",extra:"_extra_lzgbw_76","card-block-skeleton":"_card-block-skeleton_lzgbw_80","add-card":"_add-card_lzgbw_90","add-icon":"_add-icon_lzgbw_94",description:"_description_lzgbw_97","service-card":"_service-card_lzgbw_106","rules-card":"_rules-card_lzgbw_113"};const{Paragraph:S}=k;function C(e){const{card:c={}}=e,n=e.control,k=c.id,[w,y]=t.exports.useState(c.state.toLowerCase()),[h]=t.exports.useState(c.networks),[x]=t.exports.useState(c.uptime),[C]=t.exports.useState(c.version),[j]=t.exports.useState(c.ssl),[N]=t.exports.useState(c.gatewayPort),[L,D]=t.exports.useState(""),[P,U]=t.exports.useState(!1),[$,E]=t.exports.useState(e.loading),I=a(O),M=()=>1===c.isLocal;t.exports.useEffect((()=>{if(E(e.loading),h){const e=JSON.parse(h),t=[];for(const c of e)t.push(c.address);D(t.join(","))}}),[e.loading]);const R=o(l,{children:[r(l.Item,{children:r(d,{type:"text",onClick:async()=>{"offline"!==w?n&&n({to:c.id,type:"sync"}):g.warning(I["center.cardBlock.sync.operation.offline"])},children:I["center.cardBlock.head.extra.sync"]})},"sync"),r(l.Item,{children:r(d,{type:"text",onClick:()=>{M()?g.warning(I["center.cardBlock.delete.operation.disabled"]):m.confirm({title:I["center.cardBlock.delete.title"],content:I["center.cardBlock.delete.content"],onOk:()=>{f(`/api/v1/clients/${c.id}`,{method:"delete"}).then((()=>{g.success(I["center.cardBlock.delete.operation.success"]),U(!0)})).catch((()=>{g.error(I["center.cardBlock.delete.operation.fail"])}))}})},children:I["center.cardBlock.head.extra.delete"]})},"delete")]});return r(b,{hidden:P,bordered:!0,size:"small",style:{marginBottom:"10px",minHeight:"150px",width:"100%"},title:o("div",{className:i(v.title),children:[r(B,"online"===w?{color:"green",icon:r(_,{}),className:v.status,size:"small",children:I[`center.cardBlock.state.${w}`]}:{color:"gray",icon:r(_,{}),className:v.status,size:"small",children:I[`center.cardBlock.state.${w}`]}),o("span",{style:{color:"gray",fontSize:10,marginLeft:"5px"},children:["v",C]})]}),extra:r(s,{droplist:R,trigger:"click",position:"br",children:r(p,{loading:$,icon:r(u,{}),size:"small"})}),actions:[r(p,{type:"outline",loading:$,icon:r(z,{}),size:"small",disabled:M()||!("online"===w),onClick:()=>{if(M())return;const e=`${1===j?"https":"http"}://${L}:${N}`;window.open(e,"_blank")}},"more_"+k)],children:o("div",{className:v.content,children:[r(S,{children:I["center.cardBlock.content.name"]+": "+(c.name&&""!==c.name?c.name:c.id)}),r(S,{children:I["center.cardBlock.content.network"]+": "+L}),r(S,{children:I["center.cardBlock.content.uptime"]+": "+x})]})})}var j=Object.freeze(Object.defineProperty({__proto__:null,default:C},Symbol.toStringTag,{value:"Module"}));export{C,j as c,O as l,v as s};
