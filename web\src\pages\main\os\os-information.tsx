import React from 'react';
import {
  Card,
  Typography,
  // Tag,
  Space,
  Descriptions,
} from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import locale from './locale';

interface OSInformationProps {
  data: {
    os?: {
      type: string;
      version: string;
      arch: string;
    };
    times?: {
      up: string;
    };
  };
}

export default function OSInformation(props: OSInformationProps) {
  const t = useLocale(locale);
  const { data } = props;
  const dataStatus = [
    {
      label: t['os.info.type'],
      value: data.os.type,
    },
    {
      label: t['os.info.version'],
      value: data.os.version,
    },
    {
      label: t['os.info.arch'],
      value: data.os.arch,
    },
    {
      label: t['os.times.up'],
      value: data.times.up,
    }
  ];

  return (
    <Card>
      <Space align="start">
        <Typography.Title
          style={{ marginTop: 0, marginBottom: 16 }}
          heading={6}
        >
          {t['os.info.title']}
        </Typography.Title>
      </Space>
      <Descriptions
        colon=": "
        layout="horizontal"
        data={dataStatus}
        column={2}
      />
    </Card>
  );
}
