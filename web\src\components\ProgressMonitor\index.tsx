import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Progress,
  Typography,
  Space,
  Button,
  Alert,
  Card
} from '@arco-design/web-react';
import {
  IconUpload,
  IconCheckCircle,
  IconCloseCircle,
  IconLoading
} from '@arco-design/web-react/icon';
import api from '@/utils/api';
import styles from './style/index.module.less';

const { Text, Title } = Typography;

interface ProgressMonitorProps {
  visible: boolean;
  taskId: number | null;
  title?: string;
  onComplete?: (success: boolean, message?: string) => void;
  onCancel?: () => void;
  autoClose?: boolean;
  showDetails?: boolean;
}

interface TaskInfo {
  id: number;
  name: string;
  type: string;
  content: string;
  state: string;
  progress: string;
  extract_progress?: string;
  error_log?: string;
  created_at: string;
  updated_at: string;
}

const ProgressMonitor: React.FC<ProgressMonitorProps> = ({
  visible,
  taskId,
  title = '任务进度',
  onComplete,
  onCancel,
  autoClose = true,
  showDetails = true
}) => {
  const [task, setTask] = useState<TaskInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  // 获取任务信息
  const fetchTaskInfo = async () => {
    if (!taskId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await api(`/api/v1/tasks/${taskId}`, {
        method: 'get'
      });
      if (response.data.code === 0) {
        const taskData = response.data.data;
        setTask(taskData);
        
        // 检查任务状态
        if (taskData.state === 'finished') {
          // 任务完成
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          
          if (onComplete) {
            onComplete(true, '任务完成');
          }
          
          if (autoClose) {
            setTimeout(() => {
              if (onCancel) onCancel();
            }, 2000);
          }
        } else if (taskData.state === 'failed') {
          // 任务失败
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          
          setError(taskData.error_log || '任务执行失败');
          
          if (onComplete) {
            onComplete(false, taskData.error_log || '任务执行失败');
          }
        }
      } else {
        throw new Error(response.data.message || '获取任务信息失败');
      }
    } catch (err: any) {
      console.error('获取任务信息失败:', err);
      setError(err.message || '获取任务信息失败');
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    } finally {
      setLoading(false);
    }
  };

  // 开始监控
  const startMonitoring = () => {
    if (!taskId) return;
    
    setStartTime(new Date());
    fetchTaskInfo(); // 立即获取一次
    
    // 设置定时器
    intervalRef.current = setInterval(() => {
      fetchTaskInfo();
    }, 1000); // 每秒更新一次
  };

  // 停止监控
  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // 计算已用时间
  useEffect(() => {
    if (startTime && task && task.state === 'running') {
      const timer = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000));
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [startTime, task]);

  // 监听taskId变化
  useEffect(() => {
    if (visible && taskId) {
      startMonitoring();
    } else {
      stopMonitoring();
      setTask(null);
      setError(null);
      setStartTime(null);
      setElapsedTime(0);
    }
    
    return () => {
      stopMonitoring();
    };
  }, [visible, taskId]);

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取进度值
  const getProgress = () => {
    if (!task) return 0;
    
    // 如果有解压进度，优先显示解压进度
    if (task.extract_progress) {
      return parseFloat(task.extract_progress);
    }
    
    // 否则显示主进度
    return parseFloat(task.progress || '0');
  };

  // 获取进度状态
  const getProgressStatus = () => {
    if (!task) return 'normal';
    
    if (task.state === 'finished') return 'success';
    if (task.state === 'failed') return 'error';
    return 'normal';
  };

  // 获取状态图标
  const getStatusIcon = () => {
    if (!task) return <IconLoading />;
    
    switch (task.state) {
      case 'finished':
        return <IconCheckCircle style={{ color: 'var(--color-success-6)' }} />;
      case 'failed':
        return <IconCloseCircle style={{ color: 'var(--color-danger-6)' }} />;
      case 'running':
        return <IconLoading style={{ color: 'var(--color-primary-6)' }} />;
      default:
        return <IconUpload />;
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    if (!task) return '准备中...';
    
    switch (task.state) {
      case 'finished':
        return '任务完成';
      case 'failed':
        return '任务失败';
      case 'running':
        if (task.extract_progress) {
          return '正在解压...';
        }
        return '正在处理...';
      default:
        return '准备中...';
    }
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onCancel}
      footer={
        task?.state === 'finished' || task?.state === 'failed' ? (
          <Button type="primary" onClick={onCancel}>
            关闭
          </Button>
        ) : (
          <Button onClick={onCancel}>取消</Button>
        )
      }
      width={500}
      maskClosable={false}
    >
      <div className={styles.container}>
        {error && (
          <Alert
            type="error"
            message="错误"
            description={error}
            style={{ marginBottom: 16 }}
          />
        )}
        
        <Card className={styles.progressCard}>
          <div className={styles.statusHeader}>
            <Space>
              {getStatusIcon()}
              <Text strong>{getStatusText()}</Text>
            </Space>
            {task && (
              <Text type="secondary">{getProgress().toFixed(1)}%</Text>
            )}
          </div>
          
          <Progress
            percent={getProgress()}
            status={getProgressStatus()}
            showText={false}
            style={{ marginTop: 12 }}
          />
          
          {startTime && task?.state === 'running' && (
            <div className={styles.timeInfo}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                已用时间: {formatTime(elapsedTime)}
              </Text>
            </div>
          )}
        </Card>
        
        {showDetails && task && (
          <Card className={styles.detailsCard}>
            <Title heading={6}>任务详情</Title>
            <div className={styles.detailItem}>
              <Text type="secondary">任务ID:</Text>
              <Text>{task.id}</Text>
            </div>
            <div className={styles.detailItem}>
              <Text type="secondary">任务类型:</Text>
              <Text>{task.name}</Text>
            </div>
            <div className={styles.detailItem}>
              <Text type="secondary">创建时间:</Text>
              <Text>{task.created_at}</Text>
            </div>
            {task.content && (
              <div className={styles.detailItem}>
                <Text type="secondary">文件路径:</Text>
                <Text style={{ wordBreak: 'break-all', fontSize: 12 }}>
                  {task.content}
                </Text>
              </div>
            )}
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default ProgressMonitor;