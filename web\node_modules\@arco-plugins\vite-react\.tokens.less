

/*********** font ***********/

@font-family: Inter, -apple-system, BlinkMacSystemFont, PingFang SC, Hiragino Sans GB, noto sans, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
@font-size-display-3: 56px;
@font-size-display-2: 48px;
@font-size-display-1: 36px;
@font-size-title-3: 24px;
@font-size-title-2: 20px;
@font-size-title-1: 16px;
@font-size-body-3: 14px;
@font-size-body-2: 13px;
@font-size-body-1: 12px;
@font-size-caption: 12px;
@font-weight-100: 100;
@font-weight-200: 200;
@font-weight-300: 300;
@font-weight-400: 400;
@font-weight-500: 500;
@font-weight-600: 600;
@font-weight-700: 700;
@font-weight-800: 800;
@font-weight-900: 900;
@font-size-body: 14px;


/*********** red ***********/

@red-1: #FFECE8;
@red-2: #FDCDC5;
@red-3: #FBACA3;
@red-4: #F98981;
@red-5: #F76560;
@red-6: #f53f3f;
@red-7: #CB272D;
@red-8: #A1151E;
@red-9: #770813;
@red-10: #4D000A;


/*********** orangered ***********/

@orangered-1: #FFF3E8;
@orangered-2: #FDDDC3;
@orangered-3: #FCC59F;
@orangered-4: #FAAC7B;
@orangered-5: #F99057;
@orangered-6: #f77234;
@orangered-7: #CC5120;
@orangered-8: #A23511;
@orangered-9: #771F06;
@orangered-10: #4D0E00;


/*********** orange ***********/

@orange-1: #FFF7E8;
@orange-2: #FFE4BA;
@orange-3: #FFCF8B;
@orange-4: #FFB65D;
@orange-5: #FF9A2E;
@orange-6: #ff7d00;
@orange-7: #D25F00;
@orange-8: #A64500;
@orange-9: #792E00;
@orange-10: #4D1B00;


/*********** gold ***********/

@gold-1: #FFFCE8;
@gold-2: #FDF4BF;
@gold-3: #FCE996;
@gold-4: #FADC6D;
@gold-5: #F9CC45;
@gold-6: #f7ba1e;
@gold-7: #CC9213;
@gold-8: #A26D0A;
@gold-9: #774B04;
@gold-10: #4D2D00;


/*********** yellow ***********/

@yellow-1: #FEFFE8;
@yellow-2: #FEFEBE;
@yellow-3: #FDFA94;
@yellow-4: #FCF26B;
@yellow-5: #FBE842;
@yellow-6: #fadc19;
@yellow-7: #CFAF0F;
@yellow-8: #A38408;
@yellow-9: #785D03;
@yellow-10: #4D3800;


/*********** lime ***********/

@lime-1: #FCFFE8;
@lime-2: #EDF8BB;
@lime-3: #DCF190;
@lime-4: #C9E968;
@lime-5: #B5E241;
@lime-6: #9fdb1d;
@lime-7: #7EB712;
@lime-8: #5F940A;
@lime-9: #437004;
@lime-10: #2A4D00;


/*********** green ***********/

@green-1: #E8FFEA;
@green-2: #AFF0B5;
@green-3: #7BE188;
@green-4: #4CD263;
@green-5: #23C343;
@green-6: #00b42a;
@green-7: #009A29;
@green-8: #008026;
@green-9: #006622;
@green-10: #004D1C;


/*********** cyan ***********/

@cyan-1: #E8FFFB;
@cyan-2: #B7F4EC;
@cyan-3: #89E9E0;
@cyan-4: #5EDFD6;
@cyan-5: #37D4CF;
@cyan-6: #14c9c9;
@cyan-7: #0DA5AA;
@cyan-8: #07828B;
@cyan-9: #03616C;
@cyan-10: #00424D;


/*********** blue ***********/

@blue-1: #E8F7FF;
@blue-2: #C3E7FE;
@blue-3: #9FD4FD;
@blue-4: #7BC0FC;
@blue-5: #57A9FB;
@blue-6: #3491fa;
@blue-7: #206CCF;
@blue-8: #114BA3;
@blue-9: #063078;
@blue-10: #001A4D;


/*********** arcoblue ***********/

@arcoblue-1: #E8F3FF;
@arcoblue-2: #BEDAFF;
@arcoblue-3: #94BFFF;
@arcoblue-4: #6AA1FF;
@arcoblue-5: #4080FF;
@arcoblue-6: #165dff;
@arcoblue-7: #0E42D2;
@arcoblue-8: #072CA6;
@arcoblue-9: #031A79;
@arcoblue-10: #000D4D;


/*********** purple ***********/

@purple-1: #F5E8FF;
@purple-2: #DDBEF6;
@purple-3: #C396ED;
@purple-4: #A871E3;
@purple-5: #8D4EDA;
@purple-6: #722ed1;
@purple-7: #551DB0;
@purple-8: #3C108F;
@purple-9: #27066E;
@purple-10: #16004D;


/*********** pinkpurple ***********/

@pinkpurple-1: #FFE8FB;
@pinkpurple-2: #F7BAEF;
@pinkpurple-3: #F08EE6;
@pinkpurple-4: #E865DF;
@pinkpurple-5: #E13EDB;
@pinkpurple-6: #d91ad9;
@pinkpurple-7: #B010B6;
@pinkpurple-8: #8A0993;
@pinkpurple-9: #650370;
@pinkpurple-10: #42004D;


/*********** magenta ***********/

@magenta-1: #FFE8F1;
@magenta-2: #FDC2DB;
@magenta-3: #FB9DC7;
@magenta-4: #F979B7;
@magenta-5: #F754A8;
@magenta-6: #f5319d;
@magenta-7: #CB1E83;
@magenta-8: #A11069;
@magenta-9: #77064F;
@magenta-10: #4D0034;


/*********** gray ***********/

@gray-1: #f7f8fa;
@gray-2: #f2f3f5;
@gray-3: #e5e6eb;
@gray-4: #c9cdd4;
@gray-5: #a9aeb8;
@gray-6: #86909c;
@gray-7: #6b7785;
@gray-8: #4e5969;
@gray-9: #272e3b;
@gray-10: #1d2129;


/*********** dark ***********/

@dark-red-1: #4D000A;
@dark-red-2: #770611;
@dark-red-3: #A1161F;
@dark-red-4: #CB2E34;
@dark-red-5: #F54E4E;
@dark-red-6: #F76965;
@dark-red-7: #F98D86;
@dark-red-8: #FBB0A7;
@dark-red-9: #FDD1CA;
@dark-red-10: #FFF0EC;
@dark-orangered-1: #4D0E00;
@dark-orangered-2: #771E05;
@dark-orangered-3: #A23714;
@dark-orangered-4: #CC5729;
@dark-orangered-5: #F77E45;
@dark-orangered-6: #F9925A;
@dark-orangered-7: #FAAD7D;
@dark-orangered-8: #FCC6A1;
@dark-orangered-9: #FDDEC5;
@dark-orangered-10: #FFF4EB;
@dark-orange-1: #4D1B00;
@dark-orange-2: #793004;
@dark-orange-3: #A64B0A;
@dark-orange-4: #D26913;
@dark-orange-5: #FF8D1F;
@dark-orange-6: #FF9626;
@dark-orange-7: #FFB357;
@dark-orange-8: #FFCD87;
@dark-orange-9: #FFE3B8;
@dark-orange-10: #FFF7E8;
@dark-gold-1: #4D2D00;
@dark-gold-2: #774B04;
@dark-gold-3: #A26F0F;
@dark-gold-4: #CC961F;
@dark-gold-5: #F7C034;
@dark-gold-6: #F9CC44;
@dark-gold-7: #FADC6C;
@dark-gold-8: #FCE995;
@dark-gold-9: #FDF4BE;
@dark-gold-10: #FFFCE8;
@dark-yellow-1: #4D3800;
@dark-yellow-2: #785E07;
@dark-yellow-3: #A38614;
@dark-yellow-4: #CFB325;
@dark-yellow-5: #FAE13C;
@dark-yellow-6: #FBE94B;
@dark-yellow-7: #FCF374;
@dark-yellow-8: #FDFA9D;
@dark-yellow-9: #FEFEC6;
@dark-yellow-10: #FEFFF0;
@dark-lime-1: #2A4D00;
@dark-lime-2: #447006;
@dark-lime-3: #629412;
@dark-lime-4: #84B723;
@dark-lime-5: #A8DB39;
@dark-lime-6: #B8E24B;
@dark-lime-7: #CBE970;
@dark-lime-8: #DEF198;
@dark-lime-9: #EEF8C2;
@dark-lime-10: #FDFFEE;
@dark-green-1: #004D1C;
@dark-green-2: #046625;
@dark-green-3: #0A802D;
@dark-green-4: #129A37;
@dark-green-5: #1DB440;
@dark-green-6: #27C346;
@dark-green-7: #50D266;
@dark-green-8: #7EE18B;
@dark-green-9: #B2F0B7;
@dark-green-10: #EBFFEC;
@dark-cyan-1: #00424D;
@dark-cyan-2: #06616C;
@dark-cyan-3: #11838B;
@dark-cyan-4: #1FA6AA;
@dark-cyan-5: #30C9C9;
@dark-cyan-6: #3FD4CF;
@dark-cyan-7: #66DFD7;
@dark-cyan-8: #90E9E1;
@dark-cyan-9: #BEF4ED;
@dark-cyan-10: #F0FFFC;
@dark-blue-1: #001A4D;
@dark-blue-2: #052F78;
@dark-blue-3: #134CA3;
@dark-blue-4: #2971CF;
@dark-blue-5: #469AFA;
@dark-blue-6: #5AAAFB;
@dark-blue-7: #7DC1FC;
@dark-blue-8: #A1D5FD;
@dark-blue-9: #C6E8FE;
@dark-blue-10: #EAF8FF;
@dark-arcoblue-1: #000D4D;
@dark-arcoblue-2: #041B79;
@dark-arcoblue-3: #0E32A6;
@dark-arcoblue-4: #1D4DD2;
@dark-arcoblue-5: #306FFF;
@dark-arcoblue-6: #3C7EFF;
@dark-arcoblue-7: #689FFF;
@dark-arcoblue-8: #93BEFF;
@dark-arcoblue-9: #BEDAFF;
@dark-arcoblue-10: #EAF4FF;
@dark-purple-1: #16004D;
@dark-purple-2: #27066E;
@dark-purple-3: #3E138F;
@dark-purple-4: #5A25B0;
@dark-purple-5: #7B3DD1;
@dark-purple-6: #8E51DA;
@dark-purple-7: #A974E3;
@dark-purple-8: #C59AED;
@dark-purple-9: #DFC2F6;
@dark-purple-10: #F7EDFF;
@dark-pinkpurple-1: #42004D;
@dark-pinkpurple-2: #650370;
@dark-pinkpurple-3: #8A0D93;
@dark-pinkpurple-4: #B01BB6;
@dark-pinkpurple-5: #D92ED9;
@dark-pinkpurple-6: #E13DDB;
@dark-pinkpurple-7: #E866DF;
@dark-pinkpurple-8: #F092E6;
@dark-pinkpurple-9: #F7C1F0;
@dark-pinkpurple-10: #FFF2FD;
@dark-magenta-1: #4D0034;
@dark-magenta-2: #770850;
@dark-magenta-3: #A1176C;
@dark-magenta-4: #CB2B88;
@dark-magenta-5: #F545A6;
@dark-magenta-6: #F756A9;
@dark-magenta-7: #F97AB8;
@dark-magenta-8: #FB9EC8;
@dark-magenta-9: #FDC3DB;
@dark-magenta-10: #FFE8F1;
@dark-gray-1: #17171a;
@dark-gray-2: #2e2e30;
@dark-gray-3: #484849;
@dark-gray-4: #5f5f60;
@dark-gray-5: #78787a;
@dark-gray-6: #929293;
@dark-gray-7: #ababac;
@dark-gray-8: #c5c5c5;
@dark-gray-9: #dfdfdf;
@dark-gray-10: #f6f6f6;
@dark-primary-1: rgb(var(--arcoblue-1));
@dark-primary-2: rgb(var(--arcoblue-2));
@dark-primary-3: rgb(var(--arcoblue-3));
@dark-primary-4: rgb(var(--arcoblue-4));
@dark-primary-5: rgb(var(--arcoblue-5));
@dark-primary-6: rgb(var(--arcoblue-6));
@dark-primary-7: rgb(var(--arcoblue-7));
@dark-primary-8: rgb(var(--arcoblue-8));
@dark-primary-9: rgb(var(--arcoblue-9));
@dark-primary-10: rgb(var(--arcoblue-10));
@dark-success-1: rgb(var(--green-1));
@dark-success-2: rgb(var(--green-2));
@dark-success-3: rgb(var(--green-3));
@dark-success-4: rgb(var(--green-4));
@dark-success-5: rgb(var(--green-5));
@dark-success-6: rgb(var(--green-6));
@dark-success-7: rgb(var(--green-7));
@dark-success-8: rgb(var(--green-8));
@dark-success-9: rgb(var(--green-9));
@dark-success-10: rgb(var(--green-10));
@dark-danger-1: rgb(var(--red-1));
@dark-danger-2: rgb(var(--red-2));
@dark-danger-3: rgb(var(--red-3));
@dark-danger-4: rgb(var(--red-4));
@dark-danger-5: rgb(var(--red-5));
@dark-danger-6: rgb(var(--red-6));
@dark-danger-7: rgb(var(--red-7));
@dark-danger-8: rgb(var(--red-8));
@dark-danger-9: rgb(var(--red-9));
@dark-danger-10: rgb(var(--red-10));
@dark-warning-1: rgb(var(--orange-1));
@dark-warning-2: rgb(var(--orange-2));
@dark-warning-3: rgb(var(--orange-3));
@dark-warning-4: rgb(var(--orange-4));
@dark-warning-5: rgb(var(--orange-5));
@dark-warning-6: rgb(var(--orange-6));
@dark-warning-7: rgb(var(--orange-7));
@dark-warning-8: rgb(var(--orange-8));
@dark-warning-9: rgb(var(--orange-9));
@dark-warning-10: rgb(var(--orange-10));
@dark-link-1: rgb(var(--arcoblue-1));
@dark-link-2: rgb(var(--arcoblue-2));
@dark-link-3: rgb(var(--arcoblue-3));
@dark-link-4: rgb(var(--arcoblue-4));
@dark-link-5: rgb(var(--arcoblue-5));
@dark-link-6: rgb(var(--arcoblue-6));
@dark-link-7: rgb(var(--arcoblue-7));
@dark-link-8: rgb(var(--arcoblue-8));
@dark-link-9: rgb(var(--arcoblue-9));
@dark-link-10: rgb(var(--arcoblue-10));
@dark-color-white: rgba(255, 255, 255, 0.9);
@dark-color-black: #000000;
@dark-mask-color-bg: rgba(23, 23, 26, 0.6);
@dark-color-tooltip-bg: #373739;
@dark-color-spin-layer-bg: rgba(51, 51, 51, 0.6);
@dark-color-menu-dark-hover: var(--color-fill-2);
@dark-color-border: #333335;
@dark-color-bg-1: #17171a;
@dark-color-bg-2: #232324;
@dark-color-bg-3: #2a2a2b;
@dark-color-bg-4: #313132;
@dark-color-bg-5: #373739;
@dark-color-bg-white: #f6f6f6;
@dark-color-text-1: rgba(255, 255, 255, 0.9);
@dark-color-text-2: rgba(255, 255, 255, 0.7);
@dark-color-text-3: rgba(255, 255, 255, 0.5);
@dark-color-text-4: rgba(255, 255, 255, 0.3);
@dark-color-fill-1: rgba(255, 255, 255, 0.04);
@dark-color-fill-2: rgba(255, 255, 255, 0.08);
@dark-color-fill-3: rgba(255, 255, 255, 0.12);
@dark-color-fill-4: rgba(255, 255, 255, 0.16);
@dark-color-primary-light-1: rgba(var(--primary-6), 0.2);
@dark-color-primary-light-2: rgba(var(--primary-6), 0.35);
@dark-color-primary-light-3: rgba(var(--primary-6), 0.5);
@dark-color-primary-light-4: rgba(var(--primary-6), 0.65);
@dark-color-secondary: rgba(var(--gray-9), 0.08);
@dark-color-secondary-hover: rgba(var(--gray-8), 0.16);
@dark-color-secondary-active: rgba(var(--gray-7), 0.24);
@dark-color-secondary-disabled: rgba(var(--gray-9), 0.08);
@dark-color-danger-light-1: rgba(var(--danger-6), 0.2);
@dark-color-danger-light-2: rgba(var(--danger-6), 0.35);
@dark-color-danger-light-3: rgba(var(--danger-6), 0.5);
@dark-color-danger-light-4: rgba(var(--danger-6), 0.65);
@dark-color-success-light-1: rgb(var(--success-6), 0.2);
@dark-color-success-light-2: rgb(var(--success-6), 0.35);
@dark-color-success-light-3: rgb(var(--success-6), 0.5);
@dark-color-success-light-4: rgb(var(--success-6), 0.65);
@dark-color-warning-light-1: rgb(var(--warning-6), 0.2);
@dark-color-warning-light-2: rgb(var(--warning-6), 0.35);
@dark-color-warning-light-3: rgb(var(--warning-6), 0.5);
@dark-color-warning-light-4: rgb(var(--warning-6), 0.65);
@dark-color-link-light-1: rgba(var(--link-6), 0.2);
@dark-color-link-light-2: rgba(var(--link-6), 0.35);
@dark-color-link-light-3: rgba(var(--link-6), 0.5);
@dark-color-link-light-4: rgba(var(--link-6), 0.65);
@dark-color-border-1: #2e2e30;
@dark-color-border-2: #484849;
@dark-color-border-3: #5f5f60;
@dark-color-border-4: #929293;


/*********** primary ***********/

@primary-1: rgb(var(--arcoblue-1));
@primary-2: rgb(var(--arcoblue-2));
@primary-3: rgb(var(--arcoblue-3));
@primary-4: rgb(var(--arcoblue-4));
@primary-5: rgb(var(--arcoblue-5));
@primary-6: rgb(var(--arcoblue-6));
@primary-7: rgb(var(--arcoblue-7));
@primary-8: rgb(var(--arcoblue-8));
@primary-9: rgb(var(--arcoblue-9));
@primary-10: rgb(var(--arcoblue-10));


/*********** success ***********/

@success-1: rgb(var(--green-1));
@success-2: rgb(var(--green-2));
@success-3: rgb(var(--green-3));
@success-4: rgb(var(--green-4));
@success-5: rgb(var(--green-5));
@success-6: rgb(var(--green-6));
@success-7: rgb(var(--green-7));
@success-8: rgb(var(--green-8));
@success-9: rgb(var(--green-9));
@success-10: rgb(var(--green-10));


/*********** danger ***********/

@danger-1: rgb(var(--red-1));
@danger-2: rgb(var(--red-2));
@danger-3: rgb(var(--red-3));
@danger-4: rgb(var(--red-4));
@danger-5: rgb(var(--red-5));
@danger-6: rgb(var(--red-6));
@danger-7: rgb(var(--red-7));
@danger-8: rgb(var(--red-8));
@danger-9: rgb(var(--red-9));
@danger-10: rgb(var(--red-10));


/*********** warning ***********/

@warning-1: rgb(var(--orange-1));
@warning-2: rgb(var(--orange-2));
@warning-3: rgb(var(--orange-3));
@warning-4: rgb(var(--orange-4));
@warning-5: rgb(var(--orange-5));
@warning-6: rgb(var(--orange-6));
@warning-7: rgb(var(--orange-7));
@warning-8: rgb(var(--orange-8));
@warning-9: rgb(var(--orange-9));
@warning-10: rgb(var(--orange-10));


/*********** link ***********/

@link-1: rgb(var(--arcoblue-1));
@link-2: rgb(var(--arcoblue-2));
@link-3: rgb(var(--arcoblue-3));
@link-4: rgb(var(--arcoblue-4));
@link-5: rgb(var(--arcoblue-5));
@link-6: rgb(var(--arcoblue-6));
@link-7: rgb(var(--arcoblue-7));
@link-8: rgb(var(--arcoblue-8));
@link-9: rgb(var(--arcoblue-9));
@link-10: rgb(var(--arcoblue-10));
@link-font-size: 14px;
@link-line-height: 1.5715;
@link-color-bg_hover: var(--color-fill-2);
@link-color-bg_active: var(--color-fill-3);
@link-padding-horizontal: 4px;
@link-color-text: rgb(var(--link-6));
@link-color-text_hover: rgb(var(--link-6));
@link-color-text_active: rgb(var(--link-6));
@link-color-text_disabled: var(--color-link-light-3);
@link-color-text_success: rgb(var(--success-6));
@link-color-text_success_hover: rgb(var(--success-6));
@link-color-text_success_active: rgb(var(--success-6));
@link-color-text_success_disabled: var(--color-success-light-3);
@link-color-text_error: rgb(var(--danger-6));
@link-color-text_error_active: rgb(var(--danger-6));
@link-color-text_error_hover: rgb(var(--danger-6));
@link-color-text_error_disabled: var(--color-danger-light-3);
@link-color-text_warning: rgb(var(--warning-6));
@link-color-text_warning_hover: rgb(var(--warning-6));
@link-color-text_warning_active: rgb(var(--warning-6));
@link-color-text_warning_disabled: var(--color-warning-light-2);
@link-margin-icon-right: 6px;
@link-padding-vertical: 1px;
@link-size-icon: 12px;
@link-border-radius: var(--border-radius-small);
@link-prefix-cls: arco-link;


/*********** border ***********/

@border-none: 0;
@border-1: 1px;
@border-2: 2px;
@border-3: 3px;
@border-4: 4px;
@border-5: 5px;
@border-solid: solid;
@border-dashed: dashed;
@border-dotted: dotted;
@border-radius-none: 0;
@border-radius-small: 2px;
@border-radius-medium: 4px;
@border-radius-large: 8px;
@border-radius-circle: 50%;


/*********** shadow ***********/

@shadow-distance-none: 0;
@shadow-distance-1: 1px;
@shadow-distance-2: 2px;
@shadow-distance-3: 3px;
@shadow-distance-4: 4px;
@shadow-none: none;
@shadow-special: 0 0 1px rgba(0, 0, 0, 0.3);


/*********** size ***********/

@size-none: 0;
@size-1: 4px;
@size-2: 8px;
@size-3: 12px;
@size-4: 16px;
@size-5: 20px;
@size-6: 24px;
@size-7: 28px;
@size-8: 32px;
@size-9: 36px;
@size-10: 40px;
@size-11: 44px;
@size-12: 48px;
@size-13: 52px;
@size-14: 56px;
@size-15: 60px;
@size-16: 64px;
@size-17: 68px;
@size-18: 72px;
@size-19: 76px;
@size-20: 80px;
@size-21: 84px;
@size-22: 88px;
@size-23: 92px;
@size-24: 96px;
@size-25: 100px;
@size-26: 104px;
@size-27: 108px;
@size-28: 112px;
@size-29: 116px;
@size-30: 120px;
@size-31: 124px;
@size-32: 128px;
@size-33: 132px;
@size-34: 136px;
@size-35: 140px;
@size-36: 144px;
@size-37: 148px;
@size-38: 152px;
@size-39: 156px;
@size-40: 160px;
@size-41: 164px;
@size-42: 168px;
@size-43: 172px;
@size-44: 176px;
@size-45: 180px;
@size-46: 184px;
@size-47: 188px;
@size-48: 192px;
@size-49: 196px;
@size-50: 200px;
@size-mini: 24px;
@size-small: 28px;
@size-default: 32px;
@size-large: 36px;


/*********** spacing ***********/

@spacing-none: 0;
@spacing-1: 2px;
@spacing-2: 4px;
@spacing-3: 6px;
@spacing-4: 8px;
@spacing-5: 10px;
@spacing-6: 12px;
@spacing-7: 16px;
@spacing-8: 20px;
@spacing-9: 24px;
@spacing-10: 32px;
@spacing-11: 36px;
@spacing-12: 40px;
@spacing-13: 48px;
@spacing-14: 56px;
@spacing-15: 60px;
@spacing-16: 64px;
@spacing-17: 72px;
@spacing-18: 80px;
@spacing-19: 84px;
@spacing-20: 96px;
@spacing-21: 100px;
@spacing-22: 120px;


/*********** color ***********/

@color-transparent: transparent;
@color-primary-1: rgb(var(--primary-1));
@color-primary-2: rgb(var(--primary-2));
@color-primary-3: rgb(var(--primary-3));
@color-primary-4: rgb(var(--primary-4));
@color-primary-5: rgb(var(--primary-5));
@color-primary-6: rgb(var(--primary-6));
@color-primary-7: rgb(var(--primary-7));
@color-primary-8: rgb(var(--primary-8));
@color-primary-9: rgb(var(--primary-9));
@color-primary-10: rgb(var(--primary-10));
@color-success-1: rgb(var(--success-1));
@color-success-2: rgb(var(--success-2));
@color-success-3: rgb(var(--success-3));
@color-success-4: rgb(var(--success-4));
@color-success-5: rgb(var(--success-5));
@color-success-6: rgb(var(--success-6));
@color-success-7: rgb(var(--success-7));
@color-success-8: rgb(var(--success-8));
@color-success-9: rgb(var(--success-9));
@color-success-10: rgb(var(--success-10));
@color-warning-1: rgb(var(--warning-1));
@color-warning-2: rgb(var(--warning-2));
@color-warning-3: rgb(var(--warning-3));
@color-warning-4: rgb(var(--warning-4));
@color-warning-5: rgb(var(--warning-5));
@color-warning-6: rgb(var(--warning-6));
@color-warning-7: rgb(var(--warning-7));
@color-warning-8: rgb(var(--warning-8));
@color-warning-9: rgb(var(--warning-9));
@color-warning-10: rgb(var(--warning-10));
@color-danger-1: rgb(var(--danger-1));
@color-danger-2: rgb(var(--danger-2));
@color-danger-3: rgb(var(--danger-3));
@color-danger-4: rgb(var(--danger-4));
@color-danger-5: rgb(var(--danger-5));
@color-danger-6: rgb(var(--danger-6));
@color-danger-7: rgb(var(--danger-7));
@color-danger-8: rgb(var(--danger-8));
@color-danger-9: rgb(var(--danger-9));
@color-danger-10: rgb(var(--danger-10));
@color-link-1: rgb(var(--link-1));
@color-link-2: rgb(var(--link-2));
@color-link-3: rgb(var(--link-3));
@color-link-4: rgb(var(--link-4));
@color-link-5: rgb(var(--link-5));
@color-link-6: rgb(var(--link-6));
@color-link-7: rgb(var(--link-7));
@color-link-8: rgb(var(--link-8));
@color-link-9: rgb(var(--link-9));
@color-link-10: rgb(var(--link-10));
@color-white: #ffffff;
@color-black: #000000;
@color-menu-dark-bg: #232324;
@color-menu-light-bg: #ffffff;
@color-spin-layer-bg: rgba(255, 255, 255, 0.6);
@color-menu-dark-hover: rgba(255, 255, 255, 0.04);
@color-tooltip-bg: rgb(var(--gray-10));
@color-border: rgb(var(--gray-3));
@color-bg-popup: var(--color-bg-5);
@color-bg-1: #fff;
@color-bg-2: #fff;
@color-bg-3: #fff;
@color-bg-4: #fff;
@color-bg-5: #fff;
@color-bg-white: #fff;
@color-text-1: var(--color-neutral-10);
@color-text-2: var(--color-neutral-8);
@color-text-3: var(--color-neutral-6);
@color-text-4: var(--color-neutral-4);
@color-fill-1: var(--color-neutral-1);
@color-fill-2: var(--color-neutral-2);
@color-fill-3: var(--color-neutral-3);
@color-fill-4: var(--color-neutral-4);
@color-border-1: var(--color-neutral-2);
@color-border-2: var(--color-neutral-3);
@color-border-3: var(--color-neutral-4);
@color-border-4: var(--color-neutral-6);
@color-primary-light-1: rgb(var(--primary-1));
@color-primary-light-2: rgb(var(--primary-2));
@color-primary-light-3: rgb(var(--primary-3));
@color-primary-light-4: rgb(var(--primary-4));
@color-secondary: var(--color-neutral-2);
@color-secondary-hover: var(--color-neutral-3);
@color-secondary-active: var(--color-neutral-4);
@color-secondary-disabled: var(--color-neutral-1);
@color-danger-light-1: rgb(var(--danger-1));
@color-danger-light-2: rgb(var(--danger-2));
@color-danger-light-3: rgb(var(--danger-3));
@color-danger-light-4: rgb(var(--danger-4));
@color-success-light-1: rgb(var(--success-1));
@color-success-light-2: rgb(var(--success-2));
@color-success-light-3: rgb(var(--success-3));
@color-success-light-4: rgb(var(--success-4));
@color-warning-light-1: rgb(var(--warning-1));
@color-warning-light-2: rgb(var(--warning-2));
@color-warning-light-3: rgb(var(--warning-3));
@color-warning-light-4: rgb(var(--warning-4));
@color-link-light-1: rgb(var(--link-1));
@color-link-light-2: rgb(var(--link-2));
@color-link-light-3: rgb(var(--link-3));
@color-link-light-4: rgb(var(--link-4));


/*********** shadow1 ***********/

@shadow1-center: 0 0 5px rgba(0, 0, 0, 0.1);
@shadow1-up: 0 -2px 5px rgba(0, 0, 0, 0.1);
@shadow1-down: 0 2px 5px rgba(0, 0, 0, 0.1);
@shadow1-left: -2px 0 5px rgba(0, 0, 0, 0.1);
@shadow1-right: 2px 0 5px rgba(0, 0, 0, 0.1);
@shadow1-left-up: -2px -2px 5px rgba(0, 0, 0, 0.1);
@shadow1-left-down: -2px 2px 5px rgba(0, 0, 0, 0.1);
@shadow1-right-up: 2px -2px 5px rgba(0, 0, 0, 0.1);
@shadow1-right-down: 2px 2px 5px rgba(0, 0, 0, 0.1);


/*********** shadow2 ***********/

@shadow2-center: 0 0 10px rgba(0, 0, 0, 0.1);
@shadow2-up: 0 -4px 10px rgba(0, 0, 0, 0.1);
@shadow2-down: 0 4px 10px rgba(0, 0, 0, 0.1);
@shadow2-left: -4px 0 10px rgba(0, 0, 0, 0.1);
@shadow2-right: 4px 0 10px rgba(0, 0, 0, 0.1);
@shadow2-left-up: -4px -4px 10px rgba(0, 0, 0, 0.1);
@shadow2-left-down: -4px 4px 10px rgba(0, 0, 0, 0.1);
@shadow2-right-up: 4px -4px 10px rgba(0, 0, 0, 0.1);
@shadow2-right-down: 4px 4px 10px rgba(0, 0, 0, 0.1);


/*********** shadow3 ***********/

@shadow3-center: 0 0 20px rgba(0, 0, 0, 0.1);
@shadow3-up: 0 -8px 20px rgba(0, 0, 0, 0.1);
@shadow3-down: 0 8px 20px rgba(0, 0, 0, 0.1);
@shadow3-left: -8px 0 20px rgba(0, 0, 0, 0.1);
@shadow3-right: 8px 0 20px rgba(0, 0, 0, 0.1);
@shadow3-left-up: -8px -8px 20px rgba(0, 0, 0, 0.1);
@shadow3-left-down: -8px 8px 20px rgba(0, 0, 0, 0.1);
@shadow3-right-up: 8px -8px 20px rgba(0, 0, 0, 0.1);
@shadow3-right-down: 8px 8px 20px rgba(0, 0, 0, 0.1);


/*********** opacity ***********/

@opacity-none: 0;
@opacity-1: 10%;
@opacity-2: 20%;
@opacity-3: 30%;
@opacity-4: 40%;
@opacity-5: 50%;
@opacity-6: 60%;
@opacity-7: 70%;
@opacity-8: 80%;
@opacity-9: 90%;
@opacity-10: 100%;


/*********** radius ***********/

@radius-none: var(--border-radius-none);
@radius-small: var(--border-radius-small);
@radius-medium: var(--border-radius-medium);
@radius-large: var(--border-radius-large);
@radius-circle: var(--border-radius-circle);


/*********** mask ***********/

@mask-bg-opacity: 60%;
@mask-color-bg: rgba(29, 33, 41, 0.6);


/*********** icon ***********/

@icon-hover-border-radius: var(--border-radius-circle);
@icon-hover-color-bg: var(--color-fill-2);
@icon-hover-size-default-height: 20px;
@icon-hover-size-small-height: 20px;
@icon-hover-size-mini-height: 20px;
@icon-hover-size-large-height: 24px;
@icon-hover-size-huge-height: 24px;
@icon-hover-size-small-icon: 12px;
@icon-hover-size-mini-icon: 12px;
@icon-hover-size-default-icon: 12px;
@icon-hover-size-large-icon: 12px;
@icon-hover-size-huge-icon: 12px;


/*********** prefix ***********/

@prefix: arco;


/*********** arco ***********/

@arco-theme-tag: body;


/*********** code ***********/

@code-family: Consolas, Menlo;


/*********** transition ***********/

@transition-duration-1: 0.1s;
@transition-duration-2: 0.2s;
@transition-duration-3: 0.3s;
@transition-duration-4: 0.4s;
@transition-duration-5: 0.5s;
@transition-duration-loading: 1s;
@transition-timing-function-linear: cubic-bezier(0, 0, 1, 1);
@transition-timing-function-standard: cubic-bezier(0.34, 0.69, 0.1, 1);
@transition-timing-function-overshoot: cubic-bezier(0.3, 1.3, 0.3, 1);
@transition-timing-function-decelerate: cubic-bezier(0.4, 0.8, 0.74, 1);
@transition-timing-function-accelerate: cubic-bezier(0.26, 0, 0.6, 0.2);


/*********** z ***********/

@z-index-popup-base: 1000;
@z-index-affix: 999;
@z-index-popup: 1000;
@z-index-drawer: 1001;
@z-index-modal: 1001;
@z-index-message: 1003;
@z-index-notification: 1003;
@z-index-image-preview: 1001;


/*********** line ***********/

@line-height-base: 1.5715;


/*********** popup ***********/

@popup-box-shadow-base: 0 2px 5px rgba(0, 0, 0, 0.1);
@popup-color-content-text: var(--color-text-2);
@popup-color-content-bg: var(--color-bg-popup);
@popup-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@popup-padding-horizontal: 16px;
@popup-padding-vertical: 12px;
@popup-color-title-text: var(--color-text-1);
@popup-font-title-size: 16px;
@popup-margin-content-top: 4px;
@popup-color-border: var(--color-neutral-3);
@popup-font-size: 14px;
@popup-border-radius: var(--border-radius-medium);


/*********** picker ***********/

@picker-size-mini: 24px;
@picker-size-small: 28px;
@picker-size-default: 32px;
@picker-size-large: 36px;
@picker-size-mini-font-size-text: 12px;
@picker-size-small-font-size-text: 14px;
@picker-size-default-font-size-text: 14px;
@picker-size-large-font-size-text: 14px;
@picker-input-border-radius: var(--border-radius-small);
@picker-color-shadow_focus: var(--color-primary-light-2);
@picker-size-shadow_focus: 0;
@picker-color-shadow_error_focus: var(--color-danger-light-2);
@picker-size-shadow_error_focus: 0;
@picker-color-bg: var(--color-fill-2);
@picker-color-bg_hover: var(--color-fill-3);
@picker-color-bg_focus: var(--color-bg-2);
@picker-color-bg_disabled: var(--color-fill-2);
@picker-color-bg_error: var(--color-danger-light-1);
@picker-color-bg_error_hover: var(--color-danger-light-2);
@picker-color-bg_error_focus: var(--color-bg-2);
@picker-color-border: transparent;
@picker-color-border_hover: transparent;
@picker-color-border_focus: rgb(var(--primary-6));
@picker-color-border_disabled: transparent;
@picker-color-border_error: transparent;
@picker-color-border_error_hover: transparent;
@picker-color-border_error_focus: rgb(var(--danger-6));
@picker-color-placeholder: var(--color-text-3);
@picker-color-placeholder_disabled: var(--color-text-4);
@picker-color-text: var(--color-text-1);
@picker-color-text_disabled: var(--color-text-4);
@picker-color-icon: var(--color-text-2);
@picker-color-icon_disabled: var(--color-text-4);
@picker-color-separator: var(--color-text-3);
@picker-color-separator_disabled: var(--color-text-4);
@picker-range-color-bg-input_focus: var(--color-primary-light-1);
@picker-container-border-radius: var(--border-radius-medium);
@picker-header-color-text: var(--color-text-1);
@picker-header-font-weight-text: 500;
@picker-header-font-size: 14px;
@picker-header-padding-horizontal: 24px;
@picker-header-padding-vertical: 24px;
@picker-panel-border-width: 1px;
@picker-panel-date-width: 265px;
@picker-panel-month-width: 265px;
@picker-panel-year-width: 265px;
@picker-panel-week-width: 298px;
@picker-panel-quarter-width: 265px;
@picker-panel-time-cell-width: 36px;
@picker-panel-time-cell-spacing-horizontal: 4px;
@picker-panel-time-padding-horizontal: 10px;
@picker-panel-cell-padding-vertical: 4px;
@picker-panel-cell-circle-height: 24px;
@picker-panel-row-padding-vertical: 2px;
@picker-color-switch-icon: var(--color-text-2);
@picker-color-bg-switch-icon: var(--color-bg-popup);
@picker-color-bg-switch-icon_hover: var(--color-fill-3);
@picker-cell-font-weight-in-view: 500;
@picker-color-cell-text-in-view: var(--color-text-1);
@picker-cell-font-weight-not-in-view: 500;
@picker-color-cell-text-not-in-view: var(--color-text-4);
@picker-color-bg-circle_selected: rgb(var(--primary-6));
@picker-color-bg-cell-in-range: var(--color-primary-light-1);
@picker-color-bg-cell-disabled: var(--color-fill-1);
@picker-color-text-cell-range-boundary: var(--color-white);
@picker-color-bg-cell-range-boundary: rgb(var(--primary-6));
@picker-color-bg-cell-hover-in-range: var(--color-primary-light-2);
@picker-color-text-cell-hover-range-boundary: var(--color-text-1);
@picker-color-bg-cell-hover-range-boundary: var(--color-primary-light-2);
@picker-color-text-week-list-item: var(--color-text-2);
@picker-font-weight-week-list-item: 500;
@picker-panel-color-border: var(--color-neutral-3);
@picker-panel-color-text-cell_hover: var(--color-text-1);
@picker-panel-color-bg-cell_hover: var(--color-fill-3);
@picker-panel-color-text-cell_selected: var(--color-white);
@picker-panel-color-bg-cell_selected: rgb(var(--primary-6));
@picker-panel-color-current-time-dot: rgb(var(--primary-6));
@picker-panel-color-text-holder: var(--color-text-3);
@picker-panel-color-text-holder_active: var(--color-text-1);
@picker-panel-color-bg-label_hover: var(--color-fill-3);
@picker-panel-border-radius-cell_selected: 24px;
@picker-panel-cell-boundary-border-radius: 24px;
@picker-prefix-cls: arco-picker;


/*********** affix ***********/

@affix-prefix-cls: arco-affix;


/*********** alert ***********/

@alert-border-width: 1px;
@alert-margin-close-icon-left: 8px;
@alert-margin-icon-right: 8px;
@alert-margin-action-right: 8px;
@alert-margin-action-left: 8px;
@alert-border-radius: var(--border-radius-small);
@alert-line-height: 1.5715;
@alert-title-line-height: 1.5;
@alert-title-margin-bottom: 4px;
@alert-padding-horizontal: 16px;
@alert-padding-vertical: 9px;
@alert-padding-horizontal_with_title: 16px;
@alert-padding-vertical_with_title: 16px;
@alert-font-weight-title: 500;
@alert-font-size-text-title: 16px;
@alert-font-size-text-content: 14px;
@alert-font-size-icon: 16px;
@alert-font-size-icon_with_title: 18px;
@alert-font-size-close-icon: 12px;
@alert-color-close-icon: var(--color-text-2);
@alert-color-close-icon_hover: var(--color-text-1);
@alert-info-color-bg: var(--color-primary-light-1);
@alert-info-color-border: transparent;
@alert-info-color-icon: rgb(var(--primary-6));
@alert-info-color-text-title: var(--color-text-1);
@alert-info-color-text-content: var(--color-text-1);
@alert-info-color-text-content_title: var(--color-text-2);
@alert-warning-color-bg: var(--color-warning-light-1);
@alert-warning-color-border: transparent;
@alert-warning-color-icon: rgb(var(--warning-6));
@alert-warning-color-text-title: var(--color-text-1);
@alert-warning-color-text-content: var(--color-text-1);
@alert-warning-color-text-content_title: var(--color-text-2);
@alert-error-color-bg: var(--color-danger-light-1);
@alert-error-color-border: transparent;
@alert-error-color-icon: rgb(var(--danger-6));
@alert-error-color-text-title: var(--color-text-1);
@alert-error-color-text-content: var(--color-text-1);
@alert-error-color-text-content_title: var(--color-text-2);
@alert-success-color-bg: var(--color-success-light-1);
@alert-success-color-border: transparent;
@alert-success-color-icon: rgb(var(--success-6));
@alert-success-color-text-title: var(--color-text-1);
@alert-success-color-text-content: var(--color-text-1);
@alert-success-color-text-content_title: var(--color-text-2);
@alert-prefix-cls: arco-alert;


/*********** anchor ***********/

@anchor-width: 150px;
@anchor-line-width: 2px;
@anchor-line-slider-height: 12px;
@anchor-line-margin-right: 12px;
@anchor-color-bg-line: var(--color-fill-3);
@anchor-color-bg-line_active: rgb(var(--primary-6));
@anchor-border-radius-title-hover: var(--border-radius-small);
@anchor-item-inner-margin-left: 16px;
@anchor-title-padding-horizontal: 8px;
@anchor-title-padding-vertical: 4px;
@anchor-title-margin-bottom: 2px;
@anchor-color-title: var(--color-text-2);
@anchor-color-title_hover: var(--color-text-1);
@anchor-color-title_active: var(--color-text-1);
@anchor-font-weight-title_hover: 500;
@anchor-font-weight-title_active: 500;
@anchor-color-bg-title_hover: var(--color-fill-2);
@anchor-font-size-title: 14px;
@anchor-lineless-color-title_active: rgb(var(--primary-6));
@anchor-lineless-bg-title_active: var(--color-fill-2);
@anchor-lineless-font-weight-title_active: 500;
@anchor-prefix-cls: arco-anchor;


/*********** auto ***********/

@auto-complete-popup-max-height: 200px;
@auto-complete-popup-border-radius: var(--border-radius-medium);
@auto-complete-popup-padding-vertical: 4px;
@auto-complete-popup-font-size: 14px;
@auto-complete-popup-color-border: var(--color-fill-3);
@auto-complete-popup-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@auto-complete-option-height: 36px;
@auto-complete-option-font-weight_selected: 500;
@auto-complete-option-padding-horizontal: 12px;
@auto-complete-option-color-bg_default: var(--color-bg-popup);
@auto-complete-option-color-bg_hover: var(--color-fill-2);
@auto-complete-option-color-bg_selected: var(--color-bg-popup);
@auto-complete-option-color-bg_disabled: var(--color-bg-popup);
@auto-complete-option-color-text_default: var(--color-text-1);
@auto-complete-option-color-text_hover: var(--color-text-1);
@auto-complete-option-color-text_selected: var(--color-text-1);
@auto-complete-option-color-text_disabled: var(--color-text-4);
@auto-complete-prefix-cls: arco-autocomplete;


/*********** select ***********/

@select-prefix-cls: arco-select;
@select-size-mini-height: 24px;
@select-size-small-height: 28px;
@select-size-default-height: 32px;
@select-size-large-height: 36px;
@select-size-mini-font-size: 12px;
@select-size-small-font-size: 14px;
@select-size-default-font-size: 14px;
@select-size-large-font-size: 16px;
@select-signal-size-mini-padding: 8px;
@select-signal-size-small-padding: 12px;
@select-signal-size-default-padding: 12px;
@select-signal-size-large-padding: 16px;
@select-multi-padding: 4px;
@select-size-icon: 12px;
@select-size-icon-bg: 16px;
@select-border-width: 1px;
@select-border-radius: var(--border-radius-small);
@select-color-text_default: var(--color-text-1);
@select-color-text_disabled: var(--color-text-4);
@select-color-text_focused: var(--color-text-1);
@select-color-placeholder_default: var(--color-text-3);
@select-color-placeholder_disabled: var(--color-text-4);
@select-color-placeholder_focused: var(--color-text-3);
@select-color-icon_default: var(--color-text-2);
@select-color-icon_disabled: var(--color-text-4);
@select-color-icon_focused: var(--color-text-2);
@select-color-icon-bg_hover: var(--color-fill-4);
@select-color-bg_default: var(--color-fill-2);
@select-color-bg_default_hover: var(--color-fill-3);
@select-color-bg_default_focus: var(--color-bg-2);
@select-color-bg_error_focus: var(--color-bg-2);
@select-color-bg_error: var(--color-danger-light-1);
@select-color-bg_error_hover: var(--color-danger-light-2);
@select-color-bg_disabled: var(--color-fill-2);
@select-color-bg_disabled_hover: var(--color-fill-2);
@select-color-border_default: transparent;
@select-color-border_default_hover: transparent;
@select-color-border_default_focus: rgb(var(--primary-6));
@select-color-border_error: transparent;
@select-color-border_error_hover: transparent;
@select-color-border_error_focus: rgb(var(--danger-6));
@select-color-border_disabled: transparent;
@select-color-border_disabled_hover: transparent;
@select-shadow-distance_default_focus: 0;
@select-shadow-distance_error_focus: 0;
@select-color-shadow_default_focus: var(--color-primary-light-2);
@select-color-shadow_error_focus: var(--color-danger-light-2);
@select-popup-max-height: 200px;
@select-popup-border-radius: var(--border-radius-medium);
@select-popup-padding-vertical: 4px;
@select-popup-font-size: 14px;
@select-popup-color-bg: var(--color-bg-popup);
@select-popup-color-border: var(--color-fill-3);
@select-popup-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@select-popup-option-height: 36px;
@select-popup-option-font-weight_selected: 500;
@select-signal-popup-option-padding-horizontal: 12px;
@select-multi-popup-option-padding-horizontal: 4px;
@select-popup-option-color-bg_default: var(--color-bg-popup);
@select-popup-option-color-bg_hover: var(--color-fill-2);
@select-popup-option-color-bg_selected: var(--color-bg-popup);
@select-popup-option-color-bg_disabled: var(--color-bg-popup);
@select-popup-option-color-text_default: var(--color-text-1);
@select-popup-option-color-text_hover: var(--color-text-1);
@select-popup-option-color-text_selected: var(--color-text-1);
@select-popup-option-color-text_disabled: var(--color-text-4);
@select-popup-option-color-hightlight-text: var(--color-text-1);
@select-popup-option-font-hightlight-weight: 500;
@select-popup-group-title-height: 20px;
@select-popup-group-title-padding-horizontal: 12px;
@select-popup-group-title-padding-top: 8px;
@select-popup-group-title-font-size: 12px;
@select-popup-group-title-color-text: var(--color-text-3);


/*********** avatar ***********/

@avatar-size-default: 40px;
@avatar-color-text: var(--color-white);
@avatar-color-bg: var(--color-fill-4);
@avatar-color-group-item-border: var(--color-bg-2);
@avatar-group-item-border-width: 2px;
@avatar-group-item-margin-left: -10px;
@avatar-group-popover-item-spacing: 4px;
@avatar-font-weight-text: 500;
@avatar-font-size-text: 20px;
@avatar-circle-border-radius: var(--border-radius-circle);
@avatar-square-border-radius: var(--border-radius-medium);
@avatar-font-size-max-count: 20px;
@avatar-color-max-count-text: var(--color-white);
@avatar-size-trigger-button: 20px;
@avatar-spacing-trigger-button-right: 4px;
@avatar-spacing-trigger-button-bottom: 4px;
@avatar-color-trigger-button-bg: var(--color-neutral-2);
@avatar-color-trigger-button-bg_hover: var(--color-neutral-3);
@avatar-color-trigger-mask-icon: var(--color-white);
@avatar-opacity-trigger-mask-bg: 60%;
@avatar-color-trigger-icon-button: var(--color-fill-4);
@avatar-size-trigger-icon: 12px;
@avatar-border-trigger-button-radius: var(--border-radius-circle);
@avatar-prefix-cls: arco-avatar;


/*********** backtop ***********/

@backtop-margin-bottom: 24px;
@backtop-margin-right: 24px;
@backtop-button-size-width: 40px;
@backtop-button-size-font: 12px;
@backtop-button-size-icon: 14px;
@backtop-button-color-bg: rgb(var(--primary-6));
@backtop-button-color-bg_hover: rgb(var(--primary-5));
@backtop-button-border-radius: var(--border-radius-circle);
@backtop-button-color-text: var(--color-white);
@backtop-prefix-cls: arco-backtop;


/*********** badge ***********/

@badge-size-count-height: 20px;
@badge-padding-count-horizontal: 6px;
@badge-margin-status-text-left: 8px;
@badge-font-count-size: 12px;
@badge-font-status-text-size: 12px;
@badge-color-count-text: var(--color-white);
@badge-color-status-text: var(--color-text-1);
@badge-color-count-bg: rgb(var(--danger-6));
@badge-size-dot-width: 6px;
@badge-color-dot-bg_default: var(--color-fill-4);
@badge-color-dot-bg_processing: rgb(var(--primary-6));
@badge-color-dot-bg_success: rgb(var(--success-6));
@badge-color-dot-bg_warning: rgb(var(--warning-6));
@badge-color-dot-bg_error: rgb(var(--danger-6));
@badge-font-count-weight: 500;
@badge-red-color-dot-bg: rgb(var(--danger-6));
@badge-orangered-color-dot-bg: #f77234;
@badge-orange-color-dot-bg: rgb(var(--orange-6));
@badge-lime-color-dot-bg: rgb(var(--lime-6));
@badge-gold-color-dot-bg: rgb(var(--gold-6));
@badge-green-color-dot-bg: rgb(var(--success-6));
@badge-cyan-color-dot-bg: rgb(var(--cyan-6));
@badge-arcoblue-color-dot-bg: rgb(var(--primary-6));
@badge-pinkpurple-color-dot-bg: rgb(var(--pinkpurple-6));
@badge-purple-color-dot-bg: rgb(var(--purple-6));
@badge-magenta-color-dot-bg: rgb(var(--magenta-6));
@badge-gray-color-dot-bg: rgb(var(--gray-4));
@badge-prefix-cls: arco-badge;


/*********** breadcrumb ***********/

@breadcrumb-color-text: var(--color-text-2);
@breadcrumb-color-text_active: var(--color-text-1);
@breadcrumb-color-link-text: var(--color-text-2);
@breadcrumb-color-separator: var(--color-text-4);
@breadcrumb-color-bg: transparent;
@breadcrumb-color-bg_hover: var(--color-fill-2);
@breadcrumb-margin-separator-horizontal: 4px;
@breadcrumb-margin-dropdown-icon-left: 4px;
@breadcrumb-padding-text-horizontal: 4px;
@breadcrumb-border-text-radius_hover: var(--border-radius-small);
@breadcrumb-size-text-height: 24px;
@breadcrumb-size-dropdown-icon: 12px;
@breadcrumb-size-font-size: 14px;
@breadcrumb-font-weight_active: 500;
@breadcrumb-color-icon: var(--color-text-3);
@breadcrumb-color-link-text_hover: rgb(var(--link-6));
@breadcrumb-color-dropdown-icon: var(--color-text-2);
@breadcrumb-prefix-cls: arco-breadcrumb;


/*********** btn ***********/

@btn-font-weight: 400;
@btn-border-radius: var(--border-radius-small);
@btn-border-width: 1px;
@btn-size-mini-height: 24px;
@btn-size-small-height: 28px;
@btn-size-default-height: 32px;
@btn-size-large-height: 36px;
@btn-size-mini-radius: var(--border-radius-small);
@btn-size-small-radius: var(--border-radius-small);
@btn-size-default-radius: var(--border-radius-small);
@btn-size-large-radius: var(--border-radius-small);
@btn-size-mini-border-width: 1px;
@btn-size-small-border-width: 1px;
@btn-size-default-border-width: 1px;
@btn-size-large-border-width: 1px;
@btn-size-mini-icon-spacing: 4px;
@btn-size-small-icon-spacing: 6px;
@btn-size-default-icon-spacing: 8px;
@btn-size-large-icon-spacing: 8px;
@btn-size-mini-icon-vertical-align: -1px;
@btn-size-small-icon-vertical-align: -2px;
@btn-size-default-icon-vertical-align: -2px;
@btn-size-large-icon-vertical-align: -2px;
@btn-size-mini-padding-horizontal: 11px;
@btn-size-small-padding-horizontal: 15px;
@btn-size-default-padding-horizontal: 15px;
@btn-size-large-padding-horizontal: 19px;
@btn-size-mini-font-size: 12px;
@btn-size-small-font-size: 14px;
@btn-size-default-font-size: 14px;
@btn-size-large-font-size: 14px;
@btn-outline-color-text: rgb(var(--primary-6));
@btn-outline-color-text_disabled: var(--color-primary-light-3);
@btn-outline-color-text_hover: rgb(var(--primary-5));
@btn-outline-color-text_active: rgb(var(--primary-7));
@btn-outline-color-bg: transparent;
@btn-outline-color-bg_disabled: transparent;
@btn-outline-color-bg_hover: transparent;
@btn-outline-color-bg_active: transparent;
@btn-outline-color-border: rgb(var(--primary-6));
@btn-outline-color-border_disabled: var(--color-primary-light-3);
@btn-outline-color-border_hover: rgb(var(--primary-5));
@btn-outline-color-border_active: rgb(var(--primary-7));
@btn-outline-color-text_warning: rgb(var(--warning-6));
@btn-outline-color-text_warning_disabled: var(--color-warning-light-3);
@btn-outline-color-text_warning_hover: rgb(var(--warning-5));
@btn-outline-color-text_warning_active: rgb(var(--warning-7));
@btn-outline-color-bg_warning: transparent;
@btn-outline-color-bg_warning_disabled: transparent;
@btn-outline-color-bg_warning_hover: transparent;
@btn-outline-color-bg_warning_active: transparent;
@btn-outline-color-border_warning: rgb(var(--warning-6));
@btn-outline-color-border_warning_disabled: var(--color-warning-light-3);
@btn-outline-color-border_warning_hover: rgb(var(--warning-5));
@btn-outline-color-border_warning_active: rgb(var(--warning-7));
@btn-outline-color-text_danger: rgb(var(--danger-6));
@btn-outline-color-text_danger_disabled: var(--color-danger-light-3);
@btn-outline-color-text_danger_hover: rgb(var(--danger-5));
@btn-outline-color-text_danger_active: rgb(var(--danger-7));
@btn-outline-color-bg_danger: transparent;
@btn-outline-color-bg_danger_disabled: transparent;
@btn-outline-color-bg_danger_hover: transparent;
@btn-outline-color-bg_danger_active: transparent;
@btn-outline-color-border_danger: rgb(var(--danger-6));
@btn-outline-color-border_danger_disabled: var(--color-danger-light-3);
@btn-outline-color-border_danger_hover: rgb(var(--danger-5));
@btn-outline-color-border_danger_active: rgb(var(--danger-7));
@btn-outline-color-text_success: rgb(var(--success-6));
@btn-outline-color-text_success_disabled: var(--color-success-light-3);
@btn-outline-color-text_success_hover: rgb(var(--success-5));
@btn-outline-color-text_success_active: rgb(var(--success-7));
@btn-outline-color-bg_success: transparent;
@btn-outline-color-bg_success_disabled: transparent;
@btn-outline-color-bg_success_hover: transparent;
@btn-outline-color-bg_success_active: transparent;
@btn-outline-color-border_success: rgb(var(--success-6));
@btn-outline-color-border_success_disabled: var(--color-success-light-3);
@btn-outline-color-border_success_hover: rgb(var(--success-5));
@btn-outline-color-border_success_active: rgb(var(--success-7));
@btn-outline-border-style: solid;
@btn-primary-color-text: #fff;
@btn-primary-color-text_disabled: #fff;
@btn-primary-color-text_hover: #fff;
@btn-primary-color-text_active: #fff;
@btn-primary-color-bg: rgb(var(--primary-6));
@btn-primary-color-bg_disabled: var(--color-primary-light-3);
@btn-primary-color-bg_hover: rgb(var(--primary-5));
@btn-primary-color-bg_active: rgb(var(--primary-7));
@btn-primary-color-border: transparent;
@btn-primary-color-border_disabled: transparent;
@btn-primary-color-border_hover: transparent;
@btn-primary-color-border_active: transparent;
@btn-primary-color-text_warning: #fff;
@btn-primary-color-text_warning_disabled: #fff;
@btn-primary-color-text_warning_hover: #fff;
@btn-primary-color-text_warning_active: #fff;
@btn-primary-color-bg_warning: rgb(var(--warning-6));
@btn-primary-color-bg_warning_disabled: var(--color-warning-light-3);
@btn-primary-color-bg_warning_hover: rgb(var(--warning-5));
@btn-primary-color-bg_warning_active: rgb(var(--warning-7));
@btn-primary-color-border_warning: transparent;
@btn-primary-color-border_warning_disabled: transparent;
@btn-primary-color-border_warning_hover: transparent;
@btn-primary-color-border_warning_active: transparent;
@btn-primary-color-text_danger: #fff;
@btn-primary-color-text_danger_disabled: #fff;
@btn-primary-color-text_danger_hover: #fff;
@btn-primary-color-text_danger_active: #fff;
@btn-primary-color-bg_danger: rgb(var(--danger-6));
@btn-primary-color-bg_danger_disabled: var(--color-danger-light-3);
@btn-primary-color-bg_danger_hover: rgb(var(--danger-5));
@btn-primary-color-bg_danger_active: rgb(var(--danger-7));
@btn-primary-color-border_danger: transparent;
@btn-primary-color-border_danger_disabled: transparent;
@btn-primary-color-border_danger_hover: transparent;
@btn-primary-color-border_danger_active: transparent;
@btn-primary-color-text_success: #fff;
@btn-primary-color-text_success_disabled: #fff;
@btn-primary-color-text_success_hover: #fff;
@btn-primary-color-text_success_active: #fff;
@btn-primary-color-bg_success: rgb(var(--success-6));
@btn-primary-color-bg_success_disabled: var(--color-success-light-3);
@btn-primary-color-bg_success_hover: rgb(var(--success-5));
@btn-primary-color-bg_success_active: rgb(var(--success-7));
@btn-primary-color-border_success: transparent;
@btn-primary-color-border_success_disabled: transparent;
@btn-primary-color-border_success_hover: transparent;
@btn-primary-color-border_success_active: transparent;
@btn-primary-border-style: solid;
@btn-secondary-color-text: var(--color-text-2);
@btn-secondary-color-text_disabled: var(--color-text-4);
@btn-secondary-color-text_hover: var(--color-text-2);
@btn-secondary-color-text_active: var(--color-text-2);
@btn-secondary-color-bg: var(--color-secondary);
@btn-secondary-color-bg_disabled: var(--color-secondary-disabled);
@btn-secondary-color-bg_hover: var(--color-secondary-hover);
@btn-secondary-color-bg_active: var(--color-secondary-active);
@btn-secondary-color-border: transparent;
@btn-secondary-color-border_disabled: transparent;
@btn-secondary-color-border_hover: transparent;
@btn-secondary-color-border_active: transparent;
@btn-secondary-color-text_warning: rgb(var(--warning-6));
@btn-secondary-color-text_warning_disabled: var(--color-warning-light-3);
@btn-secondary-color-text_warning_hover: rgb(var(--warning-6));
@btn-secondary-color-text_warning_active: rgb(var(--warning-6));
@btn-secondary-color-bg_warning: var(--color-warning-light-1);
@btn-secondary-color-bg_warning_disabled: var(--color-warning-light-1);
@btn-secondary-color-bg_warning_hover: var(--color-warning-light-2);
@btn-secondary-color-bg_warning_active: var(--color-warning-light-3);
@btn-secondary-color-border_warning: transparent;
@btn-secondary-color-border_warning_disabled: transparent;
@btn-secondary-color-border_warning_hover: transparent;
@btn-secondary-color-border_warning_active: transparent;
@btn-secondary-color-text_danger: rgb(var(--danger-6));
@btn-secondary-color-text_danger_disabled: var(--color-danger-light-3);
@btn-secondary-color-text_danger_hover: rgb(var(--danger-6));
@btn-secondary-color-text_danger_active: rgb(var(--danger-6));
@btn-secondary-color-bg_danger: var(--color-danger-light-1);
@btn-secondary-color-bg_danger_disabled: var(--color-danger-light-1);
@btn-secondary-color-bg_danger_hover: var(--color-danger-light-2);
@btn-secondary-color-bg_danger_active: var(--color-danger-light-3);
@btn-secondary-color-border_danger: transparent;
@btn-secondary-color-border_danger_disabled: transparent;
@btn-secondary-color-border_danger_hover: transparent;
@btn-secondary-color-border_danger_active: transparent;
@btn-secondary-color-text_success: rgb(var(--success-6));
@btn-secondary-color-text_success_disabled: var(--color-success-light-3);
@btn-secondary-color-text_success_hover: rgb(var(--success-6));
@btn-secondary-color-text_success_active: rgb(var(--success-6));
@btn-secondary-color-bg_success: var(--color-success-light-1);
@btn-secondary-color-bg_success_disabled: var(--color-success-light-1);
@btn-secondary-color-bg_success_hover: var(--color-success-light-2);
@btn-secondary-color-bg_success_active: var(--color-success-light-3);
@btn-secondary-color-border_success: transparent;
@btn-secondary-color-border_success_disabled: transparent;
@btn-secondary-color-border_success_hover: transparent;
@btn-secondary-color-border_success_active: transparent;
@btn-secondary-border-style: solid;
@btn-dashed-color-text: var(--color-text-2);
@btn-dashed-color-text_disabled: var(--color-text-4);
@btn-dashed-color-text_hover: var(--color-text-2);
@btn-dashed-color-text_active: var(--color-text-2);
@btn-dashed-color-bg: var(--color-fill-2);
@btn-dashed-color-bg_disabled: var(--color-fill-2);
@btn-dashed-color-bg_hover: var(--color-fill-3);
@btn-dashed-color-bg_active: var(--color-fill-4);
@btn-dashed-color-border: var(--color-neutral-3);
@btn-dashed-color-border_disabled: var(--color-neutral-3);
@btn-dashed-color-border_hover: var(--color-neutral-4);
@btn-dashed-color-border_active: var(--color-neutral-5);
@btn-dashed-color-text_warning: rgb(var(--warning-6));
@btn-dashed-color-text_warning_disabled: var(--color-warning-light-3);
@btn-dashed-color-text_warning_hover: rgb(var(--warning-6));
@btn-dashed-color-text_warning_active: rgb(var(--warning-6));
@btn-dashed-color-bg_warning: var(--color-warning-light-1);
@btn-dashed-color-bg_warning_disabled: var(--color-warning-light-1);
@btn-dashed-color-bg_warning_hover: var(--color-warning-light-2);
@btn-dashed-color-bg_warning_active: var(--color-warning-light-3);
@btn-dashed-color-border_warning: var(--color-warning-light-2);
@btn-dashed-color-border_warning_disabled: var(--color-warning-light-2);
@btn-dashed-color-border_warning_hover: var(--color-warning-light-3);
@btn-dashed-color-border_warning_active: var(--color-warning-light-4);
@btn-dashed-color-text_danger: rgb(var(--danger-6));
@btn-dashed-color-text_danger_disabled: var(--color-danger-light-3);
@btn-dashed-color-text_danger_hover: rgb(var(--danger-6));
@btn-dashed-color-text_danger_active: rgb(var(--danger-6));
@btn-dashed-color-bg_danger: var(--color-danger-light-1);
@btn-dashed-color-bg_danger_disabled: var(--color-danger-light-1);
@btn-dashed-color-bg_danger_hover: var(--color-danger-light-2);
@btn-dashed-color-bg_danger_active: var(--color-danger-light-3);
@btn-dashed-color-border_danger: var(--color-danger-light-2);
@btn-dashed-color-border_danger_disabled: var(--color-danger-light-2);
@btn-dashed-color-border_danger_hover: var(--color-danger-light-3);
@btn-dashed-color-border_danger_active: var(--color-danger-light-4);
@btn-dashed-color-text_success: rgb(var(--success-6));
@btn-dashed-color-text_success_disabled: var(--color-success-light-3);
@btn-dashed-color-text_success_hover: rgb(var(--success-6));
@btn-dashed-color-text_success_active: rgb(var(--success-6));
@btn-dashed-color-bg_success: var(--color-success-light-1);
@btn-dashed-color-bg_success_disabled: var(--color-success-light-1);
@btn-dashed-color-bg_success_hover: var(--color-success-light-2);
@btn-dashed-color-bg_success_active: var(--color-success-light-3);
@btn-dashed-color-border_success: var(--color-success-light-2);
@btn-dashed-color-border_success_disabled: var(--color-success-light-2);
@btn-dashed-color-border_success_hover: var(--color-success-light-3);
@btn-dashed-color-border_success_active: var(--color-success-light-4);
@btn-dashed-border-style: dashed;
@btn-text-color-text: rgb(var(--primary-6));
@btn-text-color-text_disabled: var(--color-primary-light-3);
@btn-text-color-text_hover: rgb(var(--primary-6));
@btn-text-color-text_active: rgb(var(--primary-6));
@btn-text-color-bg: transparent;
@btn-text-color-bg_disabled: transparent;
@btn-text-color-bg_hover: var(--color-fill-2);
@btn-text-color-bg_active: var(--color-fill-3);
@btn-text-color-border: transparent;
@btn-text-color-border_disabled: transparent;
@btn-text-color-border_hover: transparent;
@btn-text-color-border_active: transparent;
@btn-text-color-text_warning: rgb(var(--warning-6));
@btn-text-color-text_warning_disabled: var(--color-warning-light-3);
@btn-text-color-text_warning_hover: rgb(var(--warning-6));
@btn-text-color-text_warning_active: rgb(var(--warning-6));
@btn-text-color-bg_warning: transparent;
@btn-text-color-bg_warning_disabled: transparent;
@btn-text-color-bg_warning_hover: var(--color-fill-2);
@btn-text-color-bg_warning_active: var(--color-fill-3);
@btn-text-color-border_warning: transparent;
@btn-text-color-border_warning_disabled: transparent;
@btn-text-color-border_warning_hover: transparent;
@btn-text-color-border_warning_active: transparent;
@btn-text-color-text_danger: rgb(var(--danger-6));
@btn-text-color-text_danger_disabled: var(--color-danger-light-3);
@btn-text-color-text_danger_hover: rgb(var(--danger-6));
@btn-text-color-text_danger_active: rgb(var(--danger-6));
@btn-text-color-bg_danger: transparent;
@btn-text-color-bg_danger_disabled: transparent;
@btn-text-color-bg_danger_hover: var(--color-fill-2);
@btn-text-color-bg_danger_active: var(--color-fill-3);
@btn-text-color-border_danger: transparent;
@btn-text-color-border_danger_disabled: transparent;
@btn-text-color-border_danger_hover: transparent;
@btn-text-color-border_danger_active: transparent;
@btn-text-color-text_success: rgb(var(--success-6));
@btn-text-color-text_success_disabled: var(--color-success-light-3);
@btn-text-color-text_success_hover: rgb(var(--success-6));
@btn-text-color-text_success_active: rgb(var(--success-6));
@btn-text-color-bg_success: transparent;
@btn-text-color-bg_success_disabled: transparent;
@btn-text-color-bg_success_hover: var(--color-fill-2);
@btn-text-color-bg_success_active: var(--color-fill-3);
@btn-text-color-border_success: transparent;
@btn-text-color-border_success_disabled: transparent;
@btn-text-color-border_success_hover: transparent;
@btn-text-color-border_success_active: transparent;
@btn-text-border-style: solid;
@btn-prefix-cls: arco-btn;


/*********** calendar ***********/

@calendar-color-border: var(--color-neutral-3);
@calendar-header-padding-horizontal: 24px;
@calendar-header-padding-vertical: 24px;
@calendar-panel-date-cell-padding-vertical: 4px;
@calendar-panel-date-cell-circle-height: 24px;
@calendar-panel-year-cell-padding-vertical: 4px;
@calendar-panel-year-cell-circle-height: 24px;
@calendar-color-switch-icon: var(--color-text-2);
@calendar-color-bg-switch-icon: var(--color-bg-5);
@calendar-color-bg-switch-icon_hover: var(--color-fill-3);
@calendar-color-text-title: var(--color-text-1);
@calendar-color-cell-text-in-view: var(--color-text-1);
@calendar-color-cell-text-not-in-view: var(--color-text-4);
@calendar-color-bg-circle_selected: rgb(var(--primary-6));
@calendar-color-bg-cell-in-range: var(--color-primary-light-1);
@calendar-color-bg-cell-disabled: var(--color-fill-1);
@calendar-color-text-cell-range-boundary: var(--color-white);
@calendar-color-bg-cell-range-boundary: rgb(var(--primary-6));
@calendar-color-bg-cell-hover-in-range: var(--color-primary-light-1);
@calendar-color-text-cell-hover-range-boundary: var(--color-text-1);
@calendar-color-bg-cell-hover-range-boundary: var(--color-primary-light-2);
@calendar-panel-color-text-cell_hover: rgb(var(--primary-6));
@calendar-panel-color-bg-cell_hover: var(--color-primary-light-1);
@calendar-panel-color-text-cell_selected: var(--color-white);
@calendar-panel-color-bg-cell_selected: rgb(var(--primary-6));
@calendar-panel-color-current-time-dot: rgb(var(--primary-6));
@calendar-panel-cell-boundary-border-radius: 16px;
@calendar-prefix-cls: arco-calendar;


/*********** card ***********/

@card-size-small-height-title: 40px;
@card-size-small-font-size-title: 16px;
@card-size-small-font-size-title-extra: 14px;
@card-size-small-font-size: 14px;
@card-size-small-padding-horizontal-title: 16px;
@card-size-small-padding-horizontal-body: 16px;
@card-size-small-padding-vertical-body: 16px;
@card-size-default-height-title: 46px;
@card-size-default-font-size-title: 16px;
@card-size-default-font-size-title-extra: 14px;
@card-size-default-font-size: 14px;
@card-size-default-padding-horizontal-title: 20px;
@card-size-default-padding-horizontal-body: 20px;
@card-size-default-padding-vertical-body: 20px;
@card-line-height: 1.5715;
@card-font-weight-title: 500;
@card-margin-top-meta-footer: 20px;
@card-margin-top-meta-description: 4px;
@card-margin-right-action-item: 12px;
@card-color-bg: var(--color-bg-2);
@card-color-border: var(--color-neutral-3);
@card-color-title: var(--color-text-1);
@card-color-title-extra: rgb(var(--primary-6));
@card-color-body: var(--color-text-2);
@card-color-action: var(--color-text-2);
@card-color-action_hover: rgb(var(--primary-6));
@card-border-width: 1px;
@card-border-width-title-bottom: 0;
@card-border-radius: 4px;
@card-border-radius-no-border: 4px;
@card-prefix-cls: arco-card;


/*********** carousel ***********/

@carousel-content-border-radius: 0;
@carousel-arrow-position: 12px;
@carousel-arrow-size: 24px;
@carousel-arrow-font-size: 14px;
@carousel-arrow-color-icon: var(--color-white);
@carousel-arrow-color-bg: rgba(255, 255, 255, 0.3);
@carousel-arrow-color-bg_hover: rgba(255, 255, 255, 0.5);
@carousel-indicator-size-wrapper: 48px;
@carousel-indicator-color-bg-wrapper: rgba(0, 0, 0, 0.15);
@carousel-indicator-dot-size: 6px;
@carousel-indicator-line-size-width: 12px;
@carousel-indicator-line-size-height: 4px;
@carousel-indicator-slider-size-width: 48px;
@carousel-indicator-slider-size-height: 4px;
@carousel-indicator-position: 12px;
@carousel-indicator-gap: 8px;
@carousel-indicator-border-radius: var(--border-radius-medium);
@carousel-indicator-color_default: rgba(255, 255, 255, 0.3);
@carousel-indicator-color_active: var(--color-white);
@carousel-indicator-outer-border-radius: 20px;
@carousel-indicator-outer-padding: 4px;
@carousel-indicator-outer-color_default: rgba(var(--gray-4), 0.5);
@carousel-indicator-outer-color_active: var(--color-fill-4);
@carousel-indicator-outer-color-bg: transparent;
@carousel-prefix-cls: arco-carousel;


/*********** cascader ***********/

@cascader-size-item-height: 36px;
@cascader-font-item-size: 14px;
@cascader-margin-item-icon-left: 12px;
@cascader-color-item-text: var(--color-text-1);
@cascader-color-item-icon: var(--color-text-2);
@cascader-padding-item-left: 12px;
@cascader-padding-item-right: 10px;
@cascader-size-item-icon: 12px;
@cascader-color-item-text_hover: var(--color-text-1);
@cascader-color-item-text_active: var(--color-text-1);
@cascader-color-item-text_disabled: var(--color-text-4);
@cascader-color-item-text_disabled_active: var(--color-text-4);
@cascader-font-item-weight_active: 500;
@cascader-color-item-bg_active: var(--color-fill-2);
@cascader-color-item-bg_hover: var(--color-fill-2);
@cascader-color-item-bg_disabled: var(--color-fill-2);
@cascader-color-item-bg_disabled_active: var(--color-fill-2);
@cascader-color-checkbox-bg_hover: var(--color-fill-3);
@cascader-margin-checkbox-right: 8px;
@cascader-prefix-cls: arco-cascader;


/*********** input ***********/

@input-tag-size-mini-height: 24px;
@input-tag-size-small-height: 28px;
@input-tag-size-default-height: 32px;
@input-tag-size-large-height: 36px;
@input-tag-size-mini-tag-height: 20px;
@input-tag-size-small-tag-height: 20px;
@input-tag-size-default-tag-height: 24px;
@input-tag-size-large-tag-height: 28px;
@input-tag-size-mini-font-size: 12px;
@input-tag-size-small-font-size: 14px;
@input-tag-size-default-font-size: 14px;
@input-tag-size-large-font-size: 16px;
@input-tag-size-mini-padding_no_tag: 8px;
@input-tag-size-small-padding_no_tag: 12px;
@input-tag-size-default-padding_no_tag: 12px;
@input-tag-size-large-padding_no_tag: 16px;
@input-tag-color-text_default: var(--color-text-1);
@input-tag-color-text_error: var(--color-text-1);
@input-tag-color-text_disabled: var(--color-text-4);
@input-tag-color-placeholder: var(--color-text-3);
@input-tag-color-icon-clear: var(--color-text-2);
@input-tag-color-icon-clear-bg_hover: var(--color-fill-4);
@input-tag-color-border_default: transparent;
@input-tag-color-border_default_hover: transparent;
@input-tag-color-border_default_focus: rgb(var(--primary-6));
@input-tag-color-border_error: transparent;
@input-tag-color-border_error_hover: transparent;
@input-tag-color-border_error_focus: rgb(var(--danger-6));
@input-tag-color-border_disabled: transparent;
@input-tag-color-border_disabled_hover: transparent;
@input-tag-color-border_disabled_focus: transparent;
@input-tag-color-bg_default: var(--color-fill-2);
@input-tag-color-bg_default_hover: var(--color-fill-3);
@input-tag-color-bg_default_focus: var(--color-bg-2);
@input-tag-color-bg_error: rgb(var(--danger-1));
@input-tag-color-bg_error_hover: rgb(var(--danger-2));
@input-tag-color-bg_error_focus: var(--color-bg-2);
@input-tag-color-bg_disabled: var(--color-fill-2);
@input-tag-color-bg_disabled_hover: var(--color-fill-2);
@input-tag-color-shadow_default_focus: rgb(var(--primary-2));
@input-tag-color-shadow_error_focus: rgb(var(--danger-2));
@input-tag-size-shadow_error_focus: 0;
@input-tag-size-shadow_default_focus: 0;
@input-tag-tag-margin-right: 4px;
@input-tag-tag-margin-vertical: 2px;
@input-tag-padding-horizontal: 4px;
@input-tag-border-radius: var(--border-radius-small);
@input-tag-border-width: 1px;
@input-tag-size-icon-clear: 12px;
@input-tag-size-icon-clear_hover: 20px;
@input-tag-tag-font-size: 12px;
@input-tag-tag-color-bg: var(--color-bg-2);
@input-tag-tag-color-bg_focus: var(--color-fill-2);
@input-tag-tag-color-bg_disabled: var(--color-fill-2);
@input-tag-tag-color-border: var(--color-fill-3);
@input-tag-tag-color-border_disabled: var(--color-fill-3);
@input-tag-tag-color-border_focus: var(--color-fill-2);
@input-tag-tag-remove-icon-color-bg: var(--color-fill-2);
@input-tag-tag-remove-icon-color-bg_focus: var(--color-fill-3);
@input-tag-prefix-cls: arco-input-tag;
@input-color-bg: var(--color-fill-2);
@input-color-bg_hover: var(--color-fill-3);
@input-color-bg_focus: var(--color-bg-2);
@input-color-bg_disabled: var(--color-fill-2);
@input-color-addon-bg: var(--color-fill-2);
@input-color-addon-border: var(--color-neutral-3);
@input-border-addon-separator-width: 1px;
@input-color-border_focus: rgb(var(--primary-6));
@input-color-shadow_focus: var(--color-primary-light-2);
@input-size-shadow_focus: 0;
@input-color-addon-border_default: transparent;
@input-color-text: var(--color-text-1);
@input-color-placeholder-text: var(--color-text-3);
@input-color-text_disabled: var(--color-text-4);
@input-color-addon-text: var(--color-text-1);
@input-color-bg_error: var(--color-danger-light-1);
@input-color-bg_error_hover: var(--color-danger-light-2);
@input-color-bg_error_focus: var(--color-bg-2);
@input-color-border_error_focus: rgb(var(--danger-6));
@input-color-shadow_error_focus: var(--color-danger-light-2);
@input-size-shadow_error_focus: 0;
@input-border-radius: var(--border-radius-small);
@input-size-default-height: 32px;
@input-size-mini-height: 24px;
@input-size-small-height: 28px;
@input-size-large-height: 36px;
@input-border-width: 1px;
@input-color-border: transparent;
@input-color-border_disabled: transparent;
@input-color-border_hover: transparent;
@input-color-border_error: transparent;
@input-color-border_error_hover: transparent;
@input-size-default-font-size: 14px;
@input-size-small-font-size: 14px;
@input-size-large-font-size: 14px;
@input-size-mini-font-size: 12px;
@input-font-tip-size: 12px;
@input-size-mini-icon-suffix-size: 12px;
@input-size-small-icon-suffix-size: 14px;
@input-size-default-icon-suffix-size: 14px;
@input-size-large-icon-suffix-size: 14px;
@input-size-mini-icon-addon-size: 12px;
@input-size-small-icon-addon-size: 14px;
@input-size-default-icon-addon-size: 14px;
@input-size-large-icon-addon-size: 14px;
@input-size-icon-clear: 12px;
@input-color-prefix-text: var(--color-text-2);
@input-color-suffix-text: var(--color-text-2);
@input-color-tip-text: var(--color-text-3);
@input-color-icon-clear: var(--color-text-2);
@input-color-icon-clear-bg_hover: var(--color-fill-4);
@input-padding-horizontal: 12px;
@input-size-mini-padding-horizontal: 8px;
@input-size-small-padding-horizontal: 12px;
@input-size-large-padding-horizontal: 16px;
@input-spacing-clear-icon-right: 8px;
@input-padding-word-limit-left: 8px;
@input-group-border-radius_compact: var(--border-radius-small);
@input-group-border-separator-width: 1px;
@input-group-color-separator-border: var(--color-neutral-3);
@input-prefix-cls: arco-input;
@input-number-border-radius: var(--border-radius-small);
@input-number-step-layer-border-radius: 1px;
@input-number-size-mini-step-button-width: 24px;
@input-number-size-small-step-button-width: 28px;
@input-number-size-default-step-button-width: 32px;
@input-number-size-large-step-button-width: 36px;
@input-number-step-button-color: var(--color-text-2);
@input-number-step-button-color_disabled: var(--color-text-4);
@input-number-step-button-color-border: var(--color-neutral-3);
@input-number-step-button-color-bg_default: var(--color-fill-2);
@input-number-step-button-color-bg_default_hover: var(--color-fill-3);
@input-number-step-button-color-bg_default_active: var(--color-fill-4);
@input-number-step-button-color-bg_disabled: var(--color-fill-2);
@input-number-step-button-color-bg_disabled_hover: var(--color-fill-2);
@input-number-step-button-color-bg_disabled_active: var(--color-fill-2);
@input-number-color-illegal_value: rgb(var(--danger-6));
@input-number-prefix-cls: arco-input-number;


/*********** checkbox ***********/

@checkbox-prefix-cls: arco-checkbox;
@checkbox-mask-border-width: 2px;
@checkbox-mask-border-style: solid;
@checkbox-mask-border-radius: var(--border-radius-small);
@checkbox-mask-height: 14px;
@checkbox-mask-bg-height: 24px;
@checkbox-mask-bg-color-bg: var(--color-fill-2);
@checkbox-mask-color-bg: var(--color-bg-2);
@checkbox-mask-color-bg_checked: rgb(var(--primary-6));
@checkbox-mask-color-bg_disabled: var(--color-fill-2);
@checkbox-mask-color-bg_checked_disabled: var(--color-primary-light-3);
@checkbox-mask-color-border: var(--color-fill-3);
@checkbox-mask-color-border_hover: var(--color-fill-4);
@checkbox-mask-color-border_checked: transparent;
@checkbox-mask-color-border_checked_disabled: transparent;
@checkbox-mask-color-border_disabled: var(--color-fill-3);
@checkbox-color-text: var(--color-text-1);
@checkbox-color-text_disabled: var(--color-text-4);
@checkbox-group-spacing: 16px;
@checkbox-text-mask-spacing: 8px;
@checkbox-text-font-size: 14px;
@checkbox-group-size-line-height_vertical: 32px;
@checkbox-size-check-icon: 8px;
@checkbox-color-check-icon: var(--color-white);
@checkbox-color-check-icon_disabled: var(--color-fill-3);
@checkbox-color-indeterminate-icon-width: 6px;
@checkbox-color-indeterminate-icon-height: 2px;
@checkbox-color-indeterminate-icon: var(--color-white);


/*********** collapse ***********/

@collapse-border-width: 1px;
@collapse-border-radius: var(--border-radius-medium);
@collapse-color-border: var(--color-neutral-3);
@collapse-line-height: 1.5715;
@collapse-title-line-height: 24px;
@collapse-title-border-width: 1px;
@collapse-title-color-border: var(--color-neutral-3);
@collapse-title-font-size: 14px;
@collapse-title-padding-horizontal: 13px;
@collapse-title-padding-vertical: 8px;
@collapse-title-color-bg: var(--color-bg-2);
@collapse-title-color-bg_active: var(--color-bg-2);
@collapse-title-color-bg_disabled: var(--color-bg-2);
@collapse-title-color-text: var(--color-text-1);
@collapse-title-color-text_disabled: var(--color-text-4);
@collapse-title-font-weight_active: 500;
@collapse-content-color-text: var(--color-text-1);
@collapse-content-color-text_disabled: var(--color-text-1);
@collapse-content-font-size: 14px;
@collapse-content-padding-vertical: 8px;
@collapse-content-color-bg: var(--color-fill-1);
@collapse-expand-icon-size: 14px;
@collapse-expand-icon-size-bg: 16px;
@collapse-expand-icon-color-bg: var(--color-fill-2);
@collapse-expand-icon-spacing-text: 5px;
@collapse-color-expand-icon: var(--color-neutral-7);
@collapse-item-color-border: var(--color-neutral-3);
@collapse-item-border-width: 1px;
@collapse-prefix-cls: arco-collapse;


/*********** comment ***********/

@comment-color-author-text: var(--color-text-2);
@comment-color-datetime-text: var(--color-text-3);
@comment-color-content-text: var(--color-text-1);
@comment-color-actions-text: var(--color-text-2);
@comment-font-size: 14px;
@comment-font-action-size: 14px;
@comment-font-author-size: 14px;
@comment-font-datetime-size: 12px;
@comment-margin-avatar-right: 12px;
@comment-margin-author-right: 8px;
@comment-margin-actions-top: 8px;
@comment-margin-bottom: 20px;
@comment-margin-actions-right: 8px;
@comment-size-avatar-width: 32px;
@comment-prefix-cls: arco-comment;


/*********** timepicker ***********/

@timepicker-wrapper-border-radius: var(--border-radius-medium);
@timepicker-column-width: 64px;
@timepicker-column-height: 224px;
@timepicker-cell-height: 24px;
@timepicker-cell-spacing: 8px;
@timepicker-cell-font-size: 14px;
@timepicker-color-border: var(--color-neutral-3);
@timepicker-color-cell-border: var(--color-neutral-3);
@timepicker-color-text-cell: var(--color-text-1);
@timepicker-color-bg-cell_hover: var(--color-fill-2);
@timepicker-color-bg-cell_active: var(--color-fill-2);
@timepicker-color-text-cell_disabled: var(--color-text-4);
@timepicker-font-weight-cell: 500;
@timepicker-font-weight-cell_active: 500;
@timepicker-color-extra-text: var(--color-text-1);
@timepicker-font-extra-size: 12px;
@timepicker-extra-padding-horizontal: 8px;
@timepicker-extra-padding-vertical: 8px;
@timepicker-footer-padding-horizontal: 8px;
@timepicker-footer-padding-vertical: 8px;


/*********** date ***********/

@date-panel-prefix-cls: arco-panel-date;
@date-picker-prefix-cls: arco-datepicker;


/*********** time ***********/

@time-picker-prefix-cls: arco-timepicker;


/*********** datepicker ***********/

@datepicker-timepicker-height: 276px;


/*********** month ***********/

@month-panel-prefix-cls: arco-panel-month;


/*********** quarter ***********/

@quarter-panel-prefix-cls: arco-panel-quarter;


/*********** year ***********/

@year-panel-prefix-cls: arco-panel-year;


/*********** week ***********/

@week-panel-prefix-cls: arco-panel-week;


/*********** range ***********/

@range-picker-prefix-cls: arco-picker-range;


/*********** descriptions ***********/

@descriptions-border-width: 1px;
@descriptions-border-style: solid;
@descriptions-color-border: var(--color-neutral-3);
@descriptions-border-radius: var(--border-radius-medium);
@descriptions-font-size-title: 16px;
@descriptions-size-mini-title-margin-bottom: 6px;
@descriptions-size-small-title-margin-bottom: 8px;
@descriptions-size-medium-title-margin-bottom: 12px;
@descriptions-size-default-title-margin-bottom: 12px;
@descriptions-size-large-title-margin-bottom: 20px;
@descriptions-size-mini-font-size-text: 12px;
@descriptions-size-small-font-size-text: 14px;
@descriptions-size-medium-font-size-text: 14px;
@descriptions-size-default-font-size-text: 14px;
@descriptions-size-large-font-size-text: 14px;
@descriptions-color-title: var(--color-neutral-10);
@descriptions-color-text-label: var(--color-text-3);
@descriptions-color-text-value: var(--color-text-1);
@descriptions-font-weight-title: 500;
@descriptions-font-weight-text-label: 400;
@descriptions-font-weight-text-value: 400;
@descriptions-border-color-bg-label: var(--color-fill-1);
@descriptions-item-size-mini-spacing-bottom: 2px;
@descriptions-item-size-small-spacing-bottom: 4px;
@descriptions-item-size-medium-spacing-bottom: 8px;
@descriptions-item-size-default-spacing-bottom: 12px;
@descriptions-item-size-large-spacing-bottom: 16px;
@descriptions-border-item-size-mini-padding-horizontal: 20px;
@descriptions-border-item-size-mini-padding-vertical: 3px;
@descriptions-border-item-size-small-padding-horizontal: 20px;
@descriptions-border-item-size-small-padding-vertical: 3px;
@descriptions-border-item-size-medium-padding-horizontal: 20px;
@descriptions-border-item-size-medium-padding-vertical: 5px;
@descriptions-border-item-size-default-padding-horizontal: 20px;
@descriptions-border-item-size-default-padding-vertical: 7px;
@descriptions-border-item-size-large-padding-horizontal: 20px;
@descriptions-border-item-size-large-padding-vertical: 9px;
@descriptions-prefix-cls: arco-descriptions;


/*********** divider ***********/

@divider-margin-horizontal: 12px;
@divider-margin-vertical: 20px;
@divider-margin-vertical_text: 20px;
@divider-margin-text: 16px;
@divider-position-text-left: 24px;
@divider-position-text-right: 24px;
@divider-font-size: 14px;
@divider-font-weight: 500;
@divider-size: 1px;
@divider-line-style: solid;
@divider-color-bg: var(--color-neutral-3);
@divider-color-text: var(--color-text-1);
@divider-prefix-cls: arco-divider;


/*********** drawer ***********/

@drawer-size-header-height: 48px;
@drawer-margin-footer-button-left: 12px;
@drawer-font-header-size: 16px;
@drawer-font-header-weight: 500;
@drawer-padding-horizontal: 16px;
@drawer-padding-footer-vertical: 16px;
@drawer-padding-content-vertical: 12px;
@drawer-color-border: var(--color-neutral-3);
@drawer-color-header-text: var(--color-text-1);
@drawer-color-content-text: var(--color-text-1);
@drawer-position-close-icon-right: 16px;
@drawer-font-size-close-icon: 12px;
@drawer-prefix-cls: arco-drawer;


/*********** dropdown ***********/

@dropdown-max-height: 200px;
@dropdown-border-radius: var(--border-radius-medium);
@dropdown-padding-vertical: 4px;
@dropdown-font-size: 14px;
@dropdown-color-bg: var(--color-bg-popup);
@dropdown-color-border: var(--color-fill-3);
@dropdown-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@dropdown-option-height: 36px;
@dropdown-option-padding-horizontal: 12px;
@dropdown-option-font-weight_selected: 500;
@dropdown-option-color-bg_default: transparent;
@dropdown-option-color-bg_hover: var(--color-fill-2);
@dropdown-option-color-bg_selected: transparent;
@dropdown-option-color-bg_disabled: transparent;
@dropdown-option-color-text_default: var(--color-text-1);
@dropdown-option-color-text_hover: var(--color-text-1);
@dropdown-option-color-text_selected: var(--color-text-1);
@dropdown-option-color-text_disabled: var(--color-text-4);
@dropdown-group-title-height: 20px;
@dropdown-group-title-padding-horizontal: 12px;
@dropdown-group-title-margin-top: 8px;
@dropdown-group-title-font-size: 12px;
@dropdown-group-title-color-text: var(--color-text-3);
@dropdown-margin-left-suffix-icon: 12px;
@dropdown-dark-color-bg: var(--color-menu-dark-bg);
@dropdown-dark-color-border: var(--color-menu-dark-bg);
@dropdown-dark-option-color-bg_default: transparent;
@dropdown-dark-option-color-bg_hover: var(--color-menu-dark-hover);
@dropdown-dark-option-color-bg_selected: transparent;
@dropdown-dark-option-color-bg_disabled: transparent;
@dropdown-dark-option-color-text_default: var(--color-text-4);
@dropdown-dark-option-color-text_hover: var(--color-text-4);
@dropdown-dark-option-color-text_selected: var(--color-white);
@dropdown-dark-option-color-text_disabled: var(--color-text-2);
@dropdown-dark-group-title-color-text: var(--color-text-3);
@dropdown-prefix-cls: arco-dropdown;


/*********** empty ***********/

@empty-spacing-padding: 10px;
@empty-color-icon: rgb(var(--gray-5));
@empty-color-text: rgb(var(--gray-5));
@empty-font-size-image: 48px;
@empty-font-size-text: 14px;
@empty-spacing-image-margin-bottom: 4px;
@empty-size-img-height: 80px;
@empty-prefix-cls: arco-empty;


/*********** form ***********/

@form-size-mini-margin-item-bottom: 16px;
@form-size-small-margin-item-bottom: 20px;
@form-size-default-margin-item-bottom: 20px;
@form-size-large-margin-item-bottom: 20px;
@form-size-mini-font-label-size: 12px;
@form-size-small-font-label-size: 14px;
@form-size-large-font-label-size: 14px;
@form-size-default-font-label-size: 14px;
@form-font-extra-text-size: 12px;
@form-font-error-text-size: 12px;
@form-margin-label-right: 16px;
@form-margin-extra-bottom: 4px;
@form-margin-extra-top: 4px;
@form-inline-margin-item-right: 24px;
@form-inline-margin-item-bottom: 8px;
@form-vertical-margin-label-bottom: 8px;
@form-color-extra-text: var(--color-text-3);
@form-color-text-label: var(--color-text-2);
@form-color-bg_warning: var(--color-warning-light-1);
@form-color-bg_warning_hover: var(--color-warning-light-2);
@form-color-bg_warning_focus: var(--color-bg-2);
@form-color-border_warning: transparent;
@form-color-border_warning_focus: rgb(var(--warning-6));
@form-color-border_warning_hover: transparent;
@form-size-shadow_warning_focus: 0;
@form-color-shadow_warning_focus: var(--color-warning-light-2);
@form-color-bg_success: var(--color-fill-2);
@form-color-bg_success_hover: var(--color-fill-3);
@form-color-bg_success_focus: var(--color-bg-2);
@form-color-border_success: transparent;
@form-color-border_success_focus: rgb(var(--success-6));
@form-color-border_success_hover: transparent;
@form-size-shadow_success_focus: 0;
@form-color-shadow_success_focus: var(--color-success-light-2);
@form-color-bg_error: var(--color-danger-light-1);
@form-color-bg_error_hover: var(--color-danger-light-2);
@form-color-bg_error_focus: var(--color-bg-2);
@form-color-border_error: transparent;
@form-color-border_error_focus: rgb(var(--danger-6));
@form-color-border_error_hover: transparent;
@form-size-shadow_error_focus: 0;
@form-color-shadow_error_focus: var(--color-danger-light-2);
@form-color-bg_validating: var(--color-fill-2);
@form-color-bg_validating_hover: var(--color-fill-3);
@form-color-bg_validating_focus: var(--color-bg-2);
@form-color-border_validating: transparent;
@form-color-border_validating_focus: rgb(var(--primary-6));
@form-color-border_validating_hover: transparent;
@form-size-shadow_validating_focus: 0;
@form-color-shadow_validating_focus: var(--color-primary-light-2);
@form-color-tip-text_success: rgb(var(--success-6));
@form-color-tip-icon-text_success: rgb(var(--success-6));
@form-color-tip-text_error: rgb(var(--danger-6));
@form-color-tip-icon-text_error: rgb(var(--danger-6));
@form-color-tip-text_warning: rgb(var(--warning-6));
@form-color-tip-icon-text_warning: rgb(var(--warning-6));
@form-color-tip-text_validating: rgb(var(--primary-6));
@form-color-tip-icon-text_validating: rgb(var(--primary-6));
@form-prefix-cls: arco-form;


/*********** row ***********/

@row-prefix-cls: arco-row;


/*********** col ***********/

@col-prefix-cls: arco-col;


/*********** image ***********/

@image-radius: var(--border-radius-small);
@image-font-size-title: 16px;
@image-font-weight-title: 500;
@image-font-size-description: 14px;
@image-color-title_footer_outer-text: var(--color-text-1);
@image-color-title_footer_inner-text: var(--color-white);
@image-color-description_footer_inner-text: var(--color-white);
@image-color-description_footer_outer-text: var(--color-neutral-6);
@image-spacing-actions-left: 12px;
@image-font-size-actions-item: 14px;
@image-padding-actions-item-vertical: 0;
@image-padding-actions-item-horizontal: 0;
@image-spacing-actions-item-left: 12px;
@image-radius-actions-item: var(--border-radius-small);
@image-color-actions-item_footer_inner_hover-bg: rgba(0, 0, 0, 0.5);
@image-color-actions-item_footer_outer_hover-bg: var(--color-neutral-2);
@image-color-actions-item_trigger-text: var(--color-neutral-8);
@image-color-actions-item_trigger_hover-bg: var(--color-neutral-2);
@image-spacing-actions-trigger-item-vertical: 5px;
@image-spacing-actions-trigger-item-horizontal: 4px;
@image-color-footer_inner-bg: linear-gradient(360deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
@image-color-footer_inner-text: var(--color-white);
@image-padding-footer_inner_vertical: 9px;
@image-padding-footer_inner_horizontal: 16px;
@image-spacing-footer_inner_simple-vertical: 12px;
@image-spacing-footer_inner_simple-horizontal: 16px;
@image-color-footer_outer-text: var(--color-neutral-8);
@image-spacing-footer-top: 4px;
@image-color-error-bg: var(--color-neutral-1);
@image-color-error-text: var(--color-neutral-4);
@image-font-size-error-icon: 60px;
@image-font-size-error-text: 12px;
@image-line-height-error-text: 1.6667;
@image-size-error-min-height: 100px;
@image-spacing-error-padding: 16px;
@image-color-loader-bg: var(--color-neutral-1);
@image-size-loader-min-height: 100px;
@image-font-size-loader-spin: 32px;
@image-color-loader-spin-text: rgb(var(--primary-6));
@image-color-loader-spin-text-text: var(--color-neutral-6);
@image-font-size-loader-spin-text: 16px;
@image-preview-color-mask-bg: var(--color-mask-bg);
@image-preview-size-scale-value-height: 32px;
@image-preview-spacing-scale-value-vertical: 7px;
@image-preview-spacing-scale-value-horizontal: 10px;
@image-preview-font-size-scale-value: 12px;
@image-preview-color-scale-value-text: var(--color-white);
@image-preview-color-scale-value-bg: rgba(255, 255, 255, 0.08);
@image-preview-color-toolbar-bg: var(--color-bg-2);
@image-preview-radius-toolbar: var(--border-radius-medium);
@image-preview-spacing-toolbar-vertical: 4px;
@image-preview-spacing-toolbar-horizontal: 16px;
@image-preview-spacing-toolbar-horizontal_simple: 4px;
@image-preview-spacing-toolbar-vertical_simple: 4px;
@image-preview-position-toolbar-bottom: 46px;
@image-preview-font-size-action: 14px;
@image-preview-color-action-text: var(--color-neutral-8);
@image-preview-radius-action: var(--border-radius-small);
@image-preview-color-action-bg: transparent;
@image-preview-color-action_hover-bg: var(--color-neutral-2);
@image-preview-color-action_hover-text: rgb(var(--primary-6));
@image-preview-color-action_disabled-bg: transparent;
@image-preview-color-action_disabled-text: var(--color-text-4);
@image-preview-font-size-action-name: 12px;
@image-preview-spacing-action-name-right: 12px;
@image-preview-padding-action-content: 13px;
@image-preview-margin-action-right: 0;
@image-preview-spacing-trigger-padding-vertical: 12px;
@image-preview-spacing-trigger-padding-horizontal: 16px;
@image-preview-margin-action-bottom: 0;
@image-preview-color-loading-text: rgb(var(--primary-6));
@image-preview-color-loading-bg: #232324;
@image-preview-font-size-loading: 18px;
@image-preview-spacing-loading-padding: 10px;
@image-preview-size-loading-width: 48px;
@image-preview-size-loading-height: 48px;
@image-preview-radius-loading: var(--border-radius-medium);
@image-preview-size-close-btn-width: 32px;
@image-preview-size-close-icon: 14px;
@image-preview-color-close-btn-bg: rgba(0, 0, 0, 0.5);
@image-preview-color-close-btn-text: var(--color-white);
@image-preview-position-close-btn-right: 36px;
@image-preview-position-close-btn-top: 36px;
@image-preview-arrow-position: 20px;
@image-preview-arrow-size: 32px;
@image-preview-arrow-font-size: 16px;
@image-preview-arrow-color-icon: var(--color-white);
@image-preview-arrow-color-icon_disabled: rgba(255, 255, 255, 0.3);
@image-preview-arrow-color-bg: rgba(255, 255, 255, 0.3);
@image-preview-arrow-color-bg_hover: rgba(255, 255, 255, 0.5);
@image-preview-arrow-color-bg_disabled: rgba(255, 255, 255, 0.2);
@image-trigger-spacing-padding-vertical: 6px;
@image-trigger-spacing-padding-horizontal: 4px;
@image-trigger-color-bg: var(--color-bg-5);
@image-trigger-color-border: var(--color-neutral-3);
@image-trigger-size-border: 1px;
@image-trigger-radius: 4px;
@image-trigger-prefix-cls: arco-image-trigger;
@image-prefix-cls: arco-image;


/*********** preview ***********/

@preview-prefix-cls: arco-image-preview;


/*********** textarea ***********/

@textarea-color-tip-text: var(--color-text-3);
@textarea-padding-horizontal: 12px;
@textarea-padding-vertical: 4px;
@textarea-font-size: 14px;
@textarea-font-tip-size: 12px;
@textarea-layout-tip-right: 10px;
@textarea-layout-tip-bottom: 6px;
@textarea-size-min-height: 32px;
@textarea-size-icon-clear: 12px;
@textarea-layout-top-icon-clear: 10px;
@textarea-prefix-cls: arco-textarea;


/*********** search ***********/

@search-color-icon: var(--color-text-2);
@search-button-color-text: var(--color-white);
@search-size-icon: 14px;
@search-button-padding-horizontal: 8px;


/*********** password ***********/

@password-color-eye-icon: var(--color-text-2);
@password-size-eye-icon: 12px;


/*********** layout ***********/

@layout-trigger-height: 48px;
@layout-sider-background: var(--color-menu-dark-bg);
@layout-font-color-dark: var(--color-white);
@layout-font-color: var(--color-text-1);
@layout-trigger-dark-color: rgba(255, 255, 255, 0.2);
@layout-sider-background-light: var(--color-menu-light-bg);
@layout-trigger-light-color-border: var(--color-bg-5);
@layout-prefix-cls: arco-layout;


/*********** list ***********/

@list-border-width: 1px;
@list-border-color: var(--color-neutral-3);
@list-border-radius: 2px;
@list-color-text: var(--color-text-1);
@list-font-size: 14px;
@list-line-height: 1.5715;
@list-color-text-header: var(--color-text-1);
@list-color-bg-item-hover: var(--color-fill-1);
@list-font-size-header: 16px;
@list-font-weight-header: 500;
@list-line-height-header: 1.5;
@list-size-small-padding-vertical-header: 8px;
@list-size-small-padding-horizontal-header: 20px;
@list-size-small-padding-vertical-item: 9px;
@list-size-small-padding-horizontal-item: 20px;
@list-size-default-padding-vertical-header: 12px;
@list-size-default-padding-horizontal-header: 20px;
@list-size-default-padding-vertical-item: 13px;
@list-size-default-padding-horizontal-item: 20px;
@list-size-large-padding-vertical-header: 16px;
@list-size-large-padding-horizontal-header: 20px;
@list-size-large-padding-vertical-item: 17px;
@list-size-large-padding-horizontal-item: 20px;
@list-meta-font-weight-title: 500;
@list-meta-color-title: var(--color-text-1);
@list-mete-color-description: var(--color-text-2);
@list-meta-margin-right-avatar: 16px;
@list-meta-margin-bottom-title: 2px;
@list-meta-padding-horizontal: 0;
@list-meta-padding-vertical: 4px;
@list-action-gap: 20px;
@list-action-margin-top: 4px;
@list-pagination-margin-top: 24px;
@list-prefix-cls: arco-list;


/*********** mentions ***********/

@mentions-padding-horizontal: 12px;
@mentions-padding-vertical: 4px;
@mentions-font-size: 14px;
@mentions-line-height: 1.5715;
@mentions-prefix-cls: arco-mentions;


/*********** menu ***********/

@menu-font-size: 14px;
@menu-line-height: 1.5715;
@menu-border-radius: var(--border-radius-small);
@menu-font-weight-item-selected: 500;
@menu-color-label-item-selected: rgb(var(--primary-6));
@menu-height-label-item-selected: 3px;
@menu-margin-left-item-suffix-icon: 6px;
@menu-margin-right-item-prefix-icon: 16px;
@menu-horizontal-margin-right-item-prefix-icon: 16px;
@menu-item-gap: 4px;
@menu-item-indent-spacing: 20px;
@menu-width-collapse-button: 24px;
@menu-height-collapse-button: 24px;
@menu-border-radius-collapse-button: var(--border-radius-small);
@menu-light-color-bg: var(--color-menu-light-bg);
@menu-light-color-bg-item_default: var(--color-menu-light-bg);
@menu-light-color-bg-item_hover: var(--color-fill-2);
@menu-light-color-bg-item_selected: var(--color-fill-2);
@menu-light-color-bg-item_disabled: var(--color-menu-light-bg);
@menu-light-color-item_default: var(--color-text-2);
@menu-light-color-item_hover: var(--color-text-2);
@menu-light-color-item_selected: rgb(var(--primary-6));
@menu-light-color-submenu_selected: rgb(var(--primary-6));
@menu-light-color-bg-submenu_selected_hover: var(--color-fill-2);
@menu-light-color-item_disabled: var(--color-text-4);
@menu-light-color-icon_default: var(--color-text-3);
@menu-light-color-icon_hover: var(--color-text-3);
@menu-light-color-icon_selected: rgb(var(--primary-6));
@menu-light-color-icon_disabled: var(--color-text-4);
@menu-light-color-group-title: var(--color-text-3);
@menu-dark-color-bg: var(--color-menu-dark-bg);
@menu-dark-color-bg-item_default: var(--color-menu-dark-bg);
@menu-dark-color-bg-item_hover: var(--color-menu-dark-hover);
@menu-dark-color-bg-item_selected: var(--color-menu-dark-hover);
@menu-dark-color-bg-item_disabled: var(--color-menu-dark-bg);
@menu-dark-color-submenu_selected: rgb(var(--primary-6));
@menu-dark-color-bg-submenu_selected_hover: var(--color-menu-dark-hover);
@menu-dark-color-item_default: var(--color-text-4);
@menu-dark-color-item_hover: var(--color-text-4);
@menu-dark-color-item_selected: var(--color-white);
@menu-dark-color-item_disabled: var(--color-text-2);
@menu-dark-color-icon_default: var(--color-text-3);
@menu-dark-color-icon_hover: var(--color-text-3);
@menu-dark-color-icon_selected: var(--color-white);
@menu-dark-color-icon_disabled: var(--color-text-2);
@menu-dark-color-group-title: var(--color-text-3);
@menu-color-border-popup: var(--color-neutral-3);
@menu-light-color-bg-button: var(--color-fill-1);
@menu-light-color-bg-button_hover: var(--color-fill-3);
@menu-light-color-button: var(--color-text-3);
@menu-dark-color-bg-button: rgb(var(--primary-6));
@menu-dark-color-bg-button_hover: rgb(var(--primary-7));
@menu-dark-color-button: var(--color-white);
@menu-horizontal-padding-vertical: 14px;
@menu-horizontal-padding-horizontal: 20px;
@menu-horizontal-item-gap: 12px;
@menu-horizontal-item-height: 30px;
@menu-horizontal-item-padding-horizontal: 12px;
@menu-vertical-padding-vertical: 4px;
@menu-vertical-padding-horizontal: 8px;
@menu-vertical-item-height: 40px;
@menu-vertical-item-padding-horizontal: 12px;
@menu-collapse-width: 48px;
@menu-collapse-padding-vertical: 4px;
@menu-collapse-padding-horizontal: 4px;
@menu-pop-button-size: 40px;
@menu-pop-button-margin-bottom: 16px;
@menu-pop-button-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@menu-pop-button-border-color: transparent;
@menu-prefix-cls: arco-menu;


/*********** message ***********/

@message-wrapper-margin-top: 40px;
@message-wrapper-margin-bottom: 40px;
@message-padding-top: 10px;
@message-padding-bottom: 10px;
@message-padding-left: 16px;
@message-padding-right: 16px;
@message-margin-bottom: 16px;
@message-border-radius: var(--border-radius-small);
@message-font-size-icon: 20px;
@message-font-size-content: 14px;
@message-icon-margin-right: 8px;
@message-border-width: 1px;
@message-border-style: solid;
@message-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
@message-color-close-icon: var(--color-text-1);
@message-close-icon-font-size: 12px;
@message-close-icon-top: 14px;
@message-close-icon-right: 12px;
@message-close-icon-left: 14px;
@message-normal-color-bg: var(--color-bg-popup);
@message-normal-color-icon: var(--color-text-1);
@message-normal-color-content: var(--color-text-1);
@message-normal-color-border: var(--color-neutral-3);
@message-info-color-bg: var(--color-bg-popup);
@message-info-color-icon: rgb(var(--primary-6));
@message-info-color-content: var(--color-text-1);
@message-info-color-border: var(--color-neutral-3);
@message-success-color-bg: var(--color-bg-popup);
@message-success-color-icon: rgb(var(--success-6));
@message-success-color-content: var(--color-text-1);
@message-success-color-border: var(--color-neutral-3);
@message-warning-color-bg: var(--color-bg-popup);
@message-warning-color-icon: rgb(var(--warning-6));
@message-warning-color-content: var(--color-text-1);
@message-warning-color-border: var(--color-neutral-3);
@message-error-color-bg: var(--color-bg-popup);
@message-error-color-icon: rgb(var(--danger-6));
@message-error-color-content: var(--color-text-1);
@message-error-color-border: var(--color-neutral-3);
@message-loading-color-bg: var(--color-bg-popup);
@message-loading-color-icon: rgb(var(--primary-6));
@message-loading-color-content: var(--color-text-1);
@message-loading-color-border: var(--color-neutral-3);
@message-prefix-cls: arco-message;


/*********** modal ***********/

@modal-border-radius: var(--border-radius-medium);
@modal-size-tip-icon: 18px;
@modal-margin-top: 100px;
@modal-margin-tip-icon-right: 10px;
@modal-margin-footer-button-left: 12px;
@modal-color-border: var(--color-neutral-3);
@modal-border-width: 0;
@modal-box-shadow: none;
@modal-color-header-text: var(--color-text-1);
@modal-color-content-text: var(--color-text-1);
@modal-font-header-size: 16px;
@modal-font-header-weight: 500;
@modal-font-content-size: 14px;
@modal-default-align-header: center;
@modal-simple-align-header: center;
@modal-default-padding-horizontal: 20px;
@modal-default-size-header-height: 48px;
@modal-default-padding-content-vertical: 24px;
@modal-default-padding-footer-vertical: 16px;
@modal-default-size-width: 520px;
@modal-simple-size-width: 464px;
@modal-simple-padding-horizontal: 32px;
@modal-simple-padding-top: 24px;
@modal-simple-padding-bottom: 32px;
@modal-simple-margin-footer-top: 32px;
@modal-simple-margin-content-top: 24px;
@modal-position-close-icon-right: 16px;
@modal-font-size-close-icon: 12px;
@modal-color-close-icon: var(--color-text-1);
@modal-prefix-cls: arco-modal;


/*********** notification ***********/

@notification-wrapper-margin-top: 20px;
@notification-wrapper-margin-bottom: 20px;
@notification-wrapper-margin-left: 20px;
@notification-wrapper-margin-right: 20px;
@notification-border-radius: var(--border-radius-medium);
@notification-margin-bottom: 20px;
@notification-width: 300px;
@notification-padding-top: 20px;
@notification-padding-bottom: 20px;
@notification-padding-left: 20px;
@notification-padding-right: 20px;
@notification-font-size-icon: 24px;
@notification-font-size-title: 16px;
@notification-font-size-content: 14px;
@notification-icon-margin-right: 16px;
@notification-title-margin-bottom: 4px;
@notification-btn-wrapper-margin-top: 16px;
@notification-border-width: 1px;
@notification-border-style: solid;
@notification-color-close-icon: var(--color-text-1);
@notification-close-icon-font-size: 12px;
@notification-close-icon-top: 12px;
@notification-close-icon-right: 12px;
@notification-normal-color-bg: var(--color-bg-popup);
@notification-normal-color-icon: var(--color-text-1);
@notification-normal-color-text-title: var(--color-text-1);
@notification-normal-color-text-content: var(--color-text-1);
@notification-normal-color-border: var(--color-neutral-3);
@notification-info-color-bg: var(--color-bg-popup);
@notification-info-color-icon: rgb(var(--primary-6));
@notification-info-color-text-title: var(--color-text-1);
@notification-info-color-text-content: var(--color-text-1);
@notification-info-color-border: var(--color-neutral-3);
@notification-success-color-bg: var(--color-bg-popup);
@notification-success-color-icon: rgb(var(--success-6));
@notification-success-color-text-title: var(--color-text-1);
@notification-success-color-text-content: var(--color-text-1);
@notification-success-color-border: var(--color-neutral-3);
@notification-warning-color-bg: var(--color-bg-popup);
@notification-warning-color-icon: rgb(var(--warning-6));
@notification-warning-color-text-title: var(--color-text-1);
@notification-warning-color-text-content: var(--color-text-1);
@notification-warning-color-border: var(--color-neutral-3);
@notification-error-color-bg: var(--color-bg-popup);
@notification-error-color-icon: rgb(var(--danger-6));
@notification-error-color-text-title: var(--color-text-1);
@notification-error-color-text-content: var(--color-text-1);
@notification-error-color-border: var(--color-neutral-3);
@notification-prefix-cls: arco-notification;


/*********** page ***********/

@page-header-padding-left: 24px;
@page-header-padding-right: 20px;
@page-header-padding-vertical: 16px;
@page-header-padding-vertical_breadcrumb: 12px;
@page-header-color-back-icon: var(--color-text-2);
@page-header-size-back-icon: 14px;
@page-header-margin-back-icon-right: 12px;
@page-header-line-height: 28px;
@page-header-color-title-text: var(--color-text-1);
@page-header-weight-title-text: 600;
@page-header-color-back-icon-bg_hover: var(--color-fill-2);
@page-header-size-back-icon-bg_hover: 30px;
@page-header-size-title-text: 20px;
@page-header-color-divider-bg: var(--color-fill-3);
@page-header-size-divider-height: 16px;
@page-header-size-divider-width: 1px;
@page-header-margin-divider-left: 12px;
@page-header-margin-divider-right: 12px;
@page-header-color-sub-title-text: var(--color-text-3);
@page-header-size-sub-title-text: 14px;
@page-header-color-header-border: var(--color-neutral-3);
@page-header-border-header-width: 1px;
@page-header-border-header-style: solid;
@page-header-padding-content-vertical: 20px;
@page-header-padding-content-horizontal: 32px;
@page-header-margin-breadcrumb-bottom: 4px;
@page-header-prefix-cls: arco-page-header;


/*********** pagination ***********/

@pagination-prefix-cls: arco-pagination;
@pagination-item-border-radius: var(--border-radius-small);
@pagination-item-spacing: 8px;
@pagination-margin-total-spacing: 8px;
@pagination-margin-option-left: 8px;
@pagination-margin-jumper-left: 8px;
@pagination-size-mini: 24px;
@pagination-size-small: 28px;
@pagination-size-default: 32px;
@pagination-size-large: 36px;
@pagination-size-mini-font-size: 12px;
@pagination-size-small-font-size: 14px;
@pagination-size-default-font-size: 14px;
@pagination-size-large-font-size: 14px;
@pagination-size-icon-arrow_mini: 12px;
@pagination-size-icon-arrow_small: 12px;
@pagination-size-icon-arrow_default: 12px;
@pagination-size-icon-arrow_large: 14px;
@pagination-size-icon-ellipsis: 16px;
@pagination-border-width: 0;
@pagination-color-bg-item: transparent;
@pagination-color-bg-item_active: var(--color-primary-light-1);
@pagination-color-bg-item_hover: var(--color-fill-1);
@pagination-color-bg-item_disabled: transparent;
@pagination-color-bg-item_active_disabled: var(--color-fill-1);
@pagination-color-item-text: var(--color-text-2);
@pagination-color-item-text_hover: var(--color-text-2);
@pagination-color-item-text_active: rgb(var(--primary-6));
@pagination-color-item-text_disabled: var(--color-text-4);
@pagination-color-item-text_active_disabled: var(--color-primary-light-3);
@pagination-color-item-border: transparent;
@pagination-color-item-border_active: transparent;
@pagination-color-item-border_hover: transparent;
@pagination-color-item-border_disabled: transparent;
@pagination-color-item-border_active_disabled: transparent;
@pagination-color-icon-arrow: var(--color-text-2);
@pagination-color-icon-arrow-bg: transparent;
@pagination-color-icon-arrow-bg_hover: var(--color-fill-1);
@pagination-color-icon-arrow-bg_disabled: transparent;
@pagination-color-icon-arrow-text_hover: rgb(var(--primary-6));
@pagination-color-icon-arrow-text_disabled: var(--color-text-4);
@pagination-simple-input-width: 40px;
@pagination-simple-color-icon-arrow: var(--color-text-2);
@pagination-simple-color-icon-arrow-bg: transparent;
@pagination-simple-color-icon-arrow-bg_hover: var(--color-fill-1);
@pagination-simple-color-icon-arrow-bg_disabled: transparent;
@pagination-simple-color-icon-arrow-text_hover: rgb(var(--primary-6));
@pagination-simple-color-icon-arrow-text_disabled: var(--color-text-4);
@pagination-simple-margin-prev-right: 4px;
@pagination-simple-margin-next-left: 12px;
@pagination-simple-margin-separator-left: 12px;
@pagination-simple-margin-separator-right: 12px;
@pagination-color-jumper-goto: var(--color-text-3);
@pagination-color-text-total: var(--color-text-1);


/*********** patination ***********/

@patination-jumper-input-width: 40px;


/*********** popconfirm ***********/

@popconfirm-padding-horizontal: 16px;
@popconfirm-padding-vertical: 16px;
@popconfirm-margin-title-bottom: 16px;
@popconfirm-size-title-icon: 18px;
@popconfirm-margin-icon-right: 8px;
@popconfirm-margin-button-left: 8px;
@popconfirm-font-title-size: 14px;
@popconfirm-color-title-text: var(--color-text-1);
@popconfirm-prefix-cls: arco-popconfirm;


/*********** popover ***********/

@popover-prefix-cls: arco-popover;


/*********** progress ***********/

@progress-line-color-line-bg: var(--color-fill-3);
@progress-line-color-inner-bg: rgb(var(--primary-6));
@progress-line-color-inner-bg_success: rgb(var(--success-6));
@progress-line-color-inner-bg_error: rgb(var(--danger-6));
@progress-line-color-buffer-bg: var(--color-primary-light-3);
@progress-line-size-large-font-size: 16px;
@progress-line-size-small-font-size: 12px;
@progress-line-size-default-font-size: 12px;
@progress-line-size-large-margin-text-left: 16px;
@progress-line-size-small-margin-text-left: 16px;
@progress-line-color-icon_success: rgb(var(--success-6));
@progress-line-color-icon_error: rgb(var(--danger-6));
@progress-line-margin-text-left: 16px;
@progress-line-margin-icon-left: 4px;
@progress-line-color-text: var(--color-text-2);
@progress-line-color-icon_normal: var(--color-text-2);
@progress-line-size-default-icon-size: 12px;
@progress-line-size-small-icon-size: 12px;
@progress-line-size-large-icon-size: 14px;
@progress-circle-size-small-font-size: 13px;
@progress-circle-size-default-font-size: 14px;
@progress-circle-size-large-font-size: 16px;
@progress-circle-size-small-icon-size: 14px;
@progress-circle-size-default-icon-size: 16px;
@progress-circle-size-large-icon-size: 16px;
@progress-circle-color-text: var(--color-text-3);
@progress-circle-color-mask-stroke: var(--color-fill-3);
@progress-circle-size-mini-color-mask-stroke: var(--color-primary-light-3);
@progress-circle-size-mini-color-mask-stroke_success: var(--color-success-light-3);
@progress-circle-size-mini-color-mask-stroke_error: var(--color-danger-light-3);
@progress-circle-color-path-stroke: rgb(var(--primary-6));
@progress-circle-color-path-stroke_success: rgb(var(--success-6));
@progress-circle-color-path-stroke_error: rgb(var(--danger-6));
@progress-circle-color-icon_success: rgb(var(--success-6));
@progress-circle-color-icon_error: rgb(var(--danger-6));
@progress-steps-size-small-steps-item-width: 2px;
@progress-steps-margin-steps-item-right: 3px;
@progress-steps-margin-steps-item-right_small: 3px;
@progress-steps-color-item-bg: var(--color-fill-3);
@progress-steps-color-item-bg_normal: rgb(var(--primary-6));
@progress-steps-color-item-bg_success: rgb(var(--success-6));
@progress-steps-color-item-bg_error: rgb(var(--danger-6));
@progress-steps-margin-text-left: 8px;
@progress-line-color-inner-bg_warning: rgb(var(--warning-6));
@progress-line-color-icon_warning: rgb(var(--warning-6));
@progress-circle-size-mini-color-mask-stroke_warning: var(--color-warning-light-3);
@progress-circle-color-path-stroke_warning: rgb(var(--warning-6));
@progress-circle-color-icon_warning: rgb(var(--warning-6));
@progress-steps-color-item-bg_warning: rgb(var(--warning-6));
@progress-prefix-cls: arco-progress;


/*********** radio ***********/

@radio-color-border: var(--color-neutral-3);
@radio-border-width: 2px;
@radio-layout-height: 14px;
@radio-color-border_hover: var(--color-neutral-3);
@radio-color-border_disabled: var(--color-neutral-3);
@radio-color-bg: var(--color-bg-2);
@radio-color-bg_checked: rgb(var(--primary-6));
@radio-border-radius: var(--border-radius-circle);
@radio-color-bg_disabled: var(--color-fill-2);
@radio-color-bg_checked_disabled: var(--color-primary-light-3);
@radio-color-dot-bg_checked_disabled: var(--color-fill-2);
@radio-color-text: var(--color-text-1);
@radio-color-text_disabled: var(--color-text-4);
@radio-color-text_checked_disabled: var(--color-text-4);
@radio-font-text-size: 14px;
@radio-font-text-size_large: 14px;
@radio-font-text-size_mini: 12px;
@radio-font-text-size_small: 14px;
@radio-margin-text-left: 8px;
@radio-size-mask-height: 24px;
@radio-mask-bg-color-bg: var(--color-fill-2);
@radio-group-margin-right: 20px;
@radio-group-button-color-bg: var(--color-fill-2);
@radio-group-button-color-bg_dark: var(--color-bg-3);
@radio-button-padding-horizontal: 12px;
@radio-button-spacing: 3px;
@radio-button-color-bg_active: var(--color-bg-5);
@radio-button-color-bg_active_dark: var(--color-fill-3);
@radio-button-color-bg_hover: var(--color-bg-5);
@radio-button-color-text_active: rgb(var(--primary-6));
@radio-button-font-text-weight_active: 500;
@radio-button-color-text_hover: var(--color-text-1);
@radio-button-border-radius: var(--border-radius-small);
@radio-button-bg-border-radius: var(--border-radius-small);
@radio-button-color-bg: transparent;
@radio-button-color-text: var(--color-text-2);
@radio-button-color-bg_disabled: transparent;
@radio-button-color-text_disabled: var(--color-text-4);
@radio-button-color-bg_checked_disabled: var(--color-bg-5);
@radio-button-color-text_checked_disabled: var(--color-primary-light-3);
@radio-button-color-separator-bg: var(--color-neutral-3);
@radio-button-size-separator-width: 1px;
@radio-button-size-separator-height: 14px;
@radio-size-default-height: 32px;
@radio-size-small-height: 28px;
@radio-size-mini-height: 24px;
@radio-size-large-height: 36px;
@radio-group-size-line-height_vertical: 32px;
@radio-prefix-cls: arco-radio;


/*********** rate ***********/

@rate-min-height: 32px;
@rate-gap-size: 8px;
@rate-font-size: 24px;
@rate-scale_active: 1.2;
@rate-color-bg_active: rgb(var(--gold-6));
@rate-color-bg_default: var(--color-fill-3);
@rate-color-bg_hover: rgb(var(--gold-5));


/*********** prefixCls ***********/

@prefixCls: arco-rate;


/*********** resizeBox ***********/

@resizeBox-trigger-color-background: var(--color-neutral-3);
@resizeBox-trigger-size-icon-wrapper: 6px;
@resizeBox-trigger-font-size-icon: 12px;
@resizeBox-trigger-color-icon: var(--color-text-1);


/*********** resizebox ***********/

@resizebox-prefix-cls: arco-resizebox;
@resizebox-trigger-prefix-cls: arco-resizebox-trigger;


/*********** result ***********/

@result-padding-top: 24px;
@result-padding-top_icon: 32px;
@result-padding-bottom: 24px;
@result-padding-horizontal: 32px;
@result-margin-icon-bottom: 16px;
@result-margin-extra-top: 20px;
@result-margin-content-top: 20px;
@result-font-title-size: 14px;
@result-font-title-weight: 500;
@result-font-subtitle-size: 14px;
@result-color-title-text: var(--color-text-1);
@result-color-subtitle-text: var(--color-text-2);
@result-size-icon: 20px;
@result-size-icon-wrapper: 45px;
@result-size-image-width: 92px;
@result-size-icon_custom: 45px;
@result-color-icon_default: inherit;
@result-color-icon_success: rgb(var(--success-6));
@result-color-icon-bg_success: var(--color-success-light-1);
@result-color-icon_error: rgb(var(--danger-6));
@result-color-icon-bg_error: var(--color-danger-light-1);
@result-color-icon_warning: rgb(var(--warning-6));
@result-color-icon-bg_warning: var(--color-warning-light-1);
@result-color-icon_info: rgb(var(--primary-6));
@result-color-icon-bg_info: var(--color-primary-light-1);
@result-prefix-cls: arco-result;


/*********** skeleton ***********/

@skeleton-color-bg-base: var(--color-fill-2);
@skeleton-radius-image-border: var(--border-radius-small);
@skeleton-size-image_default: 48px;
@skeleton-size-image_small: 36px;
@skeleton-size-image_large: 60px;
@skeleton-spacing-image_left-margin-right: 16px;
@skeleton-spacing-image_right-margin-left: 16px;
@skeleton-size-row-height: 16px;
@skeleton-spacing-last_row-margin-bottom: 16px;
@skeleton-color-animate-bg: var(--color-fill-3);
@skeleton-prefix-cls: arco-skeleton;


/*********** slider ***********/

@slider-size-road-width: 2px;
@slider-color-road-bg: var(--color-fill-3);
@slider-color-road-bg_disabled: var(--color-fill-2);
@slider-color-bar-bg: rgb(var(--primary-6));
@slider-color-bar-bg_disabled: var(--color-fill-3);
@slider-color-button-bg: var(--color-bg-2);
@slider-border-size-button: 2px;
@slider-color-button-border: rgb(var(--primary-6));
@slider-color-button-border_disabled: var(--color-fill-3);
@slider-shadow-button_active: 0 2px 5px rgba(0, 0, 0, 0.1);
@slider-size-button-width: 12px;
@slider-size-button-width_active: 14px;
@slider-color-dot-bg: var(--color-bg-2);
@slider-border-size-dot: 2px;
@slider-font-size-dot: 12px;
@slider-size-dot-width: 8px;
@slider-spacing-margin-bottom_with-mark: 24px;
@slider-spacing-padding_width-mark: 20px;
@slider-spacing-padding_width-mark_vertical: 20px;
@slider-font-size-mark: 14px;
@slider-color-mark-font: var(--color-text-3);
@slider-size-tick-width: 1px;
@slider-size-tick-height: 3px;
@slider-spacing-input-margin-left: 20px;
@slider-size-input-width: 60px;
@slider-size-input-height: 32px;
@slider-size-input_range-width: 20px;
@slider-size-input_range-height: 32px;
@slider-size-input_range_content-width: 8px;
@slider-size-input_range_content-height: 2px;
@slider-color-input_range_content-bg: rgb(var(--gray-6));
@slider-size-height_vertical: 200px;
@slider-spacing-mark-left: 3px;
@slider-prefix: arco-slider;


/*********** space ***********/

@space-prefix-cls: arco-space;


/*********** spin ***********/

@spin-font-size-text: 14px;
@spin-font-size-icon: 20px;
@spin-font-weight: 500;
@spin-margin-top-tip: 6px;
@spin-color-text: rgb(var(--primary-6));
@spin-color-icon: rgb(var(--primary-6));
@spin-dot-color-icon_default: rgb(var(--primary-6));
@spin-dot-color-icon_second: rgb(var(--primary-5));
@spin-dot-color-icon_third: rgb(var(--primary-4));
@spin-dot-color-icon_forth: rgb(var(--primary-4));
@spin-dot-color-icon_last: rgb(var(--primary-2));
@spin-dot-size-width: 8px;
@spin-prefix-cls: arco-spin;


/*********** statistic ***********/

@statistic-font-title-size: 14px;
@statistic-margin-title-bottom: 8px;
@statistic-margin-extra-top: 8px;
@statistic-font-int-size: 26px;
@statistic-font-decimal-size: 26px;
@statistic-font-value-weight: 500;
@statistic-color-value-text: var(--color-text-1);
@statistic-color-text: var(--color-text-2);
@statistic-color-title-text: var(--color-text-2);
@statistic-color-extra-text: var(--color-text-2);
@statistic-size-value-icon: 14px;
@statistic-font-suffix-size: 14px;
@statistic-prefix-cls: arco-statistic;


/*********** steps ***********/

@steps-size-default: 28px;
@steps-size-small: 24px;
@steps-size-default-arrow: 72px;
@steps-size-small-arrow: 40px;
@steps-size-default-font-size-icon: 16px;
@steps-size-default-font-size-title: 16px;
@steps-size-default-font-size-description: 12px;
@steps-size-small-font-size-icon: 14px;
@steps-size-small-font-size-title: 14px;
@steps-size-small-font-size-description: 12px;
@steps-label-vertical-content-width: 140px;
@steps-direction-horizontal-description-width: 140px;
@steps-circle-size-item-tail: 1px;
@steps-circle-size-item-icon-gap: 12px;
@steps-circle-font-weight-item-title_active: 500;
@steps-circle-border-radius-item-icon: var(--border-radius-circle);
@steps-circle-horizontal-item-description-margin-top: 2px;
@steps-circle-vertical-item-description-margin-top: 2px;
@steps-circle-vertical-spacing-tail-top: 6px;
@steps-circle-vertical-spacing-tail-bottom: 6px;
@steps-circle-color-item-bg_wait: var(--color-fill-2);
@steps-circle-color-item-border_wait: transparent;
@steps-circle-color-item-icon-text_wait: var(--color-text-2);
@steps-circle-color-item-tail_wait: var(--color-neutral-3);
@steps-circle-color-item-title_wait: var(--color-text-2);
@steps-circle-color-item-description_wait: var(--color-text-3);
@steps-circle-color-item-bg_process: rgb(var(--primary-6));
@steps-circle-color-item-border_process: transparent;
@steps-circle-color-item-icon-text_process: var(--color-white);
@steps-circle-color-item-tail_process: rgb(var(--primary-6));
@steps-circle-color-item-title_process: var(--color-text-1);
@steps-circle-color-item-description_process: var(--color-text-3);
@steps-circle-color-item-bg_finish: var(--color-primary-light-1);
@steps-circle-color-item-border_finish: transparent;
@steps-circle-color-item-icon-text_finish: rgb(var(--primary-6));
@steps-circle-color-item-title_finish: var(--color-text-1);
@steps-circle-color-item-description_finish: var(--color-text-3);
@steps-circle-color-item-bg_error: rgb(var(--danger-6));
@steps-circle-color-item-border_error: transparent;
@steps-circle-color-item-icon-text_error: var(--color-white);
@steps-circle-color-item-tail_error: rgb(var(--danger-6));
@steps-circle-color-item-title_error: var(--color-text-1);
@steps-circle-color-item-description_error: var(--color-text-3);
@steps-dot-horizontal-item-title-margin-top: 4px;
@steps-dot-horizontal-item-description-margin-top: 4px;
@steps-dot-vertical-item-dot-margin-top: 8px;
@steps-dot-vertical-item-description-margin-top: 4px;
@steps-dot-vertical-spacing-tail-top: 4px;
@steps-dot-vertical-spacing-tail-bottom: 4px;
@steps-dot-size-item-icon: 8px;
@steps-dot-size-item-icon-active: 10px;
@steps-dot-size-item-icon-gap: 4px;
@steps-dot-size-item-tail: 1px;
@steps-dot-vertical-item-icon-margin-right: 16px;
@steps-dot-font-weight-item-title_active: 500;
@steps-dot-border-radius-item-icon: var(--border-radius-circle);
@steps-dot-color-item-bg_wait: var(--color-fill-4);
@steps-dot-color-item-border_wait: var(--color-fill-4);
@steps-dot-color-item-tail_wait: var(--color-neutral-3);
@steps-dot-color-item-title_wait: var(--color-text-2);
@steps-dot-color-item-description_wait: var(--color-text-3);
@steps-dot-color-item-bg_process: rgb(var(--primary-6));
@steps-dot-color-item-border_process: rgb(var(--primary-6));
@steps-dot-color-item-tail_process: rgb(var(--primary-6));
@steps-dot-color-item-title_process: var(--color-text-1);
@steps-dot-color-item-description_process: var(--color-text-3);
@steps-dot-color-item-bg_finish: rgb(var(--primary-6));
@steps-dot-color-item-border_finish: rgb(var(--primary-6));
@steps-dot-color-item-title_finish: var(--color-text-1);
@steps-dot-color-item-description_finish: var(--color-text-3);
@steps-dot-color-item-bg_error: rgb(var(--danger-6));
@steps-dot-color-item-border_error: rgb(var(--danger-6));
@steps-dot-color-item-tail_error: rgb(var(--danger-6));
@steps-dot-color-item-title_error: var(--color-text-1);
@steps-dot-color-item-description_error: var(--color-text-3);
@steps-arrow-size-item-gap: 4px;
@steps-arrow-size-default-title-padding-left: 16px;
@steps-arrow-size-small-title-padding-left: 20px;
@steps-arrow-item-description-margin-top: 0;
@steps-arrow-font-weight-item-title_active: 500;
@steps-arrow-color-item-bg_wait: var(--color-fill-1);
@steps-arrow-color-item-title_wait: var(--color-text-2);
@steps-arrow-color-item-description_wait: var(--color-text-3);
@steps-arrow-color-item-bg_process: rgb(var(--primary-6));
@steps-arrow-color-item-title_process: var(--color-white);
@steps-arrow-color-item-description_process: var(--color-white);
@steps-arrow-color-item-bg_finish: var(--color-primary-light-1);
@steps-arrow-color-item-title_finish: var(--color-text-1);
@steps-arrow-color-item-description_finish: var(--color-text-3);
@steps-arrow-color-item-bg_error: rgb(var(--danger-6));
@steps-arrow-color-item-title_error: var(--color-white);
@steps-arrow-color-item-description_error: var(--color-white);
@steps-navigation-color-arrow: var(--color-text-4);
@steps-navigation-size-arrow: 6px;
@steps-navigation-size-arrow-line-width: 2px;
@steps-navigation-size-arrow-top: 10px;
@steps-navigation-padding-left: 20px;
@steps-navigation-margin-right: 32px;
@steps-navigation-spacing-arrow-right: 10px;
@steps-navigation-spacing-ink-left: 0;
@steps-navigation-spacing-ink-right: 30px;
@steps-size-default-item-icon-margin-left: 56px;
@steps-size-small-item-icon-margin-left: 58px;
@steps-dot-item-icon-margin-left: 66px;
@steps-prefix-cls: arco-steps;


/*********** switch ***********/

@switch-size-default: 24px;
@switch-size-small: 16px;
@switch-font-size-text: 12px;
@switch-size-dot-default: 16px;
@switch-size-dot-small: 12px;
@switch-line-size-dot-default: 20px;
@switch-line-size-dot-small: 16px;
@switch-circle-default-width: 40px;
@switch-circle-small-width: 28px;
@switch-round-default-width: 40px;
@switch-round-small-width: 28px;
@switch-line-default-width: 36px;
@switch-line-small-width: 28px;
@switch-line-height-bg-line: 6px;
@switch-line-color-dot-shadow: var(--color-neutral-6);
@switch-color-bg_on: rgb(var(--primary-6));
@switch-color-bg_off: var(--color-fill-4);
@switch-color-bg_on_disabled: var(--color-primary-light-3);
@switch-color-bg_off_disabled: var(--color-fill-2);
@switch-color-bg_on_loading: var(--color-primary-light-3);
@switch-color-bg_off_loading: var(--color-fill-2);
@switch-color-dot-bg: var(--color-bg-white);
@switch-color-text_on: var(--color-white);
@switch-color-text_off: var(--color-white);
@switch-color-text_on_disabled: var(--color-white);
@switch-color-text_off_disabled: var(--color-white);
@switch-color-text_on_loading: var(--color-primary-light-1);
@switch-color-text_off_loading: var(--color-white);
@switch-color-dot-icon_on: rgb(var(--primary-6));
@switch-color-dot-icon_off: var(--color-neutral-3);
@switch-color-dot-icon_on_disabled: var(--color-primary-light-3);
@switch-color-dot-icon_off_disabled: var(--color-fill-2);
@switch-color-dot-icon_on_loading: var(--color-primary-light-3);
@switch-color-dot-icon_off_loading: var(--color-neutral-3);
@switch-prefix-cls: arco-switch;
@switch-size-default-gap: 4px;
@switch-size-small-gap: 2px;
@switch-size-default-line-gap: 2px;
@switch-size-small-line-gap: 0px;


/*********** table ***********/

@table-prefix-cls: arco-table;
@table-size-default-padding-horizontal: 16px;
@table-size-default-padding-vertical: 9px;
@table-size-middle-padding-horizontal: 16px;
@table-size-middle-padding-vertical: 7px;
@table-size-small-padding-horizontal: 16px;
@table-size-small-padding-vertical: 5px;
@table-size-mini-padding-horizontal: 16px;
@table-size-mini-padding-vertical: 2px;
@table-size-default-font-size: 14px;
@table-size-middle-font-size: 14px;
@table-size-small-font-size: 14px;
@table-size-mini-font-size: 12px;
@table-size-default-font-header-size: 14px;
@table-size-middle-font-header-size: 14px;
@table-size-small-font-header-size: 14px;
@table-size-mini-font-header-size: 12px;
@table-border-width: 1px;
@table-border-style: solid;
@table-size-expand-button: 14px;
@table-spacing-expand-button-margin-right: 4px;
@table-font-size-expand-button: 12px;
@table-border-radius-expand-button: 2px;
@table-color-border: var(--color-neutral-3);
@table-border-radius: var(--border-radius-medium);
@table-color-text-header-cell: rgb(var(--gray-10));
@table-color-bg-header-cell: var(--color-neutral-2);
@table-color-bg-header-sorted-cell: var(--color-neutral-3);
@table-color-bg-header-sorted-cell_hover: rgba(var(--gray-4), 0.5);
@table-color-header-filters-icon: var(--color-text-2);
@table-color-header-filters-icon_active: rgb(var(--primary-6));
@table-color-bg-header-filters-icon_hover: var(--color-neutral-4);
@table-font-size-filters-icon: 16px;
@table-size-filters-width: 24px;
@table-font-weight-header-text: 500;
@table-color-text-body-cell: rgb(var(--gray-10));
@table-color-bg-body-cell: var(--color-bg-2);
@table-color-bg-body-sorted-cell: var(--color-fill-1);
@table-color-bg-body-stripe-row: var(--color-fill-1);
@table-color-bg-body-row_hover: var(--color-fill-1);
@table-color-bg-body-row_active: var(--color-fill-1);
@table-color-expand-icon: var(--color-text-2);
@table-color-expand-icon-border: transparent;
@table-color-expand-icon-border_hover: transparent;
@table-color-expand-icon_hover: var(--color-text-1);
@table-color-bg-expand-icon: var(--color-neutral-3);
@table-color-bg-expand-icon_hover: var(--color-neutral-4);
@table-color-bg-expand-content: var(--color-fill-1);
@table-color-bg-expand-content_hover: var(--color-fill-1);
@table-border-expand-icon-width: 1px;
@table-spacing-header-sorter-icon-margin-left: 8px;
@table-color-header-sorter-icon: var(--color-neutral-5);
@table-color-header-sorter-icon_next: var(--color-neutral-6);
@table-color-header-sorter-icon_active: rgb(var(--primary-6));
@table-size-header-sorter-icon-height: 8px;
@table-font-size-header-sorter-icon: 12px;
@table-position-header-sorter-icon-up-top: -2px;
@table-position-header-sorter-icon-down-top: -3px;
@table-color-bg-filters-popup: var(--color-bg-5);
@table-color-border-filters-popup: var(--color-neutral-3);
@table-popup-min-width: 100px;
@table-popup-max-height: 200px;
@table-popup-border-radius: var(--border-radius-medium);
@table-shadow-left: inset 6px 0 8px -3px rgba(0, 0, 0, 0.15);
@table-shadow-right: inset -6px 0 8px -3px rgba(0, 0, 0, 0.15);
@table-size-shadow-wrapper-width: 10px;
@table-color-editable-body-cell-border: var(--color-white);
@table-spacing-pagination-margin: 12px;
@table-size-selection-col-width: 40px;
@table-size-expand-icon-col-width: 40px;
@table-size-body-min-width: 40px;
@table-color-body-background: var(--color-bg-2);
@table-color-bg-tfoot: var(--color-neutral-2);
@table-cls-tr: arco-table-tr;
@table-cls-th: arco-table-th;
@table-cls-td: arco-table-td;


/*********** tabs ***********/

@tabs-size-mini-font-size: 12px;
@tabs-size-small-font-size: 14px;
@tabs-size-default-font-size: 14px;
@tabs-size-large-font-size: 14px;
@tabs-size-mini-font-size_card: 12px;
@tabs-size-small-font-size_card: 14px;
@tabs-size-default-font-size_card: 14px;
@tabs-size-large-font-size_card: 14px;
@tabs-size-default-font-size_text: 14px;
@tabs-size-default-font-size_rounded: 14px;
@tabs-size-mini-font-size_capsule: 12px;
@tabs-size-small-font-size_capsule: 14px;
@tabs-size-default-font-size_capsule: 14px;
@tabs-size-large-font-size_capsule: 14px;
@tabs-size-mini-header-height_line: 32px;
@tabs-size-small-header-height_line: 36px;
@tabs-size-default-header-height_line: 40px;
@tabs-size-large-header-height_line: 44px;
@tabs-size-mini-header-height: 24px;
@tabs-size-small-header-height: 28px;
@tabs-size-default-header-height: 32px;
@tabs-size-large-header-height: 36px;
@tabs-size-mini-header-height_capsule: 24px;
@tabs-size-small-header-height_capsule: 28px;
@tabs-size-default-header-height_capsule: 32px;
@tabs-size-large-header-height_capsule: 36px;
@tabs-size-default-header-height_text: 32px;
@tabs-size-default-header-height_rounded: 32px;
@tabs-padding-title-text-vertical: 1px;
@tabs-padding-title-text-horizontal: 8px;
@tabs-color-title-text: var(--color-text-2);
@tabs-color-title-text_active: rgb(var(--primary-6));
@tabs-color-title-text_hover: var(--color-text-2);
@tabs-color-title-text_disabled: var(--color-text-4);
@tabs-color-title-text_disabled_active: var(--color-primary-light-3);
@tabs-line-size-header-border: 1px;
@tabs-line-color-header-border: var(--color-neutral-3);
@tabs-line-size-ink-stroke: 2px;
@tabs-line-color-ink-bg: rgb(var(--primary-6));
@tabs-line-color-ink-bg_disabled: var(--color-primary-light-3);
@tabs-line-font-title-text-weight_active: 500;
@tabs-line-margin-title-horizontal: 32px;
@tabs-line-margin-title-horizontal_first: 16px;
@tabs-line-margin-title-vertical: 12px;
@tabs-line-padding-title-horizontal_vertical: 20px;
@tabs-line-color-title-bg: transparent;
@tabs-line-color-title-bg_active: transparent;
@tabs-line-color-title-bg_hover: var(--color-fill-2);
@tabs-line-font-title-text-weight_hover: 400;
@tabs-line-border-radius: var(--border-radius-small);
@tabs-card-border-width: 1px;
@tabs-card-color-title-border: var(--color-neutral-3);
@tabs-card-padding-title-horizontal: 16px;
@tabs-card-padding-title-right_editable: 12px;
@tabs-card-border-radius: var(--border-radius-small);
@tabs-card-color-title-bg: transparent;
@tabs-card-color-title-bg_hover: var(--color-fill-3);
@tabs-card-color-title-bg_disabled: transparent;
@tabs-card-color-title-bg_active: transparent;
@tabs-card-border-content-width: 1px;
@tabs-card-gutter-spacing-horizontal: 4px;
@tabs-card-gutter-color-title-bg: var(--color-fill-1);
@tabs-card-gutter-color-title-bg_hover: var(--color-fill-3);
@tabs-card-gutter-color-title-bg_active: transparent;
@tabs-card-gutter-color-title-bg_disabled: var(--color-fill-1);
@tabs-text-size-separator-height: 12px;
@tabs-text-size-separator-width: 2px;
@tabs-text-color-separator-bg: var(--color-fill-3);
@tabs-text-margin-title-horizontal: 8px;
@tabs-text-color-title-bg: transparent;
@tabs-text-color-title-bg_active: transparent;
@tabs-text-color-title-bg_disabled: transparent;
@tabs-text-color-title-bg_disabled_active: var(--color-primary-light-3);
@tabs-text-color-title-bg_hover: var(--color-fill-2);
@tabs-rounded-padding-title-horizontal: 16px;
@tabs-rounded-margin-title-horizontal: 12px;
@tabs-rounded-color-title-bg: transparent;
@tabs-rounded-color-title-bg_active: var(--color-fill-2);
@tabs-rounded-color-title-bg_disabled: transparent;
@tabs-rounded-color-title-bg_hover: var(--color-fill-2);
@tabs-capsule-color-header-bg: var(--color-fill-2);
@tabs-capsule-margin-title-horizontal: 3px;
@tabs-capsule-padding-title-horizontal: 12px;
@tabs-capsule-padding-header-vertical: 3px;
@tabs-capsule-padding-header-horizontal: 3px;
@tabs-capsule-border-header-radius: var(--border-radius-small);
@tabs-capsule-border-title-radius: var(--border-radius-small);
@tabs-capsule-color-title-bg: transparent;
@tabs-capsule-color-title-bg_active: var(--color-bg-2);
@tabs-capsule-color-title-bg_hover: var(--color-bg-2);
@tabs-capsule-size-separator-width: 1px;
@tabs-capsule-size-separator-height: 14px;
@tabs-capsule-color-separator-bg: var(--color-fill-3);
@tabs-margin-close-icon-left: 8px;
@tabs-size-icon: 12px;
@tabs-size-icon-bg: 16px;
@tabs-card-color-close-icon-bg_hover: var(--color-fill-4);
@tabs-margin-add-icon-left: 8px;
@tabs-color-icon: var(--color-text-2);
@tabs-color-icon_disabled: var(--color-text-4);
@tabs-spacing-nav-icon-header: 6px;
@tabs-padding-header-wrapper-horizontal: 10px;
@tabs-padding-header-wrapper-vertical: 6px;
@tabs-content-padding: 16px;
@tabs-prefix-cls: arco-tabs;
@tabs-prefix-cls-vertical: arco-tabs-vertical;


/*********** sizes ***********/

@sizes: mini, small, large;


/*********** tag ***********/

@tag-border-width: 1px;
@tag-border-type: solid;
@tag-padding-horizontal: 8px;
@tag-padding-vertical: 0;
@tag-icon-margin-right: 4px;
@tag-text-font-weight: 500;
@tag-border-radius: var(--border-radius-small);
@tag-size-small: 20px;
@tag-size-default: 24px;
@tag-size-medium: 28px;
@tag-size-large: 32px;
@tag-size-small-font-size: 12px;
@tag-size-default-font-size: 12px;
@tag-size-medium-font-size: 14px;
@tag-size-large-font-size: 14px;
@tag-color-bg-not-checked_hover: var(--color-fill-2);
@tag-custom-color-text: var(--color-white);
@tag-custom-color-icon-bg_hover: rgba(255, 255, 255, 0.2);
@tag-default-color-bg: var(--color-fill-2);
@tag-default-color-bg_hover: var(--color-fill-3);
@tag-default-color-icon: var(--color-text-2);
@tag-default-color-text: var(--color-text-1);
@tag-default-color-border: transparent;
@tag-default-color-border_hover: transparent;
@tag-red-color-bg: rgb(var(--red-1));
@tag-red-color-bg_hover: rgb(var(--red-2));
@tag-red-color-border: transparent;
@tag-red-color-border_hover: transparent;
@tag-red-color-icon: rgb(var(--red-6));
@tag-red-color-icon-bg_hover: rgb(var(--red-2));
@tag-red-color-text: rgb(var(--red-6));
@tag-orangered-color-bg: rgb(var(--orangered-1));
@tag-orangered-color-bg_hover: rgb(var(--orangered-2));
@tag-orangered-color-border: transparent;
@tag-orangered-color-border_hover: transparent;
@tag-orangered-color-icon: rgb(var(--orangered-6));
@tag-orangered-color-icon-bg_hover: rgb(var(--orangered-2));
@tag-orangered-color-text: rgb(var(--orangered-6));
@tag-orange-color-bg: rgb(var(--orange-1));
@tag-orange-color-bg_hover: rgb(var(--orange-2));
@tag-orange-color-border: transparent;
@tag-orange-color-border_hover: transparent;
@tag-orange-color-icon: rgb(var(--orange-6));
@tag-orange-color-icon-bg_hover: rgb(var(--orange-2));
@tag-orange-color-text: rgb(var(--orange-6));
@tag-gold-color-bg: rgb(var(--gold-1));
@tag-gold-color-bg_hover: rgb(var(--gold-3));
@tag-gold-color-border: transparent;
@tag-gold-color-border_hover: transparent;
@tag-gold-color-icon: rgb(var(--gold-6));
@tag-gold-color-icon-bg_hover: rgb(var(--gold-2));
@tag-gold-color-text: rgb(var(--gold-6));
@tag-lime-color-bg: rgb(var(--lime-1));
@tag-lime-color-bg_hover: rgb(var(--lime-2));
@tag-lime-color-border: transparent;
@tag-lime-color-border_hover: transparent;
@tag-lime-color-icon: rgb(var(--lime-6));
@tag-lime-color-icon-bg_hover: rgb(var(--lime-2));
@tag-lime-color-text: rgb(var(--lime-6));
@tag-green-color-bg: rgb(var(--green-1));
@tag-green-color-bg_hover: rgb(var(--green-2));
@tag-green-color-border: transparent;
@tag-green-color-border_hover: transparent;
@tag-green-color-icon: rgb(var(--green-6));
@tag-green-color-icon-bg_hover: rgb(var(--green-2));
@tag-green-color-text: rgb(var(--green-6));
@tag-cyan-color-bg: rgb(var(--cyan-1));
@tag-cyan-color-bg_hover: rgb(var(--cyan-2));
@tag-cyan-color-border: transparent;
@tag-cyan-color-border_hover: transparent;
@tag-cyan-color-icon: rgb(var(--cyan-6));
@tag-cyan-color-icon-bg_hover: rgb(var(--cyan-2));
@tag-cyan-color-text: rgb(var(--cyan-6));
@tag-blue-color-bg: rgb(var(--blue-1));
@tag-blue-color-bg_hover: rgb(var(--blue-2));
@tag-blue-color-border: transparent;
@tag-blue-color-border_hover: transparent;
@tag-blue-color-icon: rgb(var(--blue-6));
@tag-blue-color-icon-bg_hover: rgb(var(--blue-2));
@tag-blue-color-text: rgb(var(--blue-6));
@tag-arcoblue-color-bg: rgb(var(--arcoblue-1));
@tag-arcoblue-color-bg_hover: rgb(var(--arcoblue-2));
@tag-arcoblue-color-border: transparent;
@tag-arcoblue-color-border_hover: transparent;
@tag-arcoblue-color-icon: rgb(var(--arcoblue-6));
@tag-arcoblue-color-icon-bg_hover: rgb(var(--arcoblue-2));
@tag-arcoblue-color-text: rgb(var(--arcoblue-6));
@tag-purple-color-bg: rgb(var(--purple-1));
@tag-purple-color-bg_hover: rgb(var(--purple-2));
@tag-purple-color-border: transparent;
@tag-purple-color-border_hover: transparent;
@tag-purple-color-icon: rgb(var(--purple-6));
@tag-purple-color-icon-bg_hover: rgb(var(--purple-2));
@tag-purple-color-text: rgb(var(--purple-6));
@tag-pinkpurple-color-bg: rgb(var(--pinkpurple-1));
@tag-pinkpurple-color-bg_hover: rgb(var(--pinkpurple-2));
@tag-pinkpurple-color-border: transparent;
@tag-pinkpurple-color-border_hover: rgb(var(--pinkpurple-2));
@tag-pinkpurple-color-icon: transparent;
@tag-pinkpurple-color-icon-bg_hover: rgb(var(--pinkpurple-2));
@tag-pinkpurple-color-text: rgb(var(--pinkpurple-6));
@tag-magenta-color-bg: rgb(var(--magenta-1));
@tag-magenta-color-bg_hover: rgb(var(--magenta-2));
@tag-magenta-color-border: transparent;
@tag-magenta-color-border_hover: transparent;
@tag-magenta-color-icon: rgb(var(--magenta-6));
@tag-magenta-color-icon-bg_hover: rgb(var(--magenta-2));
@tag-magenta-color-text: rgb(var(--magenta-6));
@tag-gray-color-bg: rgb(var(--gray-2));
@tag-gray-color-bg_hover: rgb(var(--gray-3));
@tag-gray-color-border: transparent;
@tag-gray-color-border_hover: transparent;
@tag-gray-color-icon: rgb(var(--gray-6));
@tag-gray-color-icon-bg_hover: rgb(var(--gray-3));
@tag-gray-color-text: rgb(var(--gray-6));
@tag-prefix-cls: arco-tag;


/*********** colors ***********/

@colors: red, orangered, orange, gold, lime, green, cyan, blue, arcoblue, purple, pinkpurple, magenta, gray;


/*********** timeline ***********/

@timeline-color-content-text: var(--color-text-1);
@timeline-color-label-text: var(--color-text-3);
@timeline-color-dot-bg: rgb(var(--primary-6));
@timeline-color-line-bg: var(--color-neutral-3);
@timeline-font-content-size: 14px;
@timeline-font-label-size: 12px;
@timeline-item-min-height: 78px;
@timeline-dot-size-width: 6px;
@timeline-dot-border-radius: var(--border-radius-circle);
@timeline-dot-border-width_hollow: 2px;
@timeline-color-dot-bg_hollow: var(--color-bg-2);
@timeline-horizontal-margin-content-spacing: 16px;
@timeline-horizontal-margin-line-left: 6px;
@timeline-horizontal-margin-line-right: 4px;
@timeline-vertical-margin-content-bottom: 4px;
@timeline-vertical-margin-content-left: 16px;
@timeline-vertical-margin-line-top: 4px;
@timeline-vertical-margin-line-bottom: 4px;
@timeline-size-line-width: 1px;
@timeline-prefix-cls: arco-timeline;
@timeline-item-prefix-cls: arco-timeline-item;


/*********** tooltip ***********/

@tooltip-padding-horizontal: 12px;
@tooltip-padding-vertical: 8px;
@tooltip-mini-padding-horizontal: 12px;
@tooltip-mini-padding-vertical: 4px;
@tooltip-mini-font-size: 14px;
@tooltip-font-size: 14px;
@tooltip-border-radius: var(--border-radius-small);
@tooltip-color-text: #fff;
@tooltip-color-bg: var(--color-tooltip-bg);
@tooltip-prefix-cls: arco-tooltip;


/*********** transfer ***********/

@transfer-width: 200px;
@transfer-height: 224px;
@transfer-height-title: 40px;
@transfer-height-footer: 40px;
@transfer-padding-horizontal-footer: 8px;
@transfer-border-color: var(--color-neutral-3);
@transfer-border-width: 1px;
@transfer-border-radius: var(--border-radius-small);
@transfer-font-size-header: 14px;
@transfer-font-size-header-unit: 12px;
@transfer-font-size-icon: 12px;
@transfer-font-weight-header: 500;
@transfer-color-text-header: var(--color-text-1);
@transfer-color-text-header-unit: var(--color-text-3);
@transfer-color-icon: var(--color-text-2);
@transfer-color-bg-icon: var(--color-fill-3);
@transfer-color-bg-header: var(--color-fill-1);
@transfer-search-padding-left: 12px;
@transfer-search-padding-right: 12px;
@transfer-search-padding-top: 8px;
@transfer-search-padding-bottom: 4px;
@transfer-item-color-bg_default: transparent;
@transfer-item-color-bg_hover: var(--color-fill-2);
@transfer-item-color-bg_disabled: transparent;
@transfer-item-color_default: var(--color-text-1);
@transfer-item-color_hover: var(--color-text-1);
@transfer-item-color_disabled: var(--color-text-4);
@transfer-item-height: 36px;
@transfer-item-padding-horizontal: 10px;
@transfer-item-font-size: 14px;
@transfer-item-draggable-height-gap: 2px;
@transfer-item-draggable-color-bg-gap: rgb(var(--primary-6));
@transfer-item-draggable-color-bg_dragging: var(--color-fill-1);
@transfer-item-draggable-color_dragging: var(--color-text-4);
@transfer-item-draggable-color_blink: var(--color-primary-light-1);
@transfer-pagination-width-input: 24px;
@transfer-pagination-gap-separator: 8px;
@transfer-operation-padding-horizontal: 20px;
@transfer-operation-gap-buttons: 12px;
@transfer-prefix-cls: arco-transfer;


/*********** tree ***********/

@tree-color-title-text: var(--color-text-1);
@tree-color-title-text_hover: var(--color-text-1);
@tree-color-title-text_active: rgb(var(--primary-6));
@tree-color-title-text_disabled: var(--color-text-4);
@tree-color-title-text_active_disabled: var(--color-primary-light-3);
@tree-color-title-bg_hover: var(--color-fill-2);
@tree-color-title-bg_highlight: var(--color-primary-light-1);
@tree-color-title-text_highlight: var(--color-text-1);
@tree-color-title-bg_dragging: var(--color-fill-1);
@tree-color-title-text_dragging: var(--color-text-4);
@tree-color-loading-icon: rgb(var(--primary-6));
@tree-color-switcher-icon: var(--color-text-2);
@tree-color-drag-icon: rgb(var(--primary-6));
@tree-node-border-radius: var(--border-radius-small);
@tree-margin-checkbox-right: 10px;
@tree-margin-switcher-icon-right: 10px;
@tree-margin-custom-icon-right: 10px;
@tree-padding-title-horizontal: 4px;
@tree-spacing-drag-icon-right: 12px;
@tree-spacing-drag-icon-text: 120px;
@tree-draggable-color-gap-bg: rgb(var(--primary-6));
@tree-draggable-size-gap-height: 2px;
@tree-showline-color-line-bg: var(--color-neutral-3);
@tree-showline-color-plus-icon-bg: var(--color-fill-2);
@tree-showline-size-plus-icon-stroke: 2px;
@tree-showline-size-plus-icon-width: 6px;
@tree-showline-size-line-width: 1px;
@tree-showline-style-line: solid;
@tree-showline-size-switcher-icon: 14px;
@tree-showline-spacing-line-vertical: 4px;
@tree-showline-border-plus-icon-radius: var(--border-radius-small);
@tree-size-mini-icon-size: 12px;
@tree-size-small-icon-size: 12px;
@tree-size-default-icon-size: 12px;
@tree-size-large-icon-size: 12px;
@tree-size-expand-icon-bg_hover: 16px;
@tree-size-mini-font-size: 12px;
@tree-size-small-font-size: 14px;
@tree-size-default-font-size: 14px;
@tree-size-large-font-size: 14px;
@tree-size-mini-line-height: 24px;
@tree-size-small-line-height: 28px;
@tree-size-default-line-height: 32px;
@tree-size-large-line-height: 36px;
@tree-prefix-cls: arco-tree;
@tree-node-prefix-cls: arco-tree-node;
@tree-select-padding-popup-left: 10px;
@tree-select-padding-popup-right: 4px;
@tree-select-padding-popup-vertical: 4px;
@tree-select-prefix-cls: arco-tree-select;


/*********** trigger ***********/

@trigger-color-arrow-bg: var(--color-bg-5);
@trigger-size-arrow-width: 8px;
@trigger-border-arrow-radius: 2px;
@trigger-prefix-cls: arco-trigger;


/*********** typography ***********/

@typography-font-size-h1: 36px;
@typography-font-size-h2: 32px;
@typography-font-size-h3: 28px;
@typography-font-size-h4: 24px;
@typography-font-size-h5: 20px;
@typography-font-size-h6: 16px;
@typography-heading-margin-top: 0;
@typography-heading-margin-bottom: 16px;
@typography-heading-font-weight: 500;
@typography-color-text: var(--color-text-1);
@typography-text-color-text-primary: rgb(var(--primary-6));
@typography-text-color-text-secondary: var(--color-text-2);
@typography-text-color-text-success: rgb(var(--success-6));
@typography-text-color-text-warning: rgb(var(--warning-6));
@typography-text-color-text-error: rgb(var(--danger-6));
@typography-text-color-text_disabled: var(--color-text-4);
@typography-text-color-bg-mark: rgb(var(--yellow-4));
@typography-text-font-weight-bold: 500;
@typography-text-color-code: var(--color-text-2);
@typography-text-color-code-border: var(--color-neutral-3);
@typography-text-color-code-bg: var(--color-neutral-2);
@typography-text-padding-code-vertical: 2px;
@typography-text-padding-code-horizontal: 8px;
@typography-text-margin-code-horizontal: 2px;
@typography-paragraph-line-height: 1.5715;
@typography-paragraph-line-height-close: 1.3;
@typography-operation-margin-left: 2px;
@typography-color-icon-copy: var(--color-text-2);
@typography-color-bg-icon-copy: transparent;
@typography-color-icon-copy_hover: var(--color-text-2);
@typography-color-bg-icon-copy_hover: var(--color-fill-2);
@typography-color-icon-copy_copied: rgb(var(--success-6));
@typography-color-icon-edit: var(--color-text-2);
@typography-color-bg-icon-edit: transparent;
@typography-color-icon-edit_hover: var(--color-text-2);
@typography-color-bg-icon-edit_hover: var(--color-fill-2);
@typography-color-expand-text: rgb(var(--primary-6));
@typography-color-expand-text_hover: rgb(var(--primary-5));
@typography-color-blockquote-border-width: 2px;
@typography-color-blockquote-border-left: var(--color-neutral-6);
@typography-color-blockquote-bg: var(--color-bg-2);
@typography-prefix-cls: arco-typography;


/*********** upload ***********/

@upload-tip-color-text: var(--color-text-3);
@upload-tip-margin-top: 4px;
@upload-tip-font-size: 12px;
@upload-list-margin-top: 24px;
@upload-picture-item-width: 80px;
@upload-picture-color-bg: var(--color-fill-2);
@upload-picture-border-radius: var(--border-radius-small);
@upload-picture-border-width: 1px;
@upload-picture-border-style: dashed;
@upload-picture-color-border: var(--color-neutral-3);
@upload-picture-color-border_disabled: var(--color-neutral-4);
@upload-picture-color-border_hover: var(--color-neutral-4);
@upload-picture-color-bg_hover: var(--color-fill-3);
@upload-picture-color-bg_disabled: var(--color-fill-1);
@upload-picture-color-text: var(--color-text-2);
@upload-picture-color-text_hover: var(--color-text-2);
@upload-picture-color-text_disabled: var(--color-text-4);
@upload-drag-font-size: 14px;
@upload-drag-border-radius: var(--border-radius-small);
@upload-drag-color-text: var(--color-text-1);
@upload-drag-border-style: dashed;
@upload-drag-border-width: 1px;
@upload-drag-padding-vertical: 50px;
@upload-drag-margin-icon-bottom: 24px;
@upload-drag-color-bg: var(--color-fill-1);
@upload-drag-color-bg_hover: var(--color-fill-3);
@upload-drag-color-bg_active: var(--color-primary-light-1);
@upload-drag-color-bg_disabled: var(--color-fill-1);
@upload-drag-color-border: var(--color-neutral-3);
@upload-drag-color-border_active: rgb(var(--primary-6));
@upload-drag-color-border_hover: var(--color-neutral-4);
@upload-drag-color-border_disabled: var(--color-text-4);
@upload-drag-color-icon: var(--color-text-2);
@upload-drag-color-icon_hover: var(--color-text-2);
@upload-drag-color-icon_active: rgb(var(--primary-6));
@upload-drag-color-text_hover: var(--color-text-1);
@upload-drag-color-text_active: var(--color-text-1);
@upload-drag-color-text_disabled: var(--color-text-4);
@upload-text-item-size-operation-icon: 12px;
@upload-text-item-margin-top: 12px;
@upload-text-item-font-size: 14px;
@upload-text-item-color-text: var(--color-text-1);
@upload-text-item-padding-left: 12px;
@upload-text-item-color-bg: var(--color-fill-1);
@upload-text-item-padding-vertical: 8px;
@upload-text-item-margin-remove-icon-left: 12px;
@upload-text-item-color-remove-icon: var(--color-text-2);
@upload-text-item-color-status-icon: var(--color-white);
@upload-text-item-color-file-icon_success: rgb(var(--success-6));
@upload-text-item-color-progress-bg_hover: rgba(var(--gray-10), 0.2);
@upload-text-item-color-progress-bg_hover_active: rgb(var(--primary-7));
@upload-text-item-size-file-icon: 16px;
@upload-text-item-margin-file-icon-right: 12px;
@upload-text-item-color-file-icon: rgb(var(--primary-6));
@upload-text-item-padding-right: 10px;
@upload-text-item-color-link: rgb(var(--link-6));
@upload-text-item-color-reupload-icon: rgb(var(--primary-6));
@upload-text-item-color-reupload-icon_hover: rgb(var(--primary-7));
@upload-text-item-size-status-icon: 12px;
@upload-text-item-color-error-icon: rgb(var(--danger-6));
@upload-text-item-color-success-icon: rgb(var(--success-6));
@upload-text-item-border-radius: var(--border-radius-small);
@upload-text-item-margin-error-icon-left: 4px;
@upload-text-item-margin-status-left: 10px;
@upload-text-item-thumbnail-width: 40px;
@upload-text-item-margin-thumbnail-right: 12px;
@upload-text-item-color-text_error: rgb(var(--danger-6));
@upload-text-item-color-text_success: unset;
@upload-text-item-color-text_uploading: unset;
@upload-picture-item-margin-preview-icon-right: 20px;
@upload-picture-item-size-width: 80px;
@upload-picture-item-border-radius: var(--border-radius-small);
@upload-picture-item-margin-right: 8px;
@upload-picture-item-margin-bottom: 8px;
@upload-picture-item-color-operation_bg: rgba(0, 0, 0, 0.5);
@upload-picture-item-color-operation-icon: var(--color-white);
@upload-picture-item-color-error-icon: var(--color-white);
@upload-picture-text-item-color-bg_error: var(--color-danger-light-1);
@upload-picture-text-item-color-text_error: rgb(var(--danger-6));
@upload-picture-text-item-color-text_success: unset;
@upload-picture-text-item-color-text_uploading: unset;
@upload-picture-text-item-padding-vertical: 8px;
@upload-drag-size-icon: 14px;
@upload-picture-item-size-mask-icon: 16px;
@upload-picture-item-size-error-icon: 26px;
@upload-text-item-size-reupload-icon: 14px;
@upload-text-item-size-success-icon: 14px;
@upload-text-item-size-error-icon: 14px;
@upload-picture-item-size-operation-icon: 14px;
@upload-prefix-cls: arco-upload;
@upload-drag-tip-margin-top: @spacing-2;
@arcoblue-6:#165DFF;