syntax = "proto3";

package sysinfos.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service SysinfoService {
  rpc GetSysinfo(GetSysinfoRequest) returns (GetSysinfoResponse) {
    option (google.api.http) = {
      get: "/api/v1/sysinfo"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get system information."
      description: "Get system information."
      tags: "System"
    };
  }
}

message GetSysinfoRequest {}

message GetSysinfoResponse {
  string message = 1;
  int32 code = 2;
  Sysinfo data = 3;
}

message OS {
  string type = 1;
  string version = 2;
  string arch = 3;
}

message CPU {
  int32 cores = 1;
  string usage = 2;
}

message Memory {
  string total = 1;
  string free = 2;
  string used = 3;
  string usage = 4;
}

message Disk {
  string name = 1;
  string total = 2;
  string free = 3;
  string used = 4;
  string usage = 5;
}

message Times {
  string up = 1;
}

message Network {
  string name = 1;
  string family = 2;
  string address = 3;
  string mac = 4;
  bool internal = 5;
}

message Server {
  string version = 1;
  int32 grpc_port = 2;
  int32 gateway_port = 3;
  int32 mqtt_tcp_port = 4;
  int32 mqtt_ws_port = 5;
}

message Sysinfo {
  OS os = 1;
  CPU cpu = 2;
  Memory memory = 3;
  repeated Disk disks = 4;
  Times times = 5;
  repeated Network networks = 6;
  Server server = 7;
}