syntax = "proto3";

package configs.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service ConfigService {
  rpc GetConfig(GetConfigRequest) returns (ConfigResponse) {
    option (google.api.http) = {
      get: "/api/v1/config"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get configuration"
      description: "Get configuration of the software"
      tags: "Config"
    };
  }
}

message GetConfigRequest {
}

message ConfigResponse {
  string message = 1;
  int32 code = 2;
  Config data = 3;
}

message Config {
  string cache_dir = 1;
  string storage_dir = 2;
  string database_file = 3;
  string version = 5;
}