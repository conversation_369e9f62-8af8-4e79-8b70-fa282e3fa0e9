import{R as e,r as t,e as a,j as o,_ as r,an as n,ao as s,F as i,ap as c,u as l,b as p,s as d,aq as m,B as u,k as f,l as h,i as b,ar as g}from"./index.bbeb3af6.js";import{C as y}from"./index.cf9faf12.js";import{T as O}from"./index.b90074ae.js";import{i as x,S as j,s as k}from"./search-form.a3922863.js";import{getColumns as S}from"./constants.448282a9.js";import"./index.9dee27c6.js";function v(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,o)}return a}function P(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?v(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):v(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function w(e,r){var n=t.exports.useContext(a).prefixCls,s=void 0===n?"arco":n,i=e.spin,c=e.className,l=P(P({"aria-hidden":!0,focusable:!1,ref:r},e),{},{className:"".concat(c?c+" ":"").concat(s,"-icon ").concat(s,"-icon-import")});return i&&(l.className="".concat(l.className," ").concat(s,"-icon-loading")),delete l.spin,delete l.isIcon,o("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...l,children:o("path",{d:"m27.929 33.072-9.071-9.07 9.07-9.072M43 24H19m12 17H7V7h24"})})}var z=e.forwardRef(w);z.defaultProps={isIcon:!0},z.displayName="IconImport";var C=z;const $=e=>{const{backup:a,requiredPermissions:r,oneOfPerm:c}=e,l=n((e=>e.userInfo));return t.exports.useMemo((()=>s({requiredPermissions:r,oneOfPerm:c},l.permissions)),[c,r,l.permissions])?o(i,{children:N(e.children)}):a?o(i,{children:N(a)}):null};function N(t){return e.isValidElement(t)?t:o(i,{children:t})}function I(){const e=c(),a=l(x),r=async(t,r)=>{switch(r){case"upgrade":e.push({pathname:"/main/store-upgrade-form",state:t});break;case"update":e.push({pathname:"/main/store-update-form",state:t});break;case"delete":f.confirm({title:a["store.tables.main.modal.delete.title"],content:a["store.tables.main.modal.delete.content"],onOk:()=>{z(!0),h(`/api/v1/packages/${t.id}`,{method:"delete"}).then((()=>{b.success(a["store.tables.main.modal.delete.operation.success"]),D()})).catch((()=>{b.error(a["store.tables.main.modal.delete.operation.fail"])})).finally((()=>{z(!1)}))}});break;default:f.confirm({title:a[`store.tables.main.modal.${r}.title`],content:a[`store.tables.main.modal.${r}.content`],onOk:()=>{z(!0),h("/api/v1/tasks",{method:"post",data:{name:r,packageId:t.id},timeout:60*("export"===r?5:1)*1e3}).then((e=>{const t=e&&e.data;if(t||t.data){if("failed"===t.data.state)b.error(a[`store.tables.main.modal.${r}.operation.fail`]);else{if("export"===r){const e=`${Date.now()}`,r=t.data.content;g.info({id:e,title:a["store.notifications.export.title"],content:`${a["store.notifications.export.location"]}: ${r}`,duration:0,btn:p("span",{children:[o(u,{type:"secondary",size:"small",onClick:()=>g.remove(e),style:{margin:"0 12px"},children:a["store.const.cancel"]}),o(u,{type:"primary",size:"small",onClick:()=>g.remove(e),children:a["store.const.ok"]})]})})}b.success(a[`store.tables.main.modal.${r}.operation.success`])}D()}})).catch((()=>{b.error(a[`store.tables.main.modal.${r}.operation.fail`])})).finally((()=>{z(!1)}))}})}},n=t.exports.useMemo((()=>S(a,r)),[a]),[s,i]=t.exports.useState([]),[v,P]=t.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[w,z]=t.exports.useState(!0),[N,I]=t.exports.useState({});function D(){const{current:e,pageSize:t}=v;z(!0),h("/api/v1/packages",{data:{page:e,size:t,...N}}).then((a=>{const o=a&&a.data;(o||o.data||o.data.content)&&(i(o.data.content),P({...v,current:e,pageSize:t,total:o.data.total}))})).finally((()=>{z(!1)}))}return t.exports.useEffect((()=>{D()}),[v.current,v.pageSize,JSON.stringify(N)]),p(y,{children:[o(j,{loading:w,onSearch:function(e){P({...v,current:1}),I(e)}}),o($,{requiredPermissions:[{resource:"menu.main.store",actions:["write"]}],children:o("div",{className:k["button-group"],children:o(d,{children:o(m,{to:"/main/store-import-form",children:o(u,{loading:w,type:"primary",icon:o(C,{}),children:a["store.tables.main.operation.import"]})})})})}),o(O,{rowKey:"id",loading:w,onChange:function({current:e,pageSize:t}){P({...v,current:e,pageSize:t})},pagination:v,columns:n,data:s})]})}export{I as default};
