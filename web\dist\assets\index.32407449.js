import{ap as e,aM as a,u as t,r,j as n,b as o,aq as s,B as i,af as l,Q as c,i as m,s as d,k as p,l as u}from"./index.e8bac691.js";import{I as f}from"./index.0218e733.js";import{U as h}from"./index.16cc9450.js";import{C as g}from"./index.a97db34b.js";import"./index.4d436df5.js";const k={"en-US":{"form.name.label":"Name","form.name.required":"Please enter the name","form.name.placeholder":"Please enter the name","form.attachment.label":"Attachment","form.attachment.required":"Please upload the attachment","form.attachment.support.tips":"Only zip can be uploaded","form.attachment.support-error.tips":"Unacceptable file type, please re-upload the specified file type!","form.upgrade.confirm.title":"Upgrade confirmation","form.upgrade.confirm.content":"Are you sure you want to upgrade?","form.upgrade.fail":"Upgrade failed","form.upgrade.success":"Upgrade successful","form.upgrade.success.title":"Upgrade successful","form.upgrade.success.desc":"Upgrade successful","form.upgrade.success.again":"Upgrade again","form.operations.back":"Back","form.operations.submit":"Submit"},"zh-CN":{"form.name.label":"名称","form.name.required":"请输入名称","form.name.placeholder":"请输入名称","form.attachment.label":"附件","form.attachment.required":"请上传附件","form.attachment.support.tips":"仅支持zip上传","form.attachment.support-error.tips":"不接受的文件类型，请重新上传指定文件类型！","form.upgrade.confirm.title":"升级确认","form.upgrade.confirm.content":"是否确认升级？","form.upgrade.fail":"升级失败","form.upgrade.success":"升级成功","form.upgrade.success.title":"升级成功","form.upgrade.success.desc":"升级成功","form.upgrade.success.again":"再次升级","form.operations.back":"返回","form.operations.submit":"提交"}};var b="_container_1klic_1",z="_wrapper_1klic_7",y="_form_1klic_13";const{useForm:S}=l;function x(){const x=e(),v=a(),C=t(k),[q,I]=r.exports.useState(!1),[U]=S(),w=async(e,a)=>{null!=e.chunkContent&&u("/api/v1/upload-tasks/"+e.id+"/chunks",{method:"post",data:{id:e.id,chunkIndex:e.chunkIndex,chunkSize:e.chunkSize,chunkContent:e.chunkContent,totalChunks:e.totalChunks,totalSize:e.totalSize},timeout:6e4}).then((e=>{const t=e&&e.data;t&&t.code,a&&a(t.data)})).catch((()=>{a&&a({state:"failed"})}))},_=async(e,a)=>{I(!0),(async(e,a)=>{u("/api/v1/upload-tasks",{method:"post",data:{packageName:e.name,packageVersion:e.version}}).then((e=>{const t=e&&e.data;t&&0===t.code?a&&a(t.data):m.error(C["form.upgrade.fail"])})).catch((()=>{m.error(C["form.upgrade.fail"])}))})(e,(t=>{const r=t.id,n=Math.ceil(e.totalSize/10485760);for(let o=0;o<n;o++){const t=o,s=10485760*o,i=Math.min(e.totalSize,s+10485760),l=a.slice(s,i),c=new FileReader;c.onloadend=function(){let a=c.result;a&&(a=String(a).replace(/^data:.+;base64,/,""),w({id:r,chunkIndex:t,chunkContent:a,chunkSize:i-s,totalChunks:n,totalSize:e.totalSize},(e=>{e&&e.state&&("finished"===e.state?u("/api/v1/tasks",{method:"post",data:{id:r,name:"upgrade",packageId:e.packageId}}).then((e=>{const a=e&&e.data;(a||a.data)&&("failed"===a.data.state?m.error(C["form.upgrade.fail"]):(m.success(C["form.upgrade.success"]),x.push({pathname:"/main/store"})))})).catch((()=>{m.error(C["form.upgrade.fail"])})).finally((()=>{I(!1)})):"failed"===e.state&&(m.error(C["form.upgrade.fail"]),I(!1)))})).catch((()=>{m.error(C["form.upgrade.fail"]),I(!1)})))},c.readAsDataURL(l)}}))};return r.exports.useEffect((()=>{if(v.state){I(!0);const e=v.state;U.setFieldsValue(e),I(!1)}else x.push({pathname:"/main/store"})}),[]),n("div",{className:b,children:o(g,{children:[n(s,{to:"/main/store",children:n(i,{loading:q,type:"primary",icon:n(f,{}),children:C["form.operations.back"]})}),n("div",{className:z,children:o(l,{form:U,className:y,children:[o(l.Item,{noStyle:!0,children:[n(l.Item,{label:C["form.name.label"],disabled:!0,required:!0,field:"name",rules:[{required:!0,message:C["form.name.required"]},{validator:(e,a)=>{/^[a-zA-Z0-9-_]{1,20}$/g.test(e)||a(C["form.name.placeholder"])}}],children:n(c,{placeholder:C["form.name.placeholder"]})}),n(l.Item,{label:C["form.attachment.label"],required:!0,field:"attachment",rules:[{required:!0,message:C["form.attachment.required"]}],children:n(h,{drag:!0,accept:".zip",autoUpload:!1,limit:1,onDrop:e=>{((e,a)=>{if(a&&e){const t=Array.isArray(a)?a:a.split(",").map((e=>e.trim())).filter((e=>e)),r=e.name.indexOf(".")>-1?e.name.split(".").pop():"";return t.some((a=>{const t=a&&a.toLowerCase(),n=(e.type||"").toLowerCase();if(t===n)return!0;if(new RegExp("/*").test(t)){const e=new RegExp("/.*$");return n.replace(e,"")===t.replace(e,"")}return!!new RegExp("..*").test(t)&&t===`.${r&&r.toLowerCase()}`}))}return!!e})(e.dataTransfer.files[0],["application/zip",".zip"])||m.info(C["form.attachment.support-error.tips"])},tip:C["form.attachment.support.tips"]})})]}),n(l.Item,{label:" ",children:n(d,{children:n(i,{loading:q,type:"primary",size:"large",onClick:async()=>{try{await U.validate();const e=U.getFields(),a={name:null,version:null,description:null,size:null,location:null,timestamp:null,chunkIndex:null,chunkContent:null,chunkSize:null,totalChunks:null,totalSize:null};if(e.attachment&&e.attachment.length>0){const t=e.attachment[0].originFile;e.name&&e.name.length>0?a.name=e.name:a.name=t.name.split(".")[0],e.version&&e.version.length>0?a.version=e.version:a.version=String(t.lastModified),a.totalSize=t.size,p.confirm({title:C["form.upgrade.confirm.title"],content:C["form.upgrade.confirm.content"],onOk:()=>{_(a,t)}})}}catch(e){}},children:C["form.operations.submit"]})})})]})})]})})}export{x as default};
