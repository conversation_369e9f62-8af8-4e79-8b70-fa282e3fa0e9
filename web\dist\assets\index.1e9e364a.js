import{u as a,r as e,b as t,j as s,i as o,k as n,l as i}from"./index.6227d37e.js";import{C as r}from"./index.a7402227.js";import{T as d}from"./index.5990d978.js";import{i as c,S as l}from"./search-form.bb0f2e47.js";import{getColumns as m}from"./constants.9f8b1a72.js";import"./index.be17d0db.js";function p(){const p=a(c),f=async(a,e)=>{if("delete"===e)n.confirm({title:p["patch.tables.main.modal.delete.title"],content:p["patch.tables.main.modal.delete.content"],onOk:()=>{g(!0),i("/api/v1/patches",{method:"delete",data:{name:a.name,packageName:a.packageName}}).then((()=>{o.success(p["patch.tables.main.modal.delete.operation.success"]),y()})).catch((()=>{o.error(p["patch.tables.main.modal.delete.operation.fail"])})).finally((()=>{g(!1)}))}});else o.warning(p["patch.tables.main.operation.no-support"])},u=e.exports.useMemo((()=>m(p,f)),[p]),[h,b]=e.exports.useState([]),[x,g]=e.exports.useState(!0),[j,S]=e.exports.useState({});function y(){g(!0),i("/api/v1/patches",{data:{...j}}).then((a=>{const e=a&&a.data;(e||e.data||e.data)&&b(e.data)})).finally((()=>{g(!1)}))}return e.exports.useEffect((()=>{y()}),[JSON.stringify(j)]),t(r,{children:[s(l,{loading:x,onSearch:function(a){S(a)}}),s(d,{rowKey:"id",loading:x,columns:u,data:h,pagination:!1})]})}export{p as default};
