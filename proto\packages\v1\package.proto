syntax = "proto3";

package packages.v1;

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service PackageService {
  rpc ListPackages(ListPackagesRequest) returns (ListPackagesResponse) {
    option (google.api.http) = {
      get: "/api/v1/packages"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List packages"
      description: "List packages on the server."
      tags: "Packages"
    };
  }

  rpc ListPatchesByPackageId(Package) returns (ListPatchesResponse) {
    option (google.api.http) = {
      get: "/api/v1/packages/{id}/patches"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List patches by package id"
      description: "List patches by package id on the server."
      tags: "Packages"
    };
  }

  rpc SavePackage(Package) returns (PackageResponse) {
    option (google.api.http) = {
      post: "/api/v1/packages"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Save package"
      description: "Save a package on the server."
      tags: "Packages"
    };
  }

  rpc UploadPackage(UploadPackageRequest) returns (PackageResponse) {
    option (google.api.http) = {
      post: "/api/v1/packages/{id}/chunks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Upload package"
      description: "Upload a package to the server."
      tags: "Packages"
    };
  }

  rpc DownloadPackage(DownloadPackageRequest) returns (PackageResponse) {
    option (google.api.http) = {
      get: "/api/v1/packages/{name}"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Download package"
      description: "Download a package from the server."
      tags: "Packages"
    };
  }

  rpc GetPackageFile(Package) returns (stream FileResponse) {}

  rpc DeletePackage(Package) returns (PackageResponse) {
    option (google.api.http) = {
      delete: "/api/v1/packages/{id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete package"
      description: "Delete a package from the server."
      tags: "Packages"
    };
  }

  rpc GetPatchFile(Patch) returns (stream FileResponse) {}
}

message ListPackagesRequest {
  int32 total = 1;
  int32 page = 2;
  int32 size = 3;
  int32 id = 4;
  string name = 5;
  string version = 6;
}

message ListPackagesResponseData {
  int32 total = 1;
  int32 page = 2;
  int32 size = 3;
  repeated Package content = 4;
}

message ListPackagesResponse {
  string message = 1;
  int32 code = 2;
  ListPackagesResponseData data = 3;
}

message ListPatchesResponse {
  string message = 1;
  int32 code = 2;
  repeated string data = 3;
}

message PackageResponse {
  string message = 1;
  int32 code = 2;
  Package data = 3;
}

message DownloadPackageRequest {
  string name = 1;
  string version = 2;
}

message FileResponse {
  bytes data = 1;
}

message UploadPackageRequest {
  int32 id = 1;
  int32 chunk_index = 2;
  bytes chunk_content = 3;
  int32 chunk_size = 4;
  int32 total_chunks = 5;
  int32 merged = 6;
}

message Package {
  int32 id = 1;
  string name = 2;
  string version = 3;
  string description = 4;
  string type = 5;
  string state = 6;
  int32 size = 7;
  string uri = 8;
  string package_path = 9;
  string install_path = 10;
  string support = 11;
  int32 backed = 12;
  int32 installed = 13;
  int32 downloaded = 14;
  int32 upgradable = 15;
  string created_at = 16;
  string updated_at = 17;
}

message Patch {
  int32 id = 1;
  string name = 2;
}