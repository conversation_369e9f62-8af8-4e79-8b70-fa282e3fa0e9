import{ap as e,u as t,r,b as a,j as o,aq as i,B as n,af as m,Q as s,i as l,s as c,k as p,l as d}from"./index.fe31dd41.js";import{I as u}from"./index.ab111282.js";import{U as h}from"./index.50a69d84.js";import{C as F}from"./index.41001b26.js";import{P as f}from"./index.425243b9.js";import"./index.d78a069d.js";const k={"en-US":{"importForm.name.label":"Name","importForm.name.required":"Please enter the name","importForm.name.placeholder":"Please enter the name","importForm.attachment.label":"Attachment","importForm.attachment.required":"Please upload the attachment","importForm.attachment.support.tips":"Only zip can be uploaded","importForm.attachment.support-error.tips":"Unacceptable file type, please re-upload the specified file type!","importForm.publish.confirm.title":"Publish confirmation","importForm.publish.confirm.content":"Do you want to confirm the publication?","importForm.created.fail":"Failed to create","importForm.created.fail.already-exists":"Package already exists","importForm.created.fail.already-published":"This version of the package has been published","importForm.created.success":"Created successfully","importForm.created.success.title":"Created successfully","importForm.created.success.desc":"Package created successfully","importForm.created.success.again":"Create again","importForm.operations.back":"Back","importForm.operations.submit":"Submit"},"zh-CN":{"importForm.name.label":"名称","importForm.name.required":"请输入名称","importForm.name.placeholder":"请输入名称","importForm.attachment.label":"附件","importForm.attachment.required":"请上传附件","importForm.attachment.support.tips":"仅支持zip上传","importForm.attachment.support-error.tips":"不接受的文件类型，请重新上传指定文件类型！","importForm.publish.confirm.title":"发布确认","importForm.publish.confirm.content":"是否确认发布？","importForm.created.fail":"创建失败","importForm.created.fail.already-exists":"包已存在","importForm.created.fail.already-published":"此版本包已有过发布","importForm.created.success":"创建成功","importForm.created.success.title":"创建成功","importForm.created.success.desc":"包创建成功","importForm.created.success.again":"再次创建","importForm.operations.back":"返回","importForm.operations.submit":"提交"}};var b="_container_1klic_1",g="_wrapper_1klic_7",y="_form_1klic_13";const{useForm:z}=m;function x(){const x=e(),C=t(k),[S,w]=r.exports.useState(!1),[v]=z(),[q,I]=r.exports.useState(!1),[_,P]=r.exports.useState(null),j=e=>new Promise(((t,r)=>{const a=new FileReader;a.onloadend=()=>{a.error?r(a.error):t(String(a.result).replace(/^data:.+;base64,/,""))},a.readAsDataURL(e)})),N=e=>new Promise((t=>{null!=e.chunkContent&&d("/api/v1/upload-package-tasks/"+e.id+"/chunks",{method:"post",data:{id:e.id,chunkIndex:e.chunkIndex,chunkSize:e.chunkSize,chunkContent:e.chunkContent,totalChunks:e.totalChunks,totalSize:e.totalSize},timeout:18e4}).then((e=>{const r=e&&e.data;t(r.data)})).catch((()=>{t({state:"failed"})}))})),A=async(e,t)=>{w(!0),(async(e,t)=>{d("/api/v1/upload-package-tasks",{method:"post",data:{packageName:e.name,packageVersion:e.version}}).then((e=>{const r=e&&e.data;r&&0===r.code?t&&t(r.data):l.error(C["importForm.created.fail"])})).catch((()=>{l.error(C["importForm.created.fail"])}))})(e,(async r=>{const a=r.id;P(a),I(!0);const o=Math.ceil(e.totalSize/104857600);for(let i=0;i<o;i++){const r=104857600*i,n=Math.min(e.totalSize,r+104857600),m=t.slice(r,n),s=i,c=await j(m);if(!c)return l.error(C["importForm.created.fail"]),w(!1),void I(!1);const p=await N({id:a,chunkIndex:s,chunkContent:c,chunkSize:n-r,totalChunks:o,totalSize:e.totalSize});if(p&&p.state){if("finished"===p.state)break;if("failed"===p.state){l.error(C["importForm.created.fail"]),w(!1),I(!1);break}}}w(!1)}))};return a("div",{className:b,children:[a(F,{children:[o(i,{to:"/main/store",children:o(n,{loading:S,type:"primary",icon:o(u,{}),children:C["importForm.operations.back"]})}),o("div",{className:g,children:a(m,{form:v,className:y,children:[a(m.Item,{noStyle:!0,children:[o(m.Item,{label:C["importForm.name.label"],required:!0,field:"name",rules:[{required:!0,message:C["importForm.name.required"]},{validator:(e,t)=>{/^[a-zA-Z0-9-_]{1,20}$/g.test(e)||t(C["importForm.name.placeholder"])}}],children:o(s,{placeholder:C["importForm.name.placeholder"]})}),o(m.Item,{label:C["importForm.attachment.label"],required:!0,field:"attachment",rules:[{required:!0,message:C["importForm.attachment.required"]}],children:o(h,{drag:!0,accept:".zip",autoUpload:!1,limit:1,onDrop:e=>{((e,t)=>{if(t&&e){const r=Array.isArray(t)?t:t.split(",").map((e=>e.trim())).filter((e=>e)),a=e.name.indexOf(".")>-1?e.name.split(".").pop():"";return r.some((t=>{const r=t&&t.toLowerCase(),o=(e.type||"").toLowerCase();if(r===o)return!0;if(new RegExp("/*").test(r)){const e=new RegExp("/.*$");return o.replace(e,"")===r.replace(e,"")}return!!new RegExp("..*").test(r)&&r===`.${a&&a.toLowerCase()}`}))}return!!e})(e.dataTransfer.files[0],["application/zip",".zip"])||l.info(C["importForm.attachment.support-error.tips"])},tip:C["importForm.attachment.support.tips"]})})]}),o(m.Item,{label:" ",children:o(c,{children:o(n,{loading:S,type:"primary",size:"large",onClick:async()=>{try{await v.validate();const e=v.getFields(),t={name:null,version:null,description:null,size:null,location:null,timestamp:null,chunkIndex:null,chunkContent:null,chunkSize:null,totalChunks:null,totalSize:null};if(e.attachment&&e.attachment.length>0){const r=e.attachment[0].originFile;e.name&&e.name.length>0?t.name=e.name:t.name=r.name.split(".")[0],t.version=String(r.lastModified),t.totalSize=r.size,p.confirm({title:C["importForm.publish.confirm.title"],content:C["importForm.publish.confirm.content"],onOk:()=>{A(t,r)}})}}catch(e){}},children:C["importForm.operations.submit"]})})})]})})]}),o(f,{visible:q,taskId:_,title:"文件上传进度",onComplete:(e,t)=>{e?(l.success(C["importForm.created.success"]),setTimeout((()=>{x.push({pathname:"/main/store"})}),1e3)):l.error(t||C["importForm.created.fail"])},onCancel:()=>{I(!1),P(null)},autoClose:!1,showDetails:!0})]})}export{x as default};
