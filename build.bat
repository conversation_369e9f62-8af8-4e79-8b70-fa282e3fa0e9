@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

:: Set color and title
title AIS Server Build Script (Windows 64-bit)
color 0B

echo.
echo ================================================
echo        AIS Server Build Script (Windows 64-bit)
echo ================================================
echo.

:: Check if Go is installed
echo [INFO] Checking Go environment...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Go environment not detected!
    echo.
    echo Please install Go:
    echo 1. Visit https://golang.org/dl/
    echo 2. Download Windows 64-bit Go installer
    echo 3. Run installer and follow instructions
    echo 4. Reopen command prompt
    echo 5. Run this script again
    echo.
    echo Or use winget for quick install:
    echo winget install GoLang.Go
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Go environment check passed
go version
echo.

:: Set build parameters
set "APP_NAME=ais-server"
set "VERSION=1.1.2"
set "BUILD_TIME=%DATE% %TIME%"
set "OUTPUT_FILE=%APP_NAME%-%VERSION%-windows-amd64.exe"

echo [INFO] Build parameters:
echo   App name: %APP_NAME%
echo   Version: %VERSION%
echo   Target platform: Windows 64-bit (amd64)
echo   Output file: %OUTPUT_FILE%
echo   Build time: %BUILD_TIME%
echo.

:: Check dependencies
echo [INFO] Checking project files...
if not exist "go.mod" (
    echo [ERROR] go.mod file not found, please run this script in project root directory
    pause
    exit /b 1
)

if not exist "main.go" (
    echo [ERROR] main.go file not found
    pause
    exit /b 1
)

echo [SUCCESS] Project files check passed
echo.

:: Download dependencies
echo [INFO] Downloading project dependencies...
go mod download
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Dependencies download failed
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies download completed
echo.

:: Tidy dependencies
echo [INFO] Tidying project dependencies...
go mod tidy
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Dependencies tidy failed, continuing build...
)
echo.

:: Build application
echo [INFO] Starting Windows 64-bit build...
echo Executing: set GOOS=windows GOARCH=amd64 CGO_ENABLED=0 && go build -ldflags "-s -w" -o "%OUTPUT_FILE%"

set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0
go build -ldflags "-s -w -X 'main.Version=%VERSION%' -X 'main.BuildTime=%BUILD_TIME%'" -o "%OUTPUT_FILE%"
set "BUILD_RESULT=%ERRORLEVEL%"

if %BUILD_RESULT% NEQ 0 (
    echo.
    echo ================================================
    echo [FAILED] Build failed!
    echo ================================================
    echo Please check code errors and retry
    color 0C
    pause
    exit /b %BUILD_RESULT%
)

:: Check output file
if not exist "%OUTPUT_FILE%" (
    echo.
    echo ================================================
    echo [FAILED] Output file not generated!
    echo ================================================
    color 0C
    pause
    exit /b 1
)

:: Get file size
for %%A in ("%OUTPUT_FILE%") do set "FILE_SIZE=%%~zA"
set /a "FILE_SIZE_MB=%FILE_SIZE% / 1024 / 1024"

echo.
echo ================================================
echo [SUCCESS] Build completed!
echo ================================================
echo Output file: %OUTPUT_FILE%
echo File size: %FILE_SIZE% bytes (~%FILE_SIZE_MB% MB)
echo Build time: %BUILD_TIME%
echo Target platform: Windows 64-bit (amd64)
echo.

:: Copy to build directory
if not exist "build\ais-server" (
    mkdir "build\ais-server"
)
copy "%OUTPUT_FILE%" "build\ais-server\ais-server.exe" >nul
echo [INFO] Copied to build\ais-server\ais-server.exe
echo.

echo Usage:
echo   Test program: %OUTPUT_FILE% --help
echo   Install service: %OUTPUT_FILE% install
echo   Start service: %OUTPUT_FILE% start
echo.
color 0A

:: Ask if run test
set /p "RUN_TEST=Run program test? (y/n): "
if /i "%RUN_TEST%"=="y" (
    echo.
    echo [INFO] Running program test...
    "%OUTPUT_FILE%" --help
    echo.
)

echo Build script completed!
echo Window will close in 5 seconds...
timeout /t 5 /nobreak >nul