import{r as t,j as s,b as e,s as a,l as o}from"./index.fe31dd41.js";import r from"./os-status-cpu.9858231c.js";import n from"./os-status-memory.110b1e5e.js";import d from"./os-information.9309e4b2.js";import i from"./os-network.d972dd5d.js";import"./index.9905d9df.js";import"./index.41001b26.js";var c="_layout_khazc_1",m="_layout-content_khazc_7";function l(){const[l,u]=t.exports.useState({os:{type:"",version:"",arch:""},cpu:{cores:"",usage:""},memory:{total:"",free:"",used:"",usage:""},disk:{total:"",free:"",used:"",usage:""},times:{up:""},networks:[]});return t.exports.useEffect((()=>{o("/api/v1/sysinfo").then((t=>{const s=t&&t.data;if(!s&&!s.data)return;const e=[];for(const[,a]of Object.entries(s.data.networks)){const t="IPv4"===a.family,s=null!=a.mac&&""!==a.mac;t&&s&&e.push(a)}s.data.networks=e,u(s.data)}))}),[]),s("div",{children:s("div",{className:c,children:s("div",{className:m,children:e(a,{size:16,direction:"vertical",style:{width:"100%"},children:[s(d,{data:l}),s(r,{data:l}),s(n,{data:l}),null!=l.networks&&l.networks.length>0?s(i,{data:l}):null]})})})})}export{l as default};
