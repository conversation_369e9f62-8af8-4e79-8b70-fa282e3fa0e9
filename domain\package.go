package domain

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
	"gopkg.in/yaml.v3"
)

var PackageConfigScriptNames = struct {
	Health    string
	Backup    string
	Restore   string
	Start     string
	Stop      string
	Install   string
	Uninstall string
	Update    string
	Export    string
}{
	Health:    "health",
	Backup:    "backup",
	Restore:   "restore",
	Start:     "start",
	Stop:      "stop",
	Install:   "install",
	Uninstall: "uninstall",
	Update:    "update",
	Export:    "export",
}

type PackageConfig struct {
	Home   string          `json:"home"`
	Script map[string]bool `json:"script"`
}

type Package struct {
	Id          int32     `json:"id" gorm:"primary_key"`
	Name        string    `json:"name" gorm:"unique_index:idx_name_version"`
	Version     string    `json:"version" gorm:"unique_index:idx_name_version"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Size        int32     `json:"size"`
	State       string    `json:"state"`
	Uri         string    `json:"uri"`
	PackagePath string    `json:"package_path"`
	InstallPath string    `json:"install_path"`
	Support     string    `json:"support"`
	Backed      int32     `json:"backed"`
	Downloaded  int32     `json:"downloaded"`
	Installed   int32     `json:"installed"`
	Upgradable  int32     `json:"upgradable"`
	CreatedAt   time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`

	Config map[string]*PackageConfig `json:"config" gorm:"-"`
}

var PackageTypes = struct {
	SDK     string
	SERVICE string
	FILE    string
}{
	SDK:     "sdk",
	SERVICE: "svc",
	FILE:    "file",
}

var PackageStates = struct {
	Downloading string
	Downloaded  string
	Installing  string
	Installed   string
	Exited      string
	Running     string
	Unknown     string
}{
	Downloading: "downloading",
	Downloaded:  "downloaded",
	Installing:  "installing",
	Installed:   "installed",
	Exited:      "exited",
	Running:     "running",
	Unknown:     "unknown",
}

func (t *Package) CanBackup() bool {
	return t.IsInstalled() && t.GetConfig().Script[PackageConfigScriptNames.Backup]
}

func (t *Package) CanResotre() bool {
	return t.IsInstalled() && t.IsBacked() && t.GetConfig().Script[PackageConfigScriptNames.Restore]
}

func (t *Package) CanInstall() bool {
	return t.IsDownloaded() && t.GetConfig().Script[PackageConfigScriptNames.Install]
}

func (t *Package) CanUninstall() bool {
	return t.IsInstalled() && t.GetConfig().Script[PackageConfigScriptNames.Uninstall]
}

func (t *Package) CanExport() bool {
	return t.IsInstalled() && t.IsRunning() && t.GetConfig().Script[PackageConfigScriptNames.Export]
}

func (t *Package) GetConfig() *PackageConfig {
	if t.Config == nil {
		t.Load()
	}
	osType := runtime.GOOS
	var config *PackageConfig
	if t.Config != nil {
		config = t.Config[osType]
	}
	if config == nil {
		config = &PackageConfig{}
	}
	return config
}

func (t *Package) GetScriptFile(scriptName string, dir string) string {
	osType := runtime.GOOS
	var cmdFileSuffix string
	if OsType.Windows == osType {
		cmdFileSuffix = ".bat"
	} else {
		cmdFileSuffix = ".sh"
	}
	return filepath.Join(dir, osType+"."+scriptName+cmdFileSuffix)
}

func (t *Package) IsSDK() bool {
	return PackageTypes.SDK == t.Type
}

func (t *Package) IsService() bool {
	return PackageTypes.SERVICE == t.Type
}

func (t *Package) IsDownloading() bool {
	return PackageStates.Downloading == t.State && t.Downloaded != 1
}

func (t *Package) IsDownloaded() bool {
	return t.Downloaded == 1
}

func (t *Package) IsInstalling() bool {
	return PackageStates.Installing == t.State && t.Installed != 1
}

func (t *Package) IsInstalled() bool {
	return t.Installed == 1
}

func (t *Package) IsRunning() bool {
	return PackageStates.Running == t.State
}

func (t *Package) IsBacked() bool {
	return t.Backed == 1
}

func (t *Package) Merge(src *Package) {
	if src != nil {
		if src.Name != "" {
			t.Name = src.Name
		}
		if src.Version != "" {
			t.Version = src.Version
		}
		if src.Description != "" {
			t.Description = src.Description
		}
		if src.Type != "" {
			t.Type = src.Type
		}
		if src.Size != 0 {
			t.Size = src.Size
		}
		if src.State != "" {
			t.State = src.State
		}
		if src.Uri != "" {
			t.Uri = src.Uri
		}
		if src.PackagePath != "" {
			t.PackagePath = src.PackagePath
		}
		if src.InstallPath != "" {
			t.InstallPath = src.InstallPath
		}
		if src.Support != "" {
			t.Support = src.Support
		}
		if src.Backed != 0 {
			t.Backed = src.Backed
		}
		if src.Installed != 0 {
			t.Installed = src.Installed
		}
		if src.Downloaded != 0 {
			t.Downloaded = src.Downloaded
		}
		if src.Upgradable != 0 {
			t.Upgradable = src.Upgradable
		}
		if src.Config != nil {
			t.Config = src.Config
		}
	}
}

func (t *Package) ToVO() *packagesv1.Package {
	if t == nil {
		return nil
	}
	vo := &packagesv1.Package{
		Id:          t.Id,
		Name:        t.Name,
		Version:     t.Version,
		Description: t.Description,
		Type:        t.Type,
		Size:        t.Size,
		State:       t.State,
		Uri:         t.Uri,
		PackagePath: t.PackagePath,
		InstallPath: t.InstallPath,
		Support:     t.Support,
		Backed:      t.Backed,
		Installed:   t.Installed,
		Downloaded:  t.Downloaded,
		Upgradable:  t.Upgradable,
		CreatedAt:   t.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   t.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return vo
}

func (pkg *Package) Delete() error {
	if pkg.Uri != "" {
		if _, err := os.Stat(pkg.Uri); !os.IsNotExist(err) {
			err := Sheller.Remove(pkg.Uri)
			if err != nil {
				return fmt.Errorf("failed to remove %s, %s", pkg.Uri, err.Error())
			} else {
				slog.Info(fmt.Sprintf("Package(%s) remove uri(%s) success", pkg.Name, pkg.Uri))
			}
		}
	}
	if pkg.Name != "" {
		pkgDir, _ := pkg.GetPackageDir()
		if _, err := os.Stat(pkgDir); !os.IsNotExist(err) {
			err = Sheller.RemoveAll(pkgDir)
			if err != nil {
				return fmt.Errorf("failed to remove %s, %s", pkgDir, err.Error())
			} else {
				slog.Info(fmt.Sprintf("Package(%s) remove dir(%s) success", pkg.Name, pkgDir))
			}
		}
	}
	return DeleteByID(pkg.Id, pkg)
}

func (t *Package) Load() error {
	osType := runtime.GOOS
	if t.Config != nil && t.Config[osType] != nil {
		return nil
	}
	pkgDir, err := t.GetPackageDir()
	if err != nil {
		return err
	}
	confFile := filepath.Join(pkgDir, "ais-cli.yml")
	if _, err := os.Stat(confFile); os.IsNotExist(err) {
		return err
	}
	// 读取配置文件
	content, err := os.ReadFile(confFile)
	if err != nil {
		return err
	}
	pkg := &Package{}
	// 解析配置文件
	err = yaml.Unmarshal(content, &pkg)
	if err != nil {
		return err
	}
	t.Merge(pkg)
	return nil
}

func (t *Package) FindAndLoad(dir string) error {
	pkgDir := filepath.Join(dir, t.Name)
	if _, err := os.Stat(pkgDir); os.IsNotExist(err) {
		return fmt.Errorf("directory %s not exists", pkgDir)
	}
	confFile := filepath.Join(pkgDir, "ais-cli.yml")
	if _, err := os.Stat(confFile); os.IsNotExist(err) {
		return fmt.Errorf("file %s not exists", confFile)
	}
	// 读取配置文件
	content, err := os.ReadFile(confFile)
	if err != nil {
		return err
	}
	pkg := &Package{}
	// 解析配置文件
	err = yaml.Unmarshal(content, &pkg)
	if err != nil {
		return err
	}
	t.Merge(pkg)
	return nil
}

func (t *Package) GetAppDir() (string, error) {
	if t.InstallPath != "" {
		return t.InstallPath, nil
	}
	pkgConfig := t.GetConfig()
	pkgConfigHome := pkgConfig.Home
	if !filepath.IsAbs(pkgConfigHome) {
		appsDir, err := config.GetAppsDir()
		if err != nil {
			return "", err
		}
		if pkgConfigHome == "" {
			pkgConfigHome = t.Name
		}
		pkgConfigHome = filepath.Join(appsDir, pkgConfigHome)
	}
	if _, err := os.Stat(pkgConfigHome); os.IsNotExist(err) {
		return "", fmt.Errorf("directory %s not exists", pkgConfigHome)
	}
	t.InstallPath = pkgConfigHome
	return t.InstallPath, nil
}

func (pkg *Package) GetStoreDir() (string, error) {
	storeDir, _ := config.GetStoreDir()
	pkgDir := filepath.Join(storeDir, pkg.Name)
	if _, err := os.Stat(pkgDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(pkgDir, 0755)
		if err != nil {
			return "", fmt.Errorf("create %s error: %v", pkgDir, err)
		}
	}
	return pkgDir, nil
}

func (t *Package) GetPatchesDir() (string, error) {
	storeDir, err := t.GetStoreDir()
	if err != nil {
		return "", err
	}
	patchesDir := filepath.Join(storeDir, "patches")
	if _, err := os.Stat(patchesDir); os.IsNotExist(err) {
		err = os.Mkdir(patchesDir, 0755)
		if err != nil {
			return "", fmt.Errorf("create %s error: %v", patchesDir, err)
		}
	}
	return patchesDir, nil
}

func (pkg *Package) GetPackageDir() (string, error) {
	if pkg.PackagePath != "" {
		return pkg.PackagePath, nil
	}
	pkgsDir, err := config.GetPackagesDir()
	if err != nil {
		return "", err
	}
	pkgDir := filepath.Join(pkgsDir, pkg.Name)
	if _, err := os.Stat(pkgDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(pkgDir, 0755)
		if err != nil {
			return "", fmt.Errorf("create %s error: %v", pkgDir, err)
		}
	}
	pkg.PackagePath = pkgDir
	return pkgDir, nil
}

func (pkg *Package) GetLocation() (*string, error) {
	if pkg.Uri != "" {
		return &pkg.Uri, nil
	}
	pkgDir, err := pkg.GetStoreDir()
	if err != nil {
		return nil, err
	}
	pkg.Uri = filepath.Join(pkgDir, pkg.Version)
	return &pkg.Uri, nil
}

func (pkg *Package) DownloadFrom(addr string) error {
	if pkg.IsDownloading() {
		return nil
	}
	pkg.State = PackageStates.Downloading
	err := pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	storeDir, _ := config.GetStoreDir()
	storePkgDir := filepath.Join(storeDir, pkg.Name)
	if _, err := os.Stat(storePkgDir); os.IsNotExist(err) {
		// 创建目录
		err = os.Mkdir(storePkgDir, 0755)
		if err != nil {
			return fmt.Errorf("create %s error: %v", storePkgDir, err)
		}
	}
	filename := filepath.Join(storePkgDir, pkg.Version)
	if _, err := os.Stat(filename); !os.IsNotExist(err) {
		slog.Debug(fmt.Sprintf("[Package(%s):Download] Package(%s) is already downloaded", pkg.Name, pkg.Name))
		pkg.Downloaded = 1
		pkg.State = PackageStates.Downloaded
		pkg.Uri = filename
		pkgsDir, err := config.GetPackagesDir()
		if err != nil {
			return fmt.Errorf("failed to get packages dir, %s", err.Error())
		}
		pkgDir := filepath.Join(pkgsDir, pkg.Name)
		if _, err := os.Stat(pkgDir); os.IsNotExist(err) {
			err = pkg.UnzipTo(pkgsDir)
			if err != nil {
				return fmt.Errorf("unzip %s error: %v", filename, err)
			}
		}
		return nil
	}
	slog.Debug(fmt.Sprintf("[Package(%s):Download] Connecting to %s", pkg.Name, addr))
	ctx, cancel := context.WithTimeout(context.Background(), 3*60*time.Second)
	defer cancel()
	conn, err := lib.NewGPRCServerConnection(addr, false)
	if err != nil {
		return fmt.Errorf("connect to %s error: %v", addr, err)
	}
	defer conn.Close()
	slog.Debug(fmt.Sprintf("[Package(%s):Download] Connected to %s", pkg.Name, addr))
	client := packagesv1.NewPackageServiceClient(conn)
	c, err := client.GetPackageFile(ctx, &packagesv1.Package{
		Name:    pkg.Name,
		Version: pkg.Version,
	})
	if err != nil {
		return fmt.Errorf("download package(%s) error: %v", pkg.Name, err)
	}
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		return fmt.Errorf("open %s error: %v", filename, err)
	}
	writer := bufio.NewWriter(file)
	chunkIndex := 0
	for {
		resp, err := c.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("recv error: %v", err)
		}
		if resp.GetData() != nil {
			_, err = writer.Write(resp.GetData())
			if err != nil {
				return fmt.Errorf("write error: %v", err)
			}
			slog.Debug(fmt.Sprintf("[Package(%s):Download] Write chunk[%d] to %s", pkg.Name, chunkIndex, filename))
			chunkIndex++
		}
	}
	err = writer.Flush()
	if err != nil {
		return fmt.Errorf("flush %s error: %v", filename, err)
	}
	err = file.Close()
	if err != nil {
		return fmt.Errorf("close %s error: %v", filename, err)
	}
	pkg.Uri = filename
	err = pkg.Unzip()
	if err != nil {
		Sheller.Remove(filename)
		Sheller.RemoveAll(storePkgDir)
		return fmt.Errorf("unzip %s error: %v", filename, err)
	}
	pkg.Downloaded = 1
	pkg.State = PackageStates.Downloaded
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) DownloadPatchFrom(addr string, filename string) error {
	if !pkg.IsInstalled() {
		return fmt.Errorf("package is not installed")
	}
	slog.Debug(fmt.Sprintf("[Package(%s):Download patch] Connecting to %s", pkg.Name, addr))
	ctx, cancel := context.WithTimeout(context.Background(), 3*60*time.Second)
	defer cancel()
	conn, err := lib.NewGPRCServerConnection(addr, false)
	if err != nil {
		return fmt.Errorf("connect to %s error: %v", addr, err)
	}
	defer conn.Close()
	slog.Debug(fmt.Sprintf("[Package(%s):Download patch] Connected to %s", pkg.Name, addr))
	client := packagesv1.NewPackageServiceClient(conn)
	c, err := client.GetPatchFile(ctx, &packagesv1.Patch{
		Id:   pkg.Id,
		Name: filename,
	})
	if err != nil {
		return fmt.Errorf("download patch(%s) error: %v", filename, err)
	}
	patchesDir, err := pkg.GetPatchesDir()
	if err != nil {
		return fmt.Errorf("get patches dir error: %v", err)
	}
	patchFile := filepath.Join(patchesDir, filename)
	file, err := os.OpenFile(patchFile, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		return fmt.Errorf("open %s error: %v", patchFile, err)
	}
	writer := bufio.NewWriter(file)
	chunkIndex := 0
	for {
		resp, err := c.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("recv error: %v", err)
		}
		if resp.GetData() != nil {
			_, err = writer.Write(resp.GetData())
			if err != nil {
				return fmt.Errorf("write error: %v", err)
			}
			slog.Debug(fmt.Sprintf("[Package(%s):Download patch] Write chunk[%d] to %s", pkg.Name, chunkIndex, patchFile))
			chunkIndex++
		}
	}
	err = writer.Flush()
	if err != nil {
		return fmt.Errorf("flush %s error: %v", patchFile, err)
	}
	return nil
}

func (pkg *Package) Unzip() error {
	pkgsDir, err := config.GetPackagesDir()
	if err != nil {
		return fmt.Errorf("failed to get packages dir, %s", err.Error())
	}
	if pkg.Uri == "" {
		return fmt.Errorf("location is empty")
	}
	if _, err := os.Stat(pkg.Uri); os.IsNotExist(err) {
		return fmt.Errorf("file %s not exists", pkg.Uri)
	}
	return lib.Unzip(pkg.Uri, pkgsDir)
}

func (pkg *Package) UnzipTo(dir string) error {
	if pkg.Uri == "" {
		return fmt.Errorf("location is empty")
	}
	if _, err := os.Stat(pkg.Uri); os.IsNotExist(err) {
		return fmt.Errorf("file %s not exists", pkg.Uri)
	}
	return lib.Unzip(pkg.Uri, dir)
}

func (pkg *Package) ExecuteScriptFile(fileName string, params string) (bool, error) {
	osType := runtime.GOOS
	var cmdStr string
	if OsType.Windows == osType {
		cmdStr = fmt.Sprintf("cmd /c %s %s", fileName, params)
	} else {
		cmdStr = fmt.Sprintf("sh %s %s", fileName, params)
	}
	// 运行脚本
	output, err := lib.ExecuteScript(cmdStr)
	if !strings.Contains(fileName, PackageConfigScriptNames.Health) {
		slog.Debug(fmt.Sprintf("[Package(%s):ExecuteScript] Execute %s script with command: %s", pkg.Name, fileName, cmdStr))
		slog.Debug(fmt.Sprintf("[Package(%s):ExecuteScript] Execute %s script output: %s", pkg.Name, fileName, output))
	}
	if err != nil {
		return false, err
	}
	if strings.Contains(output, "false") {
		return false, nil
	}
	return true, nil
}

func (pkg *Package) ExecuteScript(scriptName string, cmdParams string) (bool, error) {
	osType := runtime.GOOS
	var shellBytes *[]byte
	shellBytesOfOS := shellBytesMap[osType]
	if shellBytesOfOS == nil {
		return false, fmt.Errorf("get shell bytes error")
	}
	shellBytes = (*shellBytesOfOS)[scriptName]
	if shellBytes == nil || len(*shellBytes) == 0 {
		return false, fmt.Errorf("get shell bytes error")
	}
	var cmdFileSuffix string
	if OsType.Windows == osType {
		cmdFileSuffix = ".bat"
	} else {
		cmdFileSuffix = ".sh"
	}
	// 将脚本写入到临时文件
	tmpfile, err := os.CreateTemp("", osType+"."+scriptName+".*"+cmdFileSuffix)
	if err != nil {
		return false, err
	}
	defer os.Remove(tmpfile.Name()) // 清理
	if _, err := tmpfile.Write(*shellBytes); err != nil {
		return false, err
	}
	if err := tmpfile.Close(); err != nil {
		return false, err
	}
	return pkg.ExecuteScriptFile(tmpfile.Name(), cmdParams)
}

func (pkg *Package) ExecuteWithDir(scriptName string, dir string) (bool, error) {
	return pkg.ExecuteWithDirAndExtra(scriptName, dir, nil)
}

func (pkg *Package) ExecuteWithDirAndExtra(scriptName string, dir string, extra *string) (bool, error) {
	err := pkg.Load()
	if err != nil {
		return false, fmt.Errorf("failed to load package config, %s", err.Error())
	}
	if extra == nil {
		extra = new(string)
		*extra = ""
	}
	osType := runtime.GOOS
	osArch := runtime.GOARCH
	var cmdParams string
	pkgDir, _ := pkg.GetPackageDir()
	cmdParams = fmt.Sprintf("%s %s %s %s %s %s %s", pkg.Name, pkg.Version, osType, osArch, pkgDir, strconv.FormatBool(pkg.IsSDK()), *extra)
	scriptFile := pkg.GetScriptFile(scriptName, dir)
	if _, err := os.Stat(scriptFile); os.IsNotExist(err) {
		if !pkg.IsService() && PackageConfigScriptNames.Health == scriptName {
			return true, nil
		}
		return pkg.ExecuteScript(scriptName, cmdParams)
	} else {
		return pkg.ExecuteScriptFile(scriptFile, cmdParams)
	}
}

func (pkg *Package) Execute(scriptName string) (bool, error) {
	appDir, err := pkg.GetAppDir()
	if err != nil {
		return false, fmt.Errorf("get package(%s) app dir failed, %s", pkg.Name, err.Error())
	}
	return pkg.ExecuteWithDir(scriptName, appDir)
}

func (pkg *Package) Install() error {
	if pkg.IsInstalling() {
		return nil
	}
	isInstalled := pkg.IsInstalled()
	appDir, err := pkg.GetAppDir()
	if err == nil && appDir != "" {
		if _, err := os.Stat(appDir); !os.IsNotExist(err) {
			slog.Warn(fmt.Sprintf("Package(%s) app dir %s exists", pkg.Name, appDir))
			pkg.InstallPath = appDir
			// isInstalled = true
		} else {
			isInstalled = false
		}
	}
	pkgConfig := pkg.GetConfig()
	if !isInstalled && pkg.CanInstall() {
		pkg.State = PackageStates.Installing
		err = pkg.Update()
		if err != nil {
			return fmt.Errorf("failed to update package, %s", err.Error())
		}
		pkgDir, err := pkg.GetPackageDir()
		if err != nil {
			return fmt.Errorf("failed to get package dir, %s", err.Error())
		}

		// 检查是否为ais-cell等大量碎片文件的包，使用优化安装方式
		if pkg.isFragmentedPackage() {
			slog.Info(fmt.Sprintf("[安装] 检测到碎片文件包，使用高性能安装: %s", pkg.Name))
			ok, err := pkg.installWithOptimization(pkgDir)
			if err != nil {
				return fmt.Errorf("failed to execute optimized install, %s", err.Error())
			}
			if !ok {
				return fmt.Errorf("execute optimized install failed")
			}
		} else {
			ok, err := pkg.ExecuteWithDir(PackageConfigScriptNames.Install, pkgDir)
			if err != nil {
				return fmt.Errorf("failed to execute install script, %s", err.Error())
			}
			if !ok {
				return fmt.Errorf("execute install script failed")
			}
		}
	}
	supports := make([]string, 0)
	for scriptName, support := range pkgConfig.Script {
		if support {
			supports = append(supports, scriptName)
		}
	}
	pkg.Support = strings.Join(supports, ",")
	pkg.InstallPath = pkgConfig.Home
	pkg.Installed = 1
	pkg.State = PackageStates.Installed
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) Uninstall() error {
	// 未安装的包不允许卸载
	if !pkg.IsInstalled() {
		return fmt.Errorf("package is not installed")
	}
	appDir, err := pkg.GetAppDir()
	if err != nil {
		slog.Warn(fmt.Sprintf("Package(%s) get app dir failed, %s", pkg.Name, err.Error()))
	} else if pkg.CanUninstall() {
		pkgDir, err := pkg.GetPackageDir()
		if err != nil {
			return fmt.Errorf("failed to get package dir, %s", err.Error())
		}
		if _, err := os.Stat(pkgDir); os.IsNotExist(err) {
			return fmt.Errorf("directory %s not exists", pkgDir)
		}
		ok, err := pkg.ExecuteWithDir(PackageConfigScriptNames.Uninstall, pkgDir)
		if err != nil {
			return fmt.Errorf("failed to execute uninstall script, %s", err.Error())
		}
		if !ok {
			return fmt.Errorf("execute uninstall script failed")
		}
		if appDir != "" {
			if _, err := os.Stat(appDir); !os.IsNotExist(err) {
				err = Sheller.RemoveAll(appDir)
				if err != nil {
					return fmt.Errorf("failed to remove %s, %s", appDir, err.Error())
				}
				slog.Debug(fmt.Sprintf("[Package(%s):Uninstall] Remove %s", pkg.Name, appDir))
			}
		}
	}
	pkg.Support = ""
	pkg.InstallPath = ""
	pkg.Installed = 0
	pkg.State = PackageStates.Downloaded
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) Health(isSave bool) error {
	if !pkg.IsInstalled() {
		return nil
	}
	ok, err := pkg.Execute(PackageConfigScriptNames.Health)
	if err != nil {
		return err
	}
	if ok {
		pkg.State = PackageStates.Running
	} else {
		pkg.State = PackageStates.Exited
	}
	if isSave {
		err = pkg.Update()
		if err != nil {
			return fmt.Errorf("failed to update package, %s", err.Error())
		}
	}
	return nil
}

func (pkg *Package) Start() error {
	if !pkg.IsInstalled() {
		return fmt.Errorf("package is not installed")
	}
	ok, err := pkg.Execute(PackageConfigScriptNames.Start)
	if err != nil {
		return err
	}
	if ok {
		pkg.State = PackageStates.Running
	} else {
		pkg.State = PackageStates.Unknown
	}
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) Stop() error {
	if !pkg.IsInstalled() {
		return fmt.Errorf("package is not installed")
	}
	ok, err := pkg.Execute(PackageConfigScriptNames.Stop)
	if err != nil {
		return err
	}
	if ok {
		pkg.State = PackageStates.Exited
	} else {
		pkg.State = PackageStates.Unknown
	}
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) Backup() error {
	if !pkg.CanBackup() {
		return nil
	}
	pkgDir, err := pkg.GetPackageDir()
	if err != nil {
		return fmt.Errorf("failed to get package dir, %s", err.Error())
	}
	ok, err := pkg.ExecuteWithDir(PackageConfigScriptNames.Backup, pkgDir)
	if err != nil {
		return fmt.Errorf("failed to execute script, %s", err.Error())
	}
	if !ok {
		return fmt.Errorf("execute script failed")
	}
	if ok {
		pkg.Backed = 1
	}
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) Restore() error {
	if !pkg.CanResotre() {
		return nil
	}
	pkgDir, err := pkg.GetPackageDir()
	if err != nil {
		return fmt.Errorf("failed to get package dir, %s", err.Error())
	}
	ok, err := pkg.ExecuteWithDir(PackageConfigScriptNames.Restore, pkgDir)
	if err != nil {
		return fmt.Errorf("failed to execute script, %s", err.Error())
	}
	if !ok {
		return fmt.Errorf("execute script failed")
	}
	if ok {
		pkg.Backed = 0
	}
	err = pkg.Update()
	if err != nil {
		return fmt.Errorf("failed to update package, %s", err.Error())
	}
	return nil
}

func (pkg *Package) UpdateFrom(file string) error {
	if !pkg.CanBackup() {
		return nil
	}
	pkgDir, err := pkg.GetPackageDir()
	if err != nil {
		return fmt.Errorf("failed to get package dir, %s", err.Error())
	}
	ok, err := pkg.ExecuteWithDirAndExtra(PackageConfigScriptNames.Update, pkgDir, &file)
	if err != nil {
		return fmt.Errorf("failed to execute script, %s", err.Error())
	}
	if !ok {
		return fmt.Errorf("execute script failed")
	}
	return nil
}

func (pkg *Package) UpgradeFrom(file string) error {
	fileStat, err := os.Stat(file)
	// 判断file是否存在
	if os.IsNotExist(err) {
		return fmt.Errorf("file not exists")
	}
	cacheDir, err := config.GetCacheDir()
	if err != nil {
		return err
	}
	tempDir := fmt.Sprintf("%s/%s_%d", cacheDir, pkg.Name, time.Now().UnixNano())
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		err = os.Mkdir(tempDir, 0755)
		if err != nil {
			return fmt.Errorf("create %s error: %v", tempDir, err)
		}
	}
	defer os.RemoveAll(tempDir)
	tempPkg := &Package{Name: pkg.Name, Uri: file, Size: int32(fileStat.Size())}
	err = tempPkg.UnzipTo(tempDir)
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Package(%d):UpgradeFrom] Unzip %s to %s", pkg.Id, file, tempDir))
	err = tempPkg.FindAndLoad(tempDir)
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Package(%d):UpgradeFrom] Load package config successful, %s %s", pkg.Id, pkg.Name, pkg.Version))
	if tempPkg.Name != pkg.Name {
		return fmt.Errorf("package not match")
	}
	if tempPkg.Version == pkg.Version {
		return fmt.Errorf("package version is the same")
	}
	tempPkgDir := filepath.Join(tempDir, pkg.Name)
	if _, err := os.Stat(tempPkgDir); os.IsNotExist(err) {
		tempPkgDir = tempDir
	}
	pkgDir, err := pkg.GetPackageDir()
	if err != nil {
		return err
	}
	// 覆盖文件夹完成升级
	lib.Copy(tempPkgDir, pkgDir)
	// 更新package
	pkg.Merge(tempPkg)
	if pkg.Type == "" {
		pkg.Type = PackageTypes.SERVICE
	}
	err = pkg.Update()
	if err != nil {
		return err
	}
	slog.Debug(fmt.Sprintf("[Package(%d):UpgradeFrom] Upgrade package %s %s successful", pkg.Id, pkg.Name, pkg.Version))
	return nil
}

func (pkg *Package) Export() error {
	if !pkg.CanExport() {
		return nil
	}
	pkgDir, err := pkg.GetPackageDir()
	if err != nil {
		return fmt.Errorf("failed to get package dir, %s", err.Error())
	}
	ok, err := pkg.ExecuteWithDirAndExtra(PackageConfigScriptNames.Export, pkgDir, nil)
	if err != nil {
		return fmt.Errorf("failed to execute script, %s", err.Error())
	}
	if !ok {
		return fmt.Errorf("execute script failed")
	}
	return nil
}

// isFragmentedPackage 检测是否为碎片文件包（大量小文件）
func (pkg *Package) isFragmentedPackage() bool {
	// 检查包名是否包含已知的碎片文件包标识
	packageName := strings.ToLower(pkg.Name)
	fragmentedPackages := []string{"ais-cell", "node_modules", "vendor"}

	for _, fragPkg := range fragmentedPackages {
		if strings.Contains(packageName, fragPkg) {
			return true
		}
	}

	// 如果有URI，检查ZIP文件结构
	if pkg.Uri != "" {
		if _, err := os.Stat(pkg.Uri); err == nil {
			return isFragmentedZipPackage(pkg.Uri)
		}
	}

	return false
}

// isFragmentedZipPackage 检测ZIP文件是否为碎片文件包
func isFragmentedZipPackage(zipPath string) bool {
	r, err := zip.OpenReader(zipPath)
	if err != nil {
		return false
	}
	defer r.Close()

	totalFiles := 0
	smallFiles := 0
	const smallFileThreshold = 1024 * 1024 // 1MB

	for _, f := range r.File {
		if !f.FileInfo().IsDir() {
			totalFiles++
			if f.UncompressedSize64 < smallFileThreshold {
				smallFiles++
			}
		}
	}

	// 如果小文件占比超过80%且总文件数超过1000，认为是碎片文件包
	if totalFiles > 1000 && float64(smallFiles)/float64(totalFiles) > 0.8 {
		return true
	}

	return false
}

// installWithOptimization 使用优化方式安装碎片文件包
func (pkg *Package) installWithOptimization(pkgDir string) (bool, error) {
	osType := runtime.GOOS
	osArch := runtime.GOARCH
	cmdParams := fmt.Sprintf("%s %s %s %s %s %s", pkg.Name, pkg.Version, osType, osArch, pkgDir, strconv.FormatBool(pkg.IsSDK()))

	switch osType {
	case "windows":
		return pkg.installWindowsOptimized(pkgDir, cmdParams)
	case "linux", "darwin":
		return pkg.installUnixOptimized(pkgDir, cmdParams)
	default:
		// 回退到标准安装方式
		return pkg.ExecuteWithDir(PackageConfigScriptNames.Install, pkgDir)
	}
}

// installWindowsOptimized Windows优化安装
func (pkg *Package) installWindowsOptimized(pkgDir string, cmdParams string) (bool, error) {
	// 使用高性能复制替代标准的robocopy
	pkgConfig := pkg.GetConfig()
	if pkgConfig.Home == "" {
		return false, fmt.Errorf("package home directory not configured")
	}

	srcPath := filepath.Join(pkgDir, fmt.Sprintf("%s-%s-%s-%s", pkg.Name, pkg.Version, runtime.GOOS, runtime.GOARCH))
	dstPath := pkgConfig.Home

	slog.Info(fmt.Sprintf("[优化安装] 开始高性能复制: %s -> %s", srcPath, dstPath))

	// 使用高性能目录复制
	err := lib.CopyDirOptimized(srcPath, dstPath, func(progress float64, message string) {
		slog.Info(fmt.Sprintf("[优化安装] %s 复制进度: %.1f%% - %s", pkg.Name, progress, message))
	})

	if err != nil {
		slog.Error(fmt.Sprintf("[优化安装] 高性能复制失败，回退到标准方式: %v", err))
		// 回退到标准安装脚本
		return pkg.ExecuteWithDir(PackageConfigScriptNames.Install, pkgDir)
	}

	// 设置环境变量
	return pkg.setWindowsEnvironment(cmdParams)
}

// installUnixOptimized Unix系统优化安装
func (pkg *Package) installUnixOptimized(pkgDir string, cmdParams string) (bool, error) {
	// 使用高性能复制替代标准的cp命令
	pkgConfig := pkg.GetConfig()
	if pkgConfig.Home == "" {
		return false, fmt.Errorf("package home directory not configured")
	}

	srcPath := filepath.Join(pkgDir, fmt.Sprintf("%s-%s-%s-%s", pkg.Name, pkg.Version, runtime.GOOS, runtime.GOARCH))
	dstPath := pkgConfig.Home

	slog.Info(fmt.Sprintf("[优化安装] 开始高性能复制: %s -> %s", srcPath, dstPath))

	// 使用高性能目录复制
	err := lib.CopyDirOptimized(srcPath, dstPath, func(progress float64, message string) {
		slog.Info(fmt.Sprintf("[优化安装] %s 复制进度: %.1f%% - %s", pkg.Name, progress, message))
	})

	if err != nil {
		slog.Error(fmt.Sprintf("[优化安装] 高性能复制失败，回退到标准方式: %v", err))
		// 回退到标准安装脚本
		return pkg.ExecuteWithDir(PackageConfigScriptNames.Install, pkgDir)
	}

	// 设置环境变量和权限
	return pkg.setUnixEnvironment(cmdParams)
}

// setWindowsEnvironment 设置Windows环境变量
func (pkg *Package) setWindowsEnvironment(cmdParams string) (bool, error) {
	// 这里可以添加Windows特定的环境变量设置逻辑
	// 暂时返回成功，实际环境变量设置可以通过注册表或setx命令完成
	slog.Info(fmt.Sprintf("[优化安装] Windows环境变量设置完成: %s", pkg.Name))
	return true, nil
}

// setUnixEnvironment 设置Unix环境变量
func (pkg *Package) setUnixEnvironment(cmdParams string) (bool, error) {
	// 这里可以添加Unix特定的环境变量设置逻辑
	// 暂时返回成功，实际环境变量设置可以通过修改shell配置文件完成
	slog.Info(fmt.Sprintf("[优化安装] Unix环境变量设置完成: %s", pkg.Name))
	return true, nil
}
