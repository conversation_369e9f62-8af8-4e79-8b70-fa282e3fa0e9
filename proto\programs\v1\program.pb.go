// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        (unknown)
// source: programs/v1/program.proto

package programsv1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{0}
}

func (x *CommonResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListProgramsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page  int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size  int32  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Id    int64  `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	Name  string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *ListProgramsRequest) Reset() {
	*x = ListProgramsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProgramsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProgramsRequest) ProtoMessage() {}

func (x *ListProgramsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProgramsRequest.ProtoReflect.Descriptor instead.
func (*ListProgramsRequest) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{1}
}

func (x *ListProgramsRequest) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListProgramsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProgramsRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListProgramsRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListProgramsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListProgramsResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int32      `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Page    int32      `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size    int32      `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Content []*Program `protobuf:"bytes,4,rep,name=content,proto3" json:"content,omitempty"`
}

func (x *ListProgramsResponseData) Reset() {
	*x = ListProgramsResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProgramsResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProgramsResponseData) ProtoMessage() {}

func (x *ListProgramsResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProgramsResponseData.ProtoReflect.Descriptor instead.
func (*ListProgramsResponseData) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{2}
}

func (x *ListProgramsResponseData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListProgramsResponseData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListProgramsResponseData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListProgramsResponseData) GetContent() []*Program {
	if x != nil {
		return x.Content
	}
	return nil
}

type ListProgramsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string                    `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Code    int32                     `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Data    *ListProgramsResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ListProgramsResponse) Reset() {
	*x = ListProgramsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListProgramsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProgramsResponse) ProtoMessage() {}

func (x *ListProgramsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProgramsResponse.ProtoReflect.Descriptor instead.
func (*ListProgramsResponse) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{3}
}

func (x *ListProgramsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListProgramsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListProgramsResponse) GetData() *ListProgramsResponseData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ProgramCommandRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *ProgramCommandRequest) Reset() {
	*x = ProgramCommandRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgramCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgramCommandRequest) ProtoMessage() {}

func (x *ProgramCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgramCommandRequest.ProtoReflect.Descriptor instead.
func (*ProgramCommandRequest) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{4}
}

func (x *ProgramCommandRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProgramCommandRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type Program struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	State       string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	PackagePath string `protobuf:"bytes,4,opt,name=package_path,json=packagePath,proto3" json:"package_path,omitempty"`
	InstallPath string `protobuf:"bytes,5,opt,name=install_path,json=installPath,proto3" json:"install_path,omitempty"`
	Port        int64  `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	Bootable    bool   `protobuf:"varint,7,opt,name=bootable,proto3" json:"bootable,omitempty"`
	Sort        int32  `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *Program) Reset() {
	*x = Program{}
	if protoimpl.UnsafeEnabled {
		mi := &file_programs_v1_program_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Program) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Program) ProtoMessage() {}

func (x *Program) ProtoReflect() protoreflect.Message {
	mi := &file_programs_v1_program_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Program.ProtoReflect.Descriptor instead.
func (*Program) Descriptor() ([]byte, []int) {
	return file_programs_v1_program_proto_rawDescGZIP(), []int{5}
}

func (x *Program) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Program) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Program) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Program) GetPackagePath() string {
	if x != nil {
		return x.PackagePath
	}
	return ""
}

func (x *Program) GetInstallPath() string {
	if x != nil {
		return x.InstallPath
	}
	return ""
}

func (x *Program) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Program) GetBootable() bool {
	if x != nil {
		return x.Bootable
	}
	return false
}

func (x *Program) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_programs_v1_program_proto protoreflect.FileDescriptor

var file_programs_v1_program_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x3e, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x77, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x18, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x7f, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x32, 0xaa, 0x04, 0x0a, 0x0e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55, 0x92, 0x41,
	0x37, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0d, 0x4c, 0x69, 0x73,
	0x74, 0x20, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x1c, 0x4c, 0x69, 0x73, 0x74,
	0x20, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x65,
	0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x62, 0x01,
	0x2a, 0x12, 0x10, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0xc0, 0x01, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6d, 0x92, 0x41, 0x42, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x13, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x20,
	0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x1a, 0x21, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x20, 0x6f, 0x6e, 0x20, 0x74, 0x68, 0x65, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x62, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x2f, 0x63, 0x6d, 0x64, 0x73, 0x12, 0xa7, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x63, 0x92, 0x41, 0x43,
	0x0a, 0x08, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x12, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x1a, 0x23,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x20, 0x74, 0x68, 0x65, 0x20, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x20, 0x66, 0x72, 0x6f, 0x6d, 0x20, 0x74, 0x68, 0x65, 0x20, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x2a, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x42, 0xfd, 0x01, 0x92, 0x41, 0x48, 0x12, 0x05, 0x32, 0x03, 0x31, 0x2e, 0x30, 0x2a, 0x01, 0x02,
	0x72, 0x3c, 0x0a, 0x0a, 0x41, 0x49, 0x53, 0x20, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x2e,
	0x68, 0x74, 0x74, 0x70, 0x3a, 0x2f, 0x2f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68,
	0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x2f, 0x61, 0x69, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x0a, 0x0f,
	0x63, 0x6f, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x42,
	0x0c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a,
	0x44, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6a, 0x68, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2f, 0x61, 0x69,
	0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x73, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x50, 0x58, 0x58, 0xaa, 0x02, 0x0b, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x0b, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x73, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x17, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0xea, 0x02, 0x0c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_programs_v1_program_proto_rawDescOnce sync.Once
	file_programs_v1_program_proto_rawDescData = file_programs_v1_program_proto_rawDesc
)

func file_programs_v1_program_proto_rawDescGZIP() []byte {
	file_programs_v1_program_proto_rawDescOnce.Do(func() {
		file_programs_v1_program_proto_rawDescData = protoimpl.X.CompressGZIP(file_programs_v1_program_proto_rawDescData)
	})
	return file_programs_v1_program_proto_rawDescData
}

var file_programs_v1_program_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_programs_v1_program_proto_goTypes = []interface{}{
	(*CommonResponse)(nil),           // 0: programs.v1.CommonResponse
	(*ListProgramsRequest)(nil),      // 1: programs.v1.ListProgramsRequest
	(*ListProgramsResponseData)(nil), // 2: programs.v1.ListProgramsResponseData
	(*ListProgramsResponse)(nil),     // 3: programs.v1.ListProgramsResponse
	(*ProgramCommandRequest)(nil),    // 4: programs.v1.ProgramCommandRequest
	(*Program)(nil),                  // 5: programs.v1.Program
}
var file_programs_v1_program_proto_depIdxs = []int32{
	5, // 0: programs.v1.ListProgramsResponseData.content:type_name -> programs.v1.Program
	2, // 1: programs.v1.ListProgramsResponse.data:type_name -> programs.v1.ListProgramsResponseData
	1, // 2: programs.v1.PackageService.ListPrograms:input_type -> programs.v1.ListProgramsRequest
	4, // 3: programs.v1.PackageService.ExecuteProgram:input_type -> programs.v1.ProgramCommandRequest
	5, // 4: programs.v1.PackageService.DeleteProgram:input_type -> programs.v1.Program
	3, // 5: programs.v1.PackageService.ListPrograms:output_type -> programs.v1.ListProgramsResponse
	0, // 6: programs.v1.PackageService.ExecuteProgram:output_type -> programs.v1.CommonResponse
	0, // 7: programs.v1.PackageService.DeleteProgram:output_type -> programs.v1.CommonResponse
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_programs_v1_program_proto_init() }
func file_programs_v1_program_proto_init() {
	if File_programs_v1_program_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_programs_v1_program_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_programs_v1_program_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListProgramsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_programs_v1_program_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListProgramsResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_programs_v1_program_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListProgramsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_programs_v1_program_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgramCommandRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_programs_v1_program_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Program); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_programs_v1_program_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_programs_v1_program_proto_goTypes,
		DependencyIndexes: file_programs_v1_program_proto_depIdxs,
		MessageInfos:      file_programs_v1_program_proto_msgTypes,
	}.Build()
	File_programs_v1_program_proto = out.File
	file_programs_v1_program_proto_rawDesc = nil
	file_programs_v1_program_proto_goTypes = nil
	file_programs_v1_program_proto_depIdxs = nil
}
