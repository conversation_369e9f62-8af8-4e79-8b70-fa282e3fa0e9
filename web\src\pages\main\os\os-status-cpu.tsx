import React from 'react';
import {
  Card,
  Typography,
  // Tag,
  Space,
  Descriptions,
} from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import locale from './locale';

interface OSStatusProps {
  data: {
    cpu?: {
      cores: string;
      usage: string;
    };
  };
}

export default function OSStatusCPU(props: OSStatusProps) {
  const t = useLocale(locale);
  const { data } = props;
  const dataCPUStatus = [
    {
      label: t['os.cpu.cores'],
      value: data.cpu.cores,
    },
    {
      label: t['os.cpu.usage'],
      value: data.cpu.usage
    }
  ];

  return (
    <Card>
      <Space align="start">
        <Typography.Title
          style={{ marginTop: 0, marginBottom: 16 }}
          heading={6}
        >
          {t['os.cpu.title']}
        </Typography.Title>
      </Space>
      <Descriptions
        colon=": "
        layout="horizontal"
        data={dataCPUStatus}
        column={2}
      />
    </Card>
  );
}
