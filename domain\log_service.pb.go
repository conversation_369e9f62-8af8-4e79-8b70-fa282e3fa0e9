package domain

import (
	"context"
	"log/slog"
	"time"

	logsv1 "gitlab.jhonginfo.com/product/ais-server/proto/logs/v1"
)

func ClearLogsScheduleTask() {
	slog.Debug("Clear logs schedule task")
	do := &Log{}
	// Clear logs before 7 days
	err := do.DeleteBefore(7)
	if err != nil {
		slog.Error("Clear logs err: " + err.Error())
		return
	}
	slog.Debug("Clear logs schedule task done")
}

func LogsPOs2LogsVOs(pos []*Log) []*logsv1.Log {
	if pos == nil {
		return nil
	}
	var vos []*logsv1.Log
	for _, po := range pos {
		vos = append(vos, &logsv1.Log{
			Id:        po.Id,
			Level:     po.Level,
			Message:   po.Message,
			CreatedAt: po.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return vos
}

func (b *Backend) ListLogs(ctx context.Context, req *logsv1.ListLogsRequest) (*logsv1.ListLogsResponse, error) {
	var logs []*Log
	var data *logsv1.ListLogsResponseData

	page := int(req.Page)
	size := int(req.Size)
	do := &Log{}

	total, err := do.Count()
	if err != nil {
		return nil, err
	}
	if page > 0 && size > 0 {
		logs, err = do.Page(&page, &size)
		if err != nil {
			return nil, err
		}
		data = &logsv1.ListLogsResponseData{
			Total:   int32(total),
			Page:    int32(page),
			Size:    int32(size),
			Content: LogsPOs2LogsVOs(logs),
		}
	}
	return &logsv1.ListLogsResponse{
		Code: 0,
		Data: data,
	}, nil
}

func (b *Backend) SaveLog(ctx context.Context, req *logsv1.Log) (*logsv1.SaveLogResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	var createdAt time.Time
	var err error
	if req.CreatedAt != "" {
		createdAt, err = time.Parse("2006-01-02 15:04:05", req.CreatedAt)
		if err != nil {
			createdAt = time.Now()
		}
	}
	do := &Log{
		CreatedAt: createdAt,
		Level:     req.Level,
		Message:   req.Message,
	}
	err = do.Add()
	if err != nil {
		return nil, err
	}

	return &logsv1.SaveLogResponse{
		Code:    0,
		Message: "Log saved",
	}, nil
}

func (b *Backend) ClearLogs(ctx context.Context, req *logsv1.ClearLogsRequest) (*logsv1.ClearLogsResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	do := &Log{}
	err := do.DeleteAll()
	if err != nil {
		return nil, err
	}

	return &logsv1.ClearLogsResponse{
		Code:    0,
		Message: "Logs cleared",
	}, nil
}
