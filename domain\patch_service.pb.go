package domain

import (
	"context"
	"os"
	"path/filepath"

	patchesv1 "gitlab.jhonginfo.com/product/ais-server/proto/patches/v1"
)

func (b *Backend) ListPatches(_ context.Context, req *patchesv1.Patch) (*patchesv1.ListPatchesResponse, error) {
	ts, err := ListPatches(req.Name, req.Uri, req.PackageName)
	if err != nil {
		return nil, err
	}
	return &patchesv1.ListPatchesResponse{
		Code: 0,
		Data: PatchPOs2PatchVOs(ts),
	}, nil
}

func (b *Backend) DeletePatch(_ context.Context, req *patchesv1.Patch) (*patchesv1.CommonResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkg := &Package{
		Name: req.GetPackageName(),
	}
	err := pkg.GetByName()
	if err != nil {
		return nil, err
	}
	patchesDir, err := pkg.GetPatchesDir()
	if err != nil {
		return nil, err
	}
	patchName := req.GetName()
	patchFile := filepath.Join(patchesDir, patchName)
	if _, err := os.Stat(patchFile); os.IsNotExist(err) {
		return &patchesv1.CommonResponse{
			Code:    0,
			Message: "Patch not found",
		}, nil
	}
	if err := os.Remove(patchFile); err != nil {
		return nil, err
	}
	return &patchesv1.CommonResponse{
		Code:    0,
		Message: "Delete patch successful",
	}, nil
}
