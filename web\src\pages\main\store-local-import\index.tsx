import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import {
  Card,
  Form,
  Button,
  Message,
  Space,
  Input,
  Typography,
  Alert,
  Divider
} from '@arco-design/web-react';
import {
  IconArrowLeft,
  IconFile
} from '@arco-design/web-react/icon';

import api from '@/utils/api';
import useLocale from '@/utils/useLocale';
import ProgressMonitor from '@/components/ProgressMonitor';

import locale from './locale';
import styles from './style/index.module.less';

const { useForm } = Form;
const { Title, Text } = Typography;

function LocalImportForm() {
  const history = useHistory();
  const t = useLocale(locale);
  const [loading, setLoading] = useState(false);
  const [form] = useForm();
  const [selectedFile, setSelectedFile] = useState<{path: string, name: string} | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);



  // 处理进度监控完成
  const handleProgressComplete = (success: boolean, message?: string) => {
    if (success) {
      Message.success(t['localImport.success']);
      setTimeout(() => {
        history.push({ pathname: '/main/store' });
      }, 1000);
    } else {
      Message.error(message || t['localImport.failed']);
    }
  };
  
  // 关闭进度监控
  const handleProgressClose = () => {
    setShowProgress(false);
    setCurrentTaskId(null);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 监控导入进度（已移除，使用ProgressMonitor组件代替）

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (!selectedFile) {
      Message.error('请选择要导入的文件');
      return;
    }

    try {
      setLoading(true);

      const response = await api('/api/v1/local-import-tasks', {
        method: 'post',
        data: {
          package_name: values.packageName,
          package_version: 'auto', // 自动从压缩包解析
          local_path: selectedFile.path
        }
      });

      if (response.data.code === 0) {
        const taskId = response.data.data.id;
        setCurrentTaskId(taskId);
        setShowProgress(true);
        
        // 进度监控由ProgressMonitor组件处理
      } else {
        throw new Error(response.data.message || '创建任务失败');
      }
    } catch (error: any) {
      console.error('导入失败:', error);
      Message.error(error.message || '导入失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setSelectedFile(null);
    setShowProgress(false);
    setCurrentTaskId(null);
  };

  // 返回列表
  const handleBack = () => {
    history.push('/main/store');
  };

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Space>
            <Button 
              type="text" 
              icon={<IconArrowLeft />} 
              onClick={handleBack}
            >
              {t['localImport.button.back']}
            </Button>
            <Title heading={4}>{t['localImport.title']}</Title>
          </Space>
        </div>

        <Divider />

        <div className={styles.content}>
          <Alert
            type="info"
            content={t['localImport.info.alert']}
            style={{ marginBottom: 24 }}
          />

          <Form
            form={form}
            layout="vertical"
            onSubmit={handleSubmit}
            disabled={loading}
          >
            <Form.Item
              label={t['localImport.form.packageName']}
              field="packageName"
              rules={[{ required: true, message: t['localImport.form.packageName.placeholder'] }]}
            >
              <Input placeholder={t['localImport.form.packageName.placeholder']} />
            </Form.Item>

            <Form.Item
              label={t['localImport.form.localPath']}
              field="localPath"
              rules={[
                { required: true, message: t['localImport.form.localPath.required'] },
                { 
                  validator: (value, callback) => {
                    if (value && !value.toLowerCase().endsWith('.zip')) {
                      callback(t['localImport.form.localPath.zipOnly']);
                    }
                  }
                }
              ]}
            >
              <Input 
                placeholder={t['localImport.form.localPath.placeholder']}
                onChange={(value) => {
                  setSelectedFile(value ? { path: value, name: value.split('\\').pop() || value } : null);
                }}
                disabled={loading}
              />
            </Form.Item>

            {/* 选中文件信息 */}
            {selectedFile && (
              <Card className={styles.fileInfo}>
                <Space>
                  <IconFile style={{ color: 'var(--color-primary-6)' }} />
                  <div>
                    <Text strong>{selectedFile.name}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {selectedFile.path}
                    </Text>
                  </div>
                </Space>
              </Card>
            )}

            {/* 进度显示由ProgressMonitor组件处理 */}

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={!selectedFile || loading}
                >
                  {t['localImport.button.import']}
                </Button>
                <Button 
                  onClick={handleReset}
                  disabled={loading}
                >
                  {t['localImport.button.reset']}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Card>
      
      {/* 进度监控 */}
      <ProgressMonitor
        visible={showProgress}
        taskId={currentTaskId}
        title={t['localImport.monitor.title']}
        onComplete={handleProgressComplete}
        onCancel={handleProgressClose}
      />
    </div>
  );
}

export default LocalImportForm;