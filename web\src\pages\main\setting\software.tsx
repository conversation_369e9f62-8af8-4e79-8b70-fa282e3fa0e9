import React, { useContext, useEffect } from 'react';
import useLocale from '@/utils/useLocale';
import locale from './locale';
import { GlobalContext } from '@/context';
import {
  Grid,
  Input,
  Button,
  Form,
  Space,
  Skeleton,
  Switch,
} from '@arco-design/web-react';

function SoftwareForm({ loading, data, callback }: { loading?: boolean, data?: any, callback?: any }) {
  const t = useLocale(locale);
  const [ form ] = Form.useForm();
  const { lang } = useContext(GlobalContext);

  const handleSave = async () => {
    try {
      await form.validate();
      callback && callback(form.getFieldsValue());
    } catch (_) {}
  };

  const handleReset = () => {
    form.setFieldsValue({
      ...data,
    });
  };

  const loadingNode = (rows = 1) => {
    return (
      <Skeleton
        text={{
          rows,
          width: new Array(rows).fill('100%'),
        }}
        animation
      />
    );
  };

  useEffect(() => {
    form.setFieldsValue({
      ...data,
    });
  });

  return (
    <Form
      style={{ width: '500px', marginTop: '6px' }}
      form={form}
      labelCol={{ span: lang === 'en-US' ? 7 : 6 }}
      wrapperCol={{ span: lang === 'en-US' ? 17 : 18 }}
    >
      
      <Form.Item
        label={t['softwareSetting.name']}
        field="name"
      >
        {loading ? (
          loadingNode()
        ) : (
          <Input placeholder={t['softwareSetting.name.placeholder']} />
        )}
      </Form.Item>
      <Form.Item label={t['softwareSetting.host']} style={{ marginBottom: 0 }}>
        <Grid.Row gutter={8}>
          <Grid.Col span={18}>
            <Form.Item field='host'>
              <Input placeholder={t['softwareSetting.host.placeholder']} />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={6}>
            <Form.Item field='ssl' label={t['softwareSetting.ssl']}>
            <Switch defaultChecked={data.ssl} />
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
      </Form.Item>

      <Form.Item
        label={t['softwareSetting.cacheDir']}
      >
        {loading ? (
          loadingNode()
        ) : (
          <span>{data.cacheDir}</span>
        )}
      </Form.Item>
      
      <Form.Item
        label={t['softwareSetting.storageDir']}
      >
        {loading ? (
          loadingNode()
        ) : (
          <span>{data.storageDir}</span>
        )}
      </Form.Item>

      <Form.Item
        label={t['softwareSetting.version']}
      >
        {loading ? (
          loadingNode()
        ) : (
          <span>{data.version}</span>
        )}
      </Form.Item>

      <Form.Item label=" ">
        <Space>
          <Button type="primary" onClick={handleSave}>
            {t['softwareSetting.operations.save']}
          </Button>
          <Button onClick={handleReset}>{t['softwareSetting.operations.reset']}</Button>
        </Space>
      </Form.Item>
    </Form>
  );
}

export default SoftwareForm;
