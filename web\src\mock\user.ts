import Mock from 'mockjs';
import { isSSR } from '@/utils/is';
import setupMock from '@/utils/setupMock';
import { generatePermission } from '@/routes';

if (!isSSR) {
  Mock.XHR.prototype.withCredentials = true;

  setupMock({
    setup: () => {
      // 用户信息
      // const userRole = window.localStorage.getItem('userRole') || 'admin';
      const userRole = window.localStorage.getItem('userRole') || 'user';
      Mock.mock(new RegExp('/api/user/userInfo'), () => {
        return Mock.mock({
          name: '管理员',
          avatar:
            '/images/avatar.jpg',
          email: '<EMAIL>',
          job: 'CTO',
          jobName: '技术总监',
          organization: 'jhong',
          organizationName: '长沙京鸿',
          location: 'changsha',
          locationName: '长沙',
          introduction: '无',
          personalWebsite: 'https://www.arco.design',
          verified: true,
          phoneNumber: /132[*]{6}[0-9]{2}/,
          accountId: /[a-z]{4}[-][0-9]{8}/,
          registrationTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
          permissions: generatePermission(userRole),
        });
      });

      // 登录
      Mock.mock(new RegExp('/api/user/login'), (params) => {
        const { userName, password } = JSON.parse(params.body);
        if (!userName) {
          return {
            status: 'error',
            msg: '用户名不能为空',
          };
        }
        if (!password) {
          return {
            status: 'error',
            msg: '密码不能为空',
          };
        }
        if (userName === 'admin' && password === 'admin') {
          return {
            status: 'ok',
          };
        }
        return {
          status: 'error',
          msg: '账号或者密码错误',
        };
      });
    },
  });
}
