import{u as e,b as a,j as o,s as m,T as l}from"./index.e8bac691.js";import{l as r,D as s}from"./index.b8bf3dbf.js";import{C as t}from"./index.a97db34b.js";function i(i){const n=e(r),{data:d}=i,u=[{label:n["os.memory.total"],value:d.memory.total},{label:n["os.memory.free"],value:d.memory.free},{label:n["os.memory.used"],value:d.memory.used},{label:n["os.memory.usage"],value:d.memory.usage}];return a(t,{children:[o(m,{align:"start",children:o(l.Title,{style:{marginTop:0,marginBottom:16},heading:6,children:n["os.memory.title"]})}),o(s,{colon:": ",layout:"horizontal",data:u,column:2})]})}export{i as default};
