package lib

import (
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
)

// FileTask 文件复制任务
type FileTask struct {
	SrcPath  string
	DstPath  string
	IsDir    bool
	Size     int64
}

// CopyStats 复制统计信息
type CopyStats struct {
	TotalFiles    int64
	ProcessedFiles int64
	TotalSize     int64
	ProcessedSize int64
	StartTime     time.Time
	Errors        []error
	mu            sync.RWMutex
}

func (cs *CopyStats) AddProcessed(size int64) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	cs.ProcessedFiles++
	cs.ProcessedSize += size
}

func (cs *CopyStats) AddError(err error) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	cs.Errors = append(cs.Errors, err)
}

func (cs *CopyStats) GetProgress() (float64, string) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	if cs.TotalFiles == 0 {
		return 0, "准备中..."
	}
	
	progress := float64(cs.ProcessedFiles) / float64(cs.TotalFiles) * 100
	elapsed := time.Since(cs.StartTime)
	
	if cs.ProcessedFiles > 0 {
		avgTimePerFile := elapsed / time.Duration(cs.ProcessedFiles)
		remainingFiles := cs.TotalFiles - cs.ProcessedFiles
		eta := avgTimePerFile * time.Duration(remainingFiles)
		
		return progress, fmt.Sprintf("已处理: %d/%d 文件, 预计剩余: %v", 
			cs.ProcessedFiles, cs.TotalFiles, eta.Round(time.Second))
	}
	
	return progress, fmt.Sprintf("已处理: %d/%d 文件", cs.ProcessedFiles, cs.TotalFiles)
}

// CopyDirOptimized 高性能目录复制，专门优化大量碎片文件场景
func CopyDirOptimized(src, dst string, progressCallback func(float64, string)) error {
	conf := config.Get()
	
	// 确保目标目录存在
	if err := os.MkdirAll(dst, 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}
	
	// 第一阶段：扫描所有文件，构建任务列表
	slog.Info(fmt.Sprintf("[高性能复制] 开始扫描源目录: %s", src))
	tasks, stats, err := scanDirectory(src, dst)
	if err != nil {
		return fmt.Errorf("扫描目录失败: %v", err)
	}
	
	slog.Info(fmt.Sprintf("[高性能复制] 扫描完成，发现 %d 个文件，总大小: %s", 
		stats.TotalFiles, ByteCountBinary(uint64(stats.TotalSize))))
	
	// 第二阶段：并发复制文件
	return copyFilesParallel(tasks, stats, conf, progressCallback)
}

// scanDirectory 扫描目录，构建文件任务列表
func scanDirectory(src, dst string) ([]*FileTask, *CopyStats, error) {
	var tasks []*FileTask
	stats := &CopyStats{
		StartTime: time.Now(),
	}
	
	err := filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 计算相对路径
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}
		
		dstPath := filepath.Join(dst, relPath)
		
		task := &FileTask{
			SrcPath: path,
			DstPath: dstPath,
			IsDir:   info.IsDir(),
			Size:    info.Size(),
		}
		
		tasks = append(tasks, task)
		
		if !info.IsDir() {
			stats.TotalFiles++
			stats.TotalSize += info.Size()
		}
		
		return nil
	})
	
	return tasks, stats, err
}

// copyFilesParallel 并发复制文件
func copyFilesParallel(tasks []*FileTask, stats *CopyStats, conf *config.Config, progressCallback func(float64, string)) error {
	// 确定工作线程数
	workers := int(conf.CopyWorkers)
	if workers <= 0 {
		workers = runtime.NumCPU()
	}
	
	// 创建任务通道
	taskChan := make(chan *FileTask, len(tasks))
	errorChan := make(chan error, len(tasks))
	
	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			copyWorker(workerID, taskChan, errorChan, stats, conf)
		}(i)
	}
	
	// 启动进度监控协程
	progressDone := make(chan bool)
	go func() {
		ticker := time.NewTicker(500 * time.Millisecond) // 每500ms更新一次进度
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				if progressCallback != nil {
					progress, message := stats.GetProgress()
					progressCallback(progress, message)
				}
			case <-progressDone:
				return
			}
		}
	}()
	
	// 发送任务到工作队列
	go func() {
		defer close(taskChan)
		
		// 先创建所有目录
		for _, task := range tasks {
			if task.IsDir {
				if err := os.MkdirAll(task.DstPath, 0755); err != nil {
					errorChan <- fmt.Errorf("创建目录 %s 失败: %v", task.DstPath, err)
				}
			}
		}
		
		// 然后复制所有文件
		for _, task := range tasks {
			if !task.IsDir {
				taskChan <- task
			}
		}
	}()
	
	// 等待所有工作完成
	wg.Wait()
	close(errorChan)
	progressDone <- true
	
	// 检查错误
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}
	
	// 最终进度更新
	if progressCallback != nil {
		progress, message := stats.GetProgress()
		progressCallback(progress, message)
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("复制过程中发生 %d 个错误，首个错误: %v", len(errors), errors[0])
	}
	
	slog.Info(fmt.Sprintf("[高性能复制] 复制完成，处理了 %d 个文件，总大小: %s，耗时: %v", 
		stats.ProcessedFiles, ByteCountBinary(uint64(stats.ProcessedSize)), time.Since(stats.StartTime)))
	
	return nil
}

// copyWorker 文件复制工作协程
func copyWorker(workerID int, taskChan <-chan *FileTask, errorChan chan<- error, stats *CopyStats, conf *config.Config) {
	bufferSize := int(conf.CopyBufferSize)
	if bufferSize <= 0 {
		bufferSize = 1024 * 1024 // 默认1MB
	}
	
	buffer := make([]byte, bufferSize)
	
	for task := range taskChan {
		if err := copyFileOptimized(task.SrcPath, task.DstPath, buffer); err != nil {
			errorChan <- fmt.Errorf("复制文件 %s 到 %s 失败: %v", task.SrcPath, task.DstPath, err)
			continue
		}
		
		stats.AddProcessed(task.Size)
	}
}

// copyFileOptimized 优化的文件复制函数
func copyFileOptimized(src, dst string, buffer []byte) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()
	
	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}
	
	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()
	
	// 使用提供的缓冲区进行复制
	for {
		n, err := srcFile.Read(buffer)
		if err != nil && err != io.EOF {
			return err
		}
		if n == 0 {
			break
		}
		
		if _, err := dstFile.Write(buffer[:n]); err != nil {
			return err
		}
	}
	
	return dstFile.Sync()
}
