import React from 'react';
import { 
  Tag,
  Button,
  Typo<PERSON>,
  <PERSON>ge,
  Space
} from '@arco-design/web-react';

const { Text } = Typography;

export function getColumns(
  t: any,
  callback: (record: Record<string, any>, type: string) => Promise<void>
) {
  return [
    {
      title: t['patch.columns.id'],
      dataIndex: 'id'
    },
    {
      title: t['patch.columns.attribution'],
      dataIndex: 'packageName',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['patch.columns.name'],
      dataIndex: 'name',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['patch.columns.uri'],
      dataIndex: 'uri',
      render: (value) => <Text copyable>{value}</Text>,
    },
    {
      title: t['patch.columns.operation'],
      dataIndex: 'operation',
      headerCellStyle: { paddingLeft: '15px' },
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            size="small"
            onClick={() => callback(record, 'delete')}
          >
            {t['patch.columns.operation.delete']}
          </Button>
        </Space>
      ),
    },
  ];
}
