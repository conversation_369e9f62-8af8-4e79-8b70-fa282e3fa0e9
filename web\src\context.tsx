import Paho from 'paho-mqtt';
import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  Message,
  Modal,
} from '@arco-design/web-react';

import useLocale from '@/utils/useLocale';

import consts from './const';
import locale from './locale';
import api from './utils/api';
import useStorage from './utils/useStorage';

export const GlobalContext = createContext<{
  lang?: string;
  setLang?: (value: string) => void;
  theme?: string;
  setTheme?: (value: string) => void;
}>({});

export const MQTTContext = createContext<{
  id?: string;
  mqtt?: Paho.Client;
  setMQTT?: (value: Paho.Client) => void;
}>({});

export function useMQTT() {
  return useContext(MQTTContext);
}

export function MQTTProvider({ children }) {
  const t = useLocale(locale);
  const [id] = useState(Math.random().toString(36).substring(2));
  const [lang] = useStorage('arco-lang', 'zh-CN');
  const [, setParentHost] = useStorage(consts.KEY_AIS_SERVER_PARENT_HOST);
  const [, setSSL] = useStorage(
    consts.KEY_AIS_SERVER_PARENT_SSL, 
    'false'
  );
  const [, setServerUrl] = useStorage(
    consts.KEY_AIS_SERVER_PARENT_API_SERVER
  );
  const [mqtt, setMQTT] = useState(null);
  const mqttContextValue = {
    id,
    mqtt,
    setMQTT,
  };

  const connectMQTT = (cid, host, client) => {
    if (mqtt) {
      mqtt.disconnect();
    }
    const callTopic = consts.TOPIC_X_CLIENT_CALL.replace('*', cid);
    const messageTopic = consts.TOPIC_X_CLIENT_MESSAGE.replace('*', id);
    const mqttClient = new Paho.Client(
      host,
      5884,
      id
    );
    mqttClient.onConnectionLost = (responseObject) => {
      if (responseObject.errorCode !== 0) {
        console.error('onConnectionLost:', responseObject.errorMessage);
        Message.error(`${locale[lang]['message.mqtt.connection.lost']}`);
      }
    };
    mqttClient.onMessageArrived = (message) => {
      const topic = message.destinationName;
      if (topic === messageTopic) {
        Message.info(`${locale[lang]['message.mqtt.operations.control.' + message.payloadString]}`);
        return;
      } else if (topic.indexOf("/heartbeat") !== 0) {
        const clientInfo = JSON.parse(message.payloadString);
        mqttClient.onHeartbeat && mqttClient.onHeartbeat(clientInfo);
        return;
      }
      const act = JSON.parse(message.payloadString);
      if (act && act.result === 'success') {
        const actType = act.type;
        switch (actType) {
          case 'sync':
            const hash = window.location.hash;
            const paths = hash.split('#');
            const path = paths.length > 1 ? paths[1] : "/";
            const type = 'download';
            if (path === '/main/store') {
              window.location.reload();
              return;
            }
            api('/api/v1/packages').then((res) => {
              const resData = res && res.data && res.data.data;
              if (resData.content && resData.content.length > 0) {
                resData.content.forEach((item) => {
                  if (item.name !== 'ais-cell' && item.downloaded === 1 || item.downloaded === '1' || item.downloaded === true) {
                    return;
                  }
                  Modal.confirm({
                    title: t[`main.modal.${type}.title`],
                    content: t[`main.modal.${type}.content`],
                    onOk: () => {
                      api(`/api/v1/tasks`, {
                        method: 'post',
                        data: {
                          name: type,
                          packageId: item.id
                        }
                      }).then((res) => {
                        const resData = res && res.data;
                        if (!resData && !resData.data) {
                          return;
                        }
                        if (resData.data.state === 'failed') {
                          Message.error(t[`main.modal.${type}.operation.fail`]);
                        } else {
                          Message.success(t[`main.modal.${type}.operation.success`]);
                        }
                      }).catch(() => {
                        Message.error(t[`main.modal.${type}.operation.fail`]);
                      });
                    },
                  });
                });
              }
            });
            break
        }
      }
    }
    mqttClient.connect({
      onSuccess: () => {
        console.debug('MQTT is connected.');
        Message.success(`${locale[lang]['message.mqtt.connection.success']}`);
        mqttClient.subscribe(callTopic, {
          onSuccess: () => {
            console.debug('Subscribe topic[' + callTopic + '] success.');
          },
          onFailure: (responseObject) => {
            console.error('Subscribe fail:', responseObject.errorMessage);
            Message.error(`${locale[lang]['message.mqtt.operations.subscribe.fail']}`);
          },
        });
        mqttClient.subscribe(messageTopic, {
          onSuccess: () => {
            console.debug('Subscribe topic[' + messageTopic + '] success.');
          },
          onFailure: (responseObject) => {
            console.error('Subscribe fail:', responseObject.errorMessage);
            Message.error(`${locale[lang]['message.mqtt.operations.subscribe.fail']}`);
          },
        });
        mqttClient.subscribe(consts.TOPIC_ALL_CLIENT_HEARTBEAT, {
          onSuccess: () => {
            console.debug('Subscribe topic[' + consts.TOPIC_ALL_CLIENT_HEARTBEAT + '] success.');
          },
          onFailure: (responseObject) => {
            console.error('Subscribe fail:', responseObject.errorMessage);
            Message.error(`${locale[lang]['message.mqtt.operations.subscribe.fail']}`);
          },
        });
        if (client) {
          delete client.createdAt;
          delete client.updatedAt;
          const newClient = {};
          // 驼峰转下划线
          Object.keys(client).forEach((key) => {
            newClient[key.replace(/([A-Z])/g, "_$1").toLowerCase()] = client[key];
          });
          const msg = new Paho.Message(JSON.stringify(newClient))
          msg.destinationName = consts.TOPIC_X_CLIENT_HEARTBEAT.replace('*', client.id);
          msg.qos = 0;
          mqttClient.publish(msg);
        }
      },
      onFailure: (responseObject) => {
        console.error('onFailure:', responseObject.errorMessage);
        Message.error(`${locale[lang]['message.mqtt.operations.connect.fail']}`);
      },
      reconnect: true,
    });
    setMQTT(mqttClient);
  }

  const disconnectMQTT = () => {
    mqtt && mqtt.disconnect();
  }

  useEffect(() => {
    api('/api/v1/clients/current').then((res) => {
      const resData = res && res.data;
      if (resData.data && resData.data.id) {
        const currentId = resData.data.id;
        const serverUrl = `http${resData.data.ssl === 'true' ? 's' : ''}://${resData.data.parentHost}:${resData.data.parentGatewayPort}`;
        setSSL(resData.data.ssl);
        setParentHost(resData.data.parentHost);
        setServerUrl(serverUrl);
        // 弹窗提示服务端已配置
        Message.success(
          `${locale[lang]['main.message.server-configured.tips']} ${serverUrl}`
        );
        const isLocal = resData.data.parentHost == null || resData.data.parentHost === '' || resData.data.parentHost === 'localhost' || resData.data.parentHost === '127.0.0.1';
        const mqttHost = isLocal ? window.location.hostname : resData.data.parentHost;
        connectMQTT(currentId, mqttHost, isLocal ? null : resData.data);
      }
    });
    return () => disconnectMQTT();
  }, []);

  return (
    <MQTTContext.Provider value={mqttContextValue}>{children}</MQTTContext.Provider>
  );
}
