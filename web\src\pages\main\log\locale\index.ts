const i18n = {
  'en-US': {
    'menu.main': 'Main',
    'menu.main.log': 'Log',
    'menu.list': 'List',
    'menu.list.searchTable': 'Search Table',
    'searchTable.form.search': 'Search',
    'searchTable.form.reset': 'Reset',
    'searchTable.columns.id': 'ID',
    'searchTable.columns.level': 'Level',
    'searchTable.columns.message': 'Message',
    'searchTable.columns.timestamp': 'Timestamp',
    'searchTable.columns.createdAt': 'Time',
    'searchTable.operations.clear': 'Clear',
    'searchTable.operations.clear.success': 'Clear successfully',
    'searchForm.id.placeholder': 'Please enter the ID',
    'searchForm.name.placeholder': 'Please enter the name',
    'searchTable.modal.delete.title': 'Delete confirmation',
    'searchTable.modal.delete.content': 'Are you sure you want to delete this record?',
    'searchForm.version.placeholder': 'Please enter the version',
    'searchForm.all.placeholder': 'all',
  },
  'zh-CN': {
    'menu.main': '主页',
    'menu.main.log': '日志',
    'menu.list': '列表页',
    'menu.list.searchTable': '查询表格',
    'searchTable.form.search': '查询',
    'searchTable.form.reset': '重置',
    'searchTable.columns.id': 'ID',
    'searchTable.columns.level': '级别',
    'searchTable.columns.message': '信息',
    'searchTable.columns.timestamp': '时间',
    'searchTable.columns.createdAt': '时间',
    'searchTable.operations.clear': '清空',
    'searchTable.operations.clear.success': '清空成功',
    'searchForm.id.placeholder': '请输入ID',
    'searchForm.name.placeholder': '请输入名称',
    'searchForm.version.placeholder': '请输入版本',
    'searchForm.all.placeholder': '全部',
  },
};

export default i18n;
