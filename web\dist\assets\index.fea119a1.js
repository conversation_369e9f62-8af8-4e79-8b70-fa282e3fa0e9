import{u as e,r as a,b as t,j as o,k as r,l as n,i as s}from"./index.bbeb3af6.js";import{C as i}from"./index.cf9faf12.js";import{T as m}from"./index.b90074ae.js";import{i as c,S as l}from"./search-form.9713b70b.js";import{getColumns as p}from"./constants.5104c168.js";import"./index.9dee27c6.js";function d(){const d=e(c),g=async(e,a)=>{if("delete"===a)r.confirm({title:d["program.tables.main.modal.delete.title"],content:d["program.tables.main.modal.delete.content"],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}`,{method:"delete"}).then((()=>{s.success(d["program.tables.main.modal.delete.operation.success"]),$()})).catch((()=>{s.error(d["program.tables.main.modal.delete.operation.fail"])})).finally((()=>{z(!1)}))}});else r.confirm({title:d[`program.tables.main.modal.${a}.title`],content:d[`program.tables.main.modal.${a}.content`],onOk:()=>{z(!0),n(`/api/v1/programs/${e.id}/cmds`,{method:"post",data:{type:a},timeout:6e4}).then((e=>{const t=e&&e.data;(t||t.data)&&(0!==t.code?s.error(d[`program.tables.main.modal.${a}.operation.fail`]):s.success(d[`program.tables.main.modal.${a}.operation.success`]),$())})).catch((()=>{s.error(d[`program.tables.main.modal.${a}.operation.fail`])})).finally((()=>{z(!1)}))}})},f=a.exports.useMemo((()=>p(d,g)),[d]),[u,b]=a.exports.useState([]),[S,h]=a.exports.useState({sizeCanChange:!0,showTotal:!0,pageSize:50,current:1,pageSizeChangeResetCurrent:!0,pageSizeOptions:["10","20","50","100","200"]}),[x,z]=a.exports.useState(!0),[j,y]=a.exports.useState({});function $(){const{current:e,pageSize:a}=S;z(!0),n("/api/v1/programs",{data:{page:e,size:a,...j}}).then((t=>{const o=t&&t.data;(o||o.data||o.data.content)&&(b(o.data.content),h({...S,current:e,pageSize:a,total:o.data.total}))})).finally((()=>{z(!1)}))}return a.exports.useEffect((()=>{$()}),[S.current,S.pageSize,JSON.stringify(j)]),t(i,{children:[o(l,{loading:x,onSearch:function(e){h({...S,current:1}),y(e)}}),o(m,{rowKey:"id",loading:x,onChange:function({current:e,pageSize:a}){h({...S,current:e,pageSize:a})},pagination:S,columns:f,data:u})]})}export{d as default};
