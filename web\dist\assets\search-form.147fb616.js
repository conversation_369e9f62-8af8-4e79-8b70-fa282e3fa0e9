import{u as e,b as l,j as a,af as r,Q as s,B as o,ag as c,G as n}from"./index.6227d37e.js";import{i,s as d}from"./index.module.cf3602d3.js";import{I as h}from"./index.be17d0db.js";const{Row:m,Col:t}=n,{useForm:p}=r;function f(n){const f=e(i),[b]=p();return l("div",{className:d["search-form-wrapper"],children:[a(r,{form:b,className:d["search-form"],labelAlign:"left",labelCol:{span:4},wrapperCol:{span:20},children:l(m,{gutter:24,children:[a(t,{span:12,children:a(r.Item,{label:f["searchTable.columns.id"],field:"id",children:a(s,{placeholder:f["searchForm.id.placeholder"],allowClear:!0})})}),a(t,{span:12,children:a(r.Item,{label:f["searchTable.columns.name"],field:"name",children:a(s,{allowClear:!0,placeholder:f["searchForm.name.placeholder"]})})}),a(t,{span:12,children:a(r.Item,{label:f["searchTable.columns.version"],field:"version",children:a(s,{allowClear:!0,placeholder:f["searchForm.version.placeholder"]})})})]})}),l("div",{className:d["right-button"],children:[a(o,{type:"primary",icon:a(c,{}),onClick:()=>{const e=b.getFieldsValue();n.onSearch(e)},children:f["searchTable.form.search"]}),a(o,{icon:a(h,{}),onClick:()=>{b.resetFields(),n.onSearch({})},children:f["searchTable.form.reset"]})]})]})}export{f as default};
