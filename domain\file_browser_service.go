package domain

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strings"

	tasksv1 "gitlab.jhonginfo.com/product/ais-server/proto/tasks/v1"
)

// FileInfo 文件信息结构
type FileInfo struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	IsDir    bool   `json:"is_dir"`
	Size     int64  `json:"size"`
	ModTime  string `json:"mod_time"`
	Readable bool   `json:"readable"`
}

// BrowseLocalFiles 浏览本地文件系统
func (b *Backend) BrowseLocalFiles(ctx context.Context, req *tasksv1.BrowseFilesRequest) (*tasksv1.BrowseFilesResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	path := req.Path
	if path == "" {
		// 如果没有指定路径，返回根目录列表
		path = getRootPath()
	}

	slog.Info(fmt.Sprintf("[文件浏览] 浏览目录: %s", path))

	// 检查路径是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, fmt.Errorf("路径不存在: %s", path)
	}

	// 读取目录内容
	entries, err := os.ReadDir(path)
	if err != nil {
		slog.Error(fmt.Sprintf("[文件浏览] 读取目录失败: %s, 错误: %v", path, err))
		return nil, fmt.Errorf("读取目录失败: %v", err)
	}

	var files []*tasksv1.FileInfo
	var dirs []*tasksv1.FileInfo

	// 添加上级目录项（如果不是根目录）
	if !isRootPath(path) {
		parentPath := filepath.Dir(path)
		dirs = append(dirs, &tasksv1.FileInfo{
			Name:     "..",
			Path:     parentPath,
			IsDir:    true,
			Size:     0,
			ModTime:  "",
			Readable: true,
		})
	}

	// 处理目录条目
	for _, entry := range entries {
		// 跳过隐藏文件（以.开头的文件）
		if strings.HasPrefix(entry.Name(), ".") {
			continue
		}

		fullPath := filepath.Join(path, entry.Name())
		info, err := entry.Info()
		if err != nil {
			continue // 跳过无法获取信息的文件
		}

		fileInfo := &tasksv1.FileInfo{
			Name:     entry.Name(),
			Path:     fullPath,
			IsDir:    entry.IsDir(),
			Size:     info.Size(),
			ModTime:  info.ModTime().Format("2006-01-02 15:04:05"),
			Readable: isReadable(fullPath),
		}

		if entry.IsDir() {
			dirs = append(dirs, fileInfo)
		} else {
			// 只显示压缩文件
			if isArchiveFile(entry.Name()) {
				files = append(files, fileInfo)
			}
		}
	}

	// 排序：目录在前，文件在后，按名称排序
	sort.Slice(dirs, func(i, j int) bool {
		return dirs[i].Name < dirs[j].Name
	})
	sort.Slice(files, func(i, j int) bool {
		return files[i].Name < files[j].Name
	})

	// 合并目录和文件列表
	allFiles := append(dirs, files...)

	slog.Info(fmt.Sprintf("[文件浏览] 找到 %d 个目录，%d 个压缩文件", len(dirs), len(files)))

	return &tasksv1.BrowseFilesResponse{
		Code:        0,
		Message:     "success",
		CurrentPath: path,
		Files:       allFiles,
	}, nil
}

// GetSystemDrives 获取系统驱动器列表（Windows）
func (b *Backend) GetSystemDrives(ctx context.Context, req *tasksv1.GetDrivesRequest) (*tasksv1.GetDrivesResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	slog.Info("[文件浏览] 获取系统驱动器列表")

	var drives []*tasksv1.DriveInfo

	if runtime.GOOS == "windows" {
		// Windows系统：检查A-Z盘符
		for i := 'A'; i <= 'Z'; i++ {
			drive := fmt.Sprintf("%c:\\", i)
			if _, err := os.Stat(drive); err == nil {
				// 获取驱动器信息
				driveInfo := &tasksv1.DriveInfo{
					Letter:    string(i),
					Path:      drive,
					Label:     getDriveLabel(drive),
					DriveType: getDriveType(drive),
					Available: true,
				}
				drives = append(drives, driveInfo)
			}
		}
	} else {
		// Unix系统：返回根目录
		drives = append(drives, &tasksv1.DriveInfo{
			Letter:    "/",
			Path:      "/",
			Label:     "Root",
			DriveType: "fixed",
			Available: true,
		})
	}

	slog.Info(fmt.Sprintf("[文件浏览] 找到 %d 个驱动器", len(drives)))

	return &tasksv1.GetDrivesResponse{
		Code:    0,
		Message: "success",
		Drives:  drives,
	}, nil
}

// 辅助函数

// getRootPath 获取根路径
func getRootPath() string {
	if runtime.GOOS == "windows" {
		return "C:\\"
	}
	return "/"
}

// isRootPath 检查是否为根路径
func isRootPath(path string) bool {
	if runtime.GOOS == "windows" {
		// Windows: C:\, D:\, etc.
		return len(path) == 3 && path[1] == ':' && path[2] == '\\'
	}
	// Unix: /
	return path == "/"
}

// isReadable 检查文件/目录是否可读
func isReadable(path string) bool {
	file, err := os.Open(path)
	if err != nil {
		return false
	}
	file.Close()
	return true
}

// isArchiveFile 检查是否为压缩文件
func isArchiveFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	archiveExts := []string{".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz", ".lz4"}
	for _, archiveExt := range archiveExts {
		if ext == archiveExt {
			return true
		}
	}
	return false
}

// getDriveLabel 获取驱动器标签（Windows）
func getDriveLabel(drive string) string {
	// 简化实现，实际可以调用Windows API获取
	switch drive {
	case "C:\\":
		return "本地磁盘 (C:)"
	default:
		return fmt.Sprintf("本地磁盘 (%s)", drive[:1])
	}
}

// getDriveType 获取驱动器类型（Windows）
func getDriveType(drive string) string {
	// 简化实现，实际可以调用Windows API获取
	return "fixed" // fixed, removable, network, cdrom, ramdisk
}