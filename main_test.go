package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"testing"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/domain"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
)

func GetPackageFile(t *testing.T, i int) {
	addr := "localhost:5555"
	pkg := &domain.Package{Name: "ais-cell", Version: "1.1.4"}
	ctx, cancel := context.WithTimeout(context.Background(), 3*60*time.Second)
	defer cancel()
	conn, err := lib.NewGPRCServerConnection(addr, false)
	if err != nil {
		t.Error(fmt.Errorf("connect to %s error: %v", addr, err))
		return
	}
	defer conn.Close()
	slog.Debug(fmt.Sprintf("[Package(%s):Download] Connected to %s", pkg.Name, addr))
	client := packagesv1.NewPackageServiceClient(conn)
	c, err := client.GetPackageFile(ctx, &packagesv1.Package{
		Name:    pkg.Name,
		Version: pkg.Version,
	})
	if err != nil {
		t.Error(fmt.Errorf("download package(%s) error: %v", pkg.Name, err))
		return
	}
	filename := fmt.Sprintf("/Users/<USER>/Downloads/test/%d", i)
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		t.Error(fmt.Errorf("open %s error: %v", filename, err))
		return
	}
	writer := bufio.NewWriter(file)
	chunkIndex := 0
	for {
		resp, err := c.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			t.Error(fmt.Errorf("recv error: %v", err))
			return
		}
		if resp.GetData() != nil {
			_, err = writer.Write(resp.GetData())
			if err != nil {
				t.Error(fmt.Errorf("write error: %v", err))
				return
			}
			slog.Debug(fmt.Sprintf("[Package(%s):Download] Write chunk[%d] to %s", pkg.Name, chunkIndex, filename))
			chunkIndex++
		}
	}
	err = writer.Flush()
	if err != nil {
		t.Error(fmt.Errorf("flush %s error: %v", filename, err))
		return
	}
	err = file.Close()
	if err != nil {
		t.Error(fmt.Errorf("close %s error: %v", filename, err))
		return
	}
}

func GetPatchFile(t *testing.T, id int32, name string) {
	addr := "localhost:5555"
	// pkg := &domain.Package{Name: "ais-cell", Version: "1.1.4"}
	ctx, cancel := context.WithTimeout(context.Background(), 3*60*time.Second)
	defer cancel()
	conn, err := lib.NewGPRCServerConnection(addr, false)
	if err != nil {
		t.Error(fmt.Errorf("connect to %s error: %v", addr, err))
		return
	}
	defer conn.Close()
	client := packagesv1.NewPackageServiceClient(conn)
	c, err := client.GetPatchFile(ctx, &packagesv1.Patch{
		Id:   id,
		Name: name,
	})
	if err != nil {
		t.Error(fmt.Errorf("download package(%s) error: %v", name, err))
		return
	}
	filename := fmt.Sprintf("/Users/<USER>/Downloads/test/%s", name)
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0600)
	if err != nil {
		t.Error(fmt.Errorf("open %s error: %v", filename, err))
		return
	}
	writer := bufio.NewWriter(file)
	chunkIndex := 0
	for {
		resp, err := c.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			t.Error(fmt.Errorf("recv error: %v", err))
			return
		}
		if resp.GetData() != nil {
			_, err = writer.Write(resp.GetData())
			if err != nil {
				t.Error(fmt.Errorf("write error: %v", err))
				return
			}
			chunkIndex++
		}
	}
	err = writer.Flush()
	if err != nil {
		t.Error(fmt.Errorf("flush %s error: %v", filename, err))
		return
	}
	err = file.Close()
	if err != nil {
		t.Error(fmt.Errorf("close %s error: %v", filename, err))
		return
	}
}

func TestMain(t *testing.T) {
	// wait for download
	// time.Sleep(5 * time.Second)
	// GetPatchFile(t, 2, "20240828160200")
	// patches, err := domain.ListPatches()
	// if err != nil {
	// 	t.Error(fmt.Errorf("list patches error: %v", err))
	// 	return
	// }
	// // t.Logf("patches: %v", patches)
	// for _, patch := range patches {
	// 	t.Logf("patch: %v", patch)
	// }
}
