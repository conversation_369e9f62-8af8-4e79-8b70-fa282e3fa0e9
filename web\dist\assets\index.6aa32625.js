import{r as t,j as s,b as a,s as e,l as o}from"./index.bbeb3af6.js";import r from"./os-status-cpu.aecfc86d.js";import n from"./os-status-memory.324c574a.js";import i from"./os-information.e1bd7535.js";import c from"./os-network.0d203665.js";import"./index.6af89ef4.js";import"./index.cf9faf12.js";var d="_layout_khazc_1",f="_layout-content_khazc_7";function m(){const[m,l]=t.exports.useState({os:{type:"",version:"",arch:""},cpu:{cores:"",usage:""},memory:{total:"",free:"",used:"",usage:""},disk:{total:"",free:"",used:"",usage:""},times:{up:""},networks:[]});return t.exports.useEffect((()=>{o("/api/v1/sysinfo").then((t=>{const s=t&&t.data;if(!s&&!s.data)return;const a=[];for(const[,e]of Object.entries(s.data.networks)){const t="IPv4"===e.family,s=null!=e.mac&&""!==e.mac;t&&s&&a.push(e)}s.data.networks=a,l(s.data)}))}),[]),s("div",{children:s("div",{className:d,children:s("div",{className:f,children:a(e,{size:16,direction:"vertical",style:{width:"100%"},children:[s(i,{data:m}),s(r,{data:m}),s(n,{data:m}),null!=m.networks&&m.networks.length>0?s(c,{data:m}):null]})})})})}export{m as default};
