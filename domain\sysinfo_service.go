package domain

import (
	"fmt"
	"net"
	"runtime"
	"strings"
	"time"

	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/disk"
	"github.com/shirou/gopsutil/host"
	"github.com/shirou/gopsutil/load"
	"github.com/shirou/gopsutil/mem"
	gopsutil_net "github.com/shirou/gopsutil/net"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	sysinfosv1 "gitlab.jhonginfo.com/product/ais-server/proto/sysinfos/v1"
)

func GetSysinfo() *sysinfosv1.Sysinfo {
	conf := config.Get()

	memory, _ := mem.VirtualMemory()
	sysinfo := &sysinfosv1.Sysinfo{
		Os: &sysinfosv1.OS{
			Type:    runtime.GOOS,
			Version: runtime.Version(),
			Arch:    runtime.GOARCH,
		},
		Cpu: &sysinfosv1.CPU{
			Cores: int32(runtime.NumCPU()),
			Usage: "No data",
		},
		Memory: &sysinfosv1.Memory{
			Total: lib.ByteCountBinary(memory.Total),
			Free:  lib.ByteCountBinary(memory.Free),
			Used:  lib.ByteCountBinary(memory.Used),
			Usage: fmt.Sprintf("%.2f%%", memory.UsedPercent),
		},
		Times: &sysinfosv1.Times{
			Up: "No data",
		},
		Server: &sysinfosv1.Server{
			Version:     conf.Version,
			GrpcPort:    conf.GrpcPort,
			GatewayPort: conf.GatewayPort,
			MqttTcpPort: conf.MqttTcpPort,
			MqttWsPort:  conf.MqttWsPort,
		},
	}

	if sysinfo.Os.Type == "darwin" {
		avg, err := load.Avg()
		if err != nil {
			fmt.Printf("load avg error: %v", err)
		} else {
			sysinfo.Cpu.Usage = fmt.Sprintf("Load Average: %.2f, %.2f, %.2f", avg.Load1, avg.Load5, avg.Load15)
		}
	} else {
		stats, err := cpu.Times(false)
		if err != nil {
			fmt.Printf("cpu times error: %v", err)
		} else if len(stats) > 0 {
			total := stats[0].Total()
			idle := stats[0].Idle
			usage := 100 * (total - idle) / total
			sysinfo.Cpu.Usage = fmt.Sprintf("%.2f%%", usage)
		}
	}

	// 获取磁盘相关信息
	disks := make([]*sysinfosv1.Disk, 0)
	partitions, err := disk.Partitions(true)
	if err != nil {
		fmt.Printf("partitions error: %v", err)
	} else {
		for _, p := range partitions {
			disk, err := disk.Usage(p.Mountpoint)
			if err != nil {
				fmt.Printf("disk usage error: %v", err)
			} else {
				d := &sysinfosv1.Disk{
					Name:  p.Device,
					Total: lib.ByteCountBinary(disk.Total),
					Free:  lib.ByteCountBinary(disk.Free),
					Used:  lib.ByteCountBinary(disk.Used),
					Usage: fmt.Sprintf("%.2f%%", disk.UsedPercent),
				}
				disks = append(disks, d)
			}
		}
		sysinfo.Disks = disks
	}

	// 获取系统运行时间
	uptime, err := host.Uptime()
	if err != nil {
		fmt.Printf("uptime error: %v", err)
	} else {
		uptimeDuration := time.Duration(uptime) * time.Second
		sysinfo.Times.Up = uptimeDuration.String()
	}

	// 获取网络信息
	networks := make([]*sysinfosv1.Network, 0)
	interfaces, err := gopsutil_net.Interfaces()
	if err != nil {
		fmt.Printf("interfaces error: %v", err)
	} else {
		for _, i := range interfaces {
			internal := false // 是否为内网地址
			family := ""      // IPv4, IPv6
			if len(i.Addrs) > 0 {
				for _, j := range i.Addrs {
					addr := strings.Split(j.Addr, "/")[0]
					ip := net.ParseIP(addr)
					if ip != nil {
						if ip.To4() != nil {
							family = "IPv4"
						} else {
							family = "IPv6"
						}
						if ip.IsLoopback() || ip.IsLinkLocalUnicast() || ip.IsLinkLocalMulticast() {
							internal = true
						}
					}
					network := &sysinfosv1.Network{
						Name:     i.Name,
						Family:   family,
						Address:  addr,
						Mac:      i.HardwareAddr,
						Internal: internal,
					}
					networks = append(networks, network)
				}
			}
		}
		sysinfo.Networks = networks
	}
	return sysinfo
}
