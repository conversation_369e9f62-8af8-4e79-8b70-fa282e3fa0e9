const i18n = {
  'en-US': {
    'menu.main': 'Main',
    'menu.main.store': 'Store',
    'menu.list': 'List',
    'menu.list.searchTable': 'Search Table',
    'store.const.answer.yes': 'Yes',
    'store.const.answer.no': 'No',
    'store.const.ok': 'OK',
    'store.const.cancel': 'Cancel',
    'store.columns.id': 'ID',
    'store.columns.name': 'Name',
    'store.columns.version': 'Version',
    'store.columns.description': 'Description',
    'store.columns.timestamp': 'Timestamp',
    'store.columns.updatedAt': 'Updated At',
    'store.columns.type': 'Type',
    'store.columns.types.sdk': 'SDK',
    'store.columns.types.svc': 'Service',
    'store.columns.types.file': 'File',
    'store.columns.state': 'State',
    'store.columns.state.uzipped': 'Unzipped',
    'store.columns.state.downloading': 'Downloading',
    'store.columns.state.downloaded': 'Downloaded',
    'store.columns.state.installing': 'Installing',
    'store.columns.state.installed': 'Installed',
    'store.columns.state.exited': 'Exited',
    'store.columns.state.running': 'Running',
    'store.columns.state.unknown': 'Unknown',
    'store.columns.installed': 'Installed',
    'store.columns.upgradable': 'Upgradable',
    'store.columns.operation': 'Operation',
    'store.columns.operation.backup': 'Backup',
    'store.columns.operation.download': 'Download',
    'store.columns.operation.upgrade': 'Upgrade',
    'store.columns.operation.start': 'Start',
    'store.columns.operation.stop': 'Stop',
    'store.columns.operation.install': 'Install',
    'store.columns.operation.uninstall': 'Uninstall',
    'store.columns.operation.view': 'View',
    'store.columns.operation.update': 'Update',
    'store.columns.operation.offline': 'Offline',
    'store.columns.operation.online': 'Online',
    'store.columns.operation.delete': 'Delete',
    'store.columns.operation.republish': 'Republish',
    'store.columns.operation.restore': 'Restore',
    'store.columns.operation.export': 'Export',
    'store.tables.main.form.search': 'Search',
    'store.tables.main.form.reset': 'Reset',
    'store.tables.main.operation.add': 'New',
    'store.tables.main.operation.import': 'Import',
    'store.tables.main.operation.upload': 'Upload',
    'store.tables.main.operation.delete.success': 'Delete successfully',
    'store.tables.main.operation.download': 'Download',
    'store.tables.main.modal.backup.title': 'Backup confirmation',
    'store.tables.main.modal.backup.content': 'Are you sure you want to backup?',
    'store.tables.main.modal.backup.operation.success': 'Backup successfully',
    'store.tables.main.modal.backup.operation.fail': 'Backup failed',
    'store.tables.main.modal.download.title': 'Download confirmation',
    'store.tables.main.modal.download.content': 'Are you sure you want to download?',
    'store.tables.main.modal.download.operation.success': 'Download successfully',
    'store.tables.main.modal.download.operation.fail': 'Download failed',
    'store.tables.main.modal.upgrade.title': 'Upgrade confirmation',
    'store.tables.main.modal.upgrade.content': 'Are you sure you want to upgrade?',
    'store.tables.main.modal.upgrade.operation.success': 'Upgrade successfully',
    'store.tables.main.modal.upgrade.operation.fail': 'Upgrade failed',
    'store.tables.main.modal.install.title': 'Install confirmation',
    'store.tables.main.modal.install.content': 'Are you sure you want to install?',
    'store.tables.main.modal.install.operation.success': 'Install successfully',
    'store.tables.main.modal.install.operation.fail': 'Install failed',
    'store.tables.main.modal.uninstall.title': 'Uninstall confirmation',
    'store.tables.main.modal.uninstall.content': 'Are you sure you want to uninstall?',
    'store.tables.main.modal.uninstall.operation.success': 'Uninstall successfully',
    'store.tables.main.modal.uninstall.operation.fail': 'Uninstall failed',
    'store.tables.main.modal.stop.title': 'Stop confirmation',
    'store.tables.main.modal.stop.content': 'Are you sure you want to stop?',
    'store.tables.main.modal.stop.operation.success': 'Stop successfully',
    'store.tables.main.modal.stop.operation.fail': 'Stop failed',
    'store.tables.main.modal.start.title': 'Start confirmation',
    'store.tables.main.modal.start.content': 'Are you sure you want to start?',
    'store.tables.main.modal.start.operation.success': 'Start successfully',
    'store.tables.main.modal.start.operation.fail': 'Start failed',
    'store.tables.main.modal.delete.title': 'Delete confirmation',
    'store.tables.main.modal.delete.content': 'Are you sure you want to delete this record?',
    'store.tables.main.modal.delete.operation.success': 'Delete successfully',
    'store.tables.main.modal.delete.operation.fail': 'Delete failed',
    'store.tables.main.modal.republish.title': 'Republish confirmation',
    'store.tables.main.modal.republish.content': 'Are you sure you want to republish?',
    'store.tables.main.modal.republish.operation.success': 'Republish successfully',
    'store.tables.main.modal.republish.operation.fail': 'Republish failed',
    'store.tables.main.modal.restore.title': 'Restore confirmation',
    'store.tables.main.modal.restore.content': 'Are you sure you want to restore?',
    'store.tables.main.modal.restore.operation.success': 'Restore successfully',
    'store.tables.main.modal.restore.operation.fail': 'Restore failed',
    'store.tables.main.modal.update.title': 'Update confirmation',
    'store.tables.main.modal.update.content': 'Are you sure you want to update?',
    'store.tables.main.modal.update.operation.success': 'Update successfully',
    'store.tables.main.modal.update.operation.fail': 'Update failed',
    'store.tables.main.modal.export.title': 'Export confirmation',
    'store.tables.main.modal.export.content': 'Are you sure you want to export?',
    'store.tables.main.modal.export.operation.success': 'Export successfully',
    'store.tables.main.modal.export.operation.fail': 'Export failed',
    'store.forms.search.id.placeholder': 'Please enter the ID',
    'store.forms.search.name.placeholder': 'Please enter the name',
    'store.forms.search.version.placeholder': 'Please enter the version',
    'store.forms.search.all.placeholder': 'all',
    'store.notifications.export.title': 'Export notification',
    'store.notifications.export.location': 'Export location',
  },
  'zh-CN': {
    'menu.main': '主页',
    'menu.main.store': '应用仓库',
    'menu.list': '列表页',
    'menu.list.searchTable': '查询表格',
    'store.const.answer.yes': '是',
    'store.const.answer.no': '否',
    'store.const.ok': '确定',
    'store.const.cancel': '取消',
    'store.columns.id': 'ID',
    'store.columns.name': '名称',
    'store.columns.version': '版本',
    'store.columns.type': '类型',
    'store.columns.types.sdk': 'SDK',
    'store.columns.types.svc': '服务',
    'store.columns.types.file': '文件',
    'store.columns.description': '描述',
    'store.columns.state': '状态',
    'store.columns.state.uzipped': '已解压',
    'store.columns.state.downloading': '下载中',
    'store.columns.state.downloaded': '已下载',
    'store.columns.state.installing': '安装中',
    'store.columns.state.installed': '已安装',
    'store.columns.state.exited': '已退出',
    'store.columns.state.running': '运行中',
    'store.columns.state.unknown': '未知',
    'store.columns.timestamp': '时间',
    'store.columns.updatedAt': '更新时间',
    'store.columns.installed': '已安装',
    'store.columns.upgradable': '可升级',
    'store.columns.operation': '操作',
    'store.columns.operation.backup': '备份',
    'store.columns.operation.download': '下载',
    'store.columns.operation.upgrade': '升级',
    'store.columns.operation.start': '启动',
    'store.columns.operation.stop': '停止',
    'store.columns.operation.install': '安装',
    'store.columns.operation.uninstall': '卸载',
    'store.columns.operation.view': '查看',
    'store.columns.operation.update': '更新',
    'store.columns.operation.online': '上线',
    'store.columns.operation.offline': '下线',
    'store.columns.operation.delete': '删除',
    'store.columns.operation.republish': '再版',
    'store.columns.operation.restore': '恢复',
    'store.columns.operation.export': '导出',
    'store.tables.main.form.search': '查询',
    'store.tables.main.form.reset': '重置',
    'store.tables.main.operation.add': '新建',
    'store.tables.main.operation.import': '导入',
    'store.tables.main.operation.upload': '上传',
    'store.tables.main.operation.delete.success': '删除成功',
    'store.tables.main.operation.download': '下载',
    'store.tables.main.modal.backup.title': '备份确认',
    'store.tables.main.modal.backup.content': '确定备份吗？',
    'store.tables.main.modal.backup.operation.success': '备份成功',
    'store.tables.main.modal.backup.operation.fail': '备份失败',
    'store.tables.main.modal.download.title': '下载确认',
    'store.tables.main.modal.download.content': '确定下载吗？',
    'store.tables.main.modal.download.operation.success': '下载成功',
    'store.tables.main.modal.download.operation.fail': '下载失败',
    'store.tables.main.modal.upgrade.title': '升级确认',
    'store.tables.main.modal.upgrade.content': '确定升级吗？',
    'store.tables.main.modal.upgrade.operation.success': '升级成功',
    'store.tables.main.modal.upgrade.operation.fail': '升级失败',
    'store.tables.main.modal.install.title': '安装确认',
    'store.tables.main.modal.install.content': '确定安装吗？',
    'store.tables.main.modal.install.operation.success': '安装成功',
    'store.tables.main.modal.install.operation.fail': '安装失败',
    'store.tables.main.modal.uninstall.title': '卸载确认',
    'store.tables.main.modal.uninstall.content': '确定卸载吗？',
    'store.tables.main.modal.uninstall.operation.success': '卸载成功',
    'store.tables.main.modal.uninstall.operation.fail': '卸载失败',
    'store.tables.main.modal.stop.title': '停用确认',
    'store.tables.main.modal.stop.content': '确定停用吗？',
    'store.tables.main.modal.stop.operation.success': '停用成功',
    'store.tables.main.modal.stop.operation.fail': '停用失败',
    'store.tables.main.modal.start.title': '启用确认',
    'store.tables.main.modal.start.content': '确定启用吗？',
    'store.tables.main.modal.start.operation.success': '启用成功',
    'store.tables.main.modal.start.operation.fail': '启用失败',
    'store.tables.main.modal.delete.title': '删除确认',
    'store.tables.main.modal.delete.content': '确定删除该条记录吗？',
    'store.tables.main.modal.delete.operation.success': '删除成功',
    'store.tables.main.modal.delete.operation.fail': '删除失败',
    'store.tables.main.modal.restore.title': '恢复确认',
    'store.tables.main.modal.restore.content': '确定恢复吗？',
    'store.tables.main.modal.restore.operation.success': '恢复成功',
    'store.tables.main.modal.restore.operation.fail': '恢复失败',
    'store.tables.main.modal.republish.title': '再版确认',
    'store.tables.main.modal.republish.content': '确定再版吗？',
    'store.tables.main.modal.republish.operation.success': '再版成功',
    'store.tables.main.modal.republish.operation.fail': '再版失败',
    'store.tables.main.modal.update.title': '更新确认',
    'store.tables.main.modal.update.content': '确定更新吗？',
    'store.tables.main.modal.update.operation.success': '更新成功',
    'store.tables.main.modal.update.operation.fail': '更新失败',
    'store.tables.main.modal.export.title': '导出确认',
    'store.tables.main.modal.export.content': '确定导出吗？',
    'store.tables.main.modal.export.operation.success': '导出成功',
    'store.tables.main.modal.export.operation.fail': '导出失败',
    'store.forms.search.id.placeholder': '请输入ID',
    'store.forms.search.name.placeholder': '请输入名称',
    'store.forms.search.version.placeholder': '请输入版本',
    'store.forms.search.all.placeholder': '全部',
    'store.notifications.export.title': '导出通知',
    'store.notifications.export.location': '导出位置',
  },
};

export default i18n;
