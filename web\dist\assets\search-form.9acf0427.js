import{u as e,b as a,j as l,af as r,Q as s,B as c,ag as o,G as n}from"./index.e8bac691.js";import{i,s as d}from"./index.module.cf3602d3.js";import{I as h}from"./index.dde5f5a0.js";const{Row:m,Col:t}=n,{useForm:p}=r;function f(n){const f=e(i),[b]=p();return a("div",{className:d["search-form-wrapper"],children:[l(r,{form:b,className:d["search-form"],labelAlign:"left",labelCol:{span:4},wrapperCol:{span:20},children:a(m,{gutter:24,children:[l(t,{span:12,children:l(r.Item,{label:f["searchTable.columns.id"],field:"id",children:l(s,{placeholder:f["searchForm.id.placeholder"],allowClear:!0})})}),l(t,{span:12,children:l(r.Item,{label:f["searchTable.columns.name"],field:"name",children:l(s,{allowClear:!0,placeholder:f["searchForm.name.placeholder"]})})}),l(t,{span:12,children:l(r.Item,{label:f["searchTable.columns.version"],field:"version",children:l(s,{allowClear:!0,placeholder:f["searchForm.version.placeholder"]})})})]})}),a("div",{className:d["right-button"],children:[l(c,{type:"primary",icon:l(o,{}),onClick:()=>{const e=b.getFieldsValue();n.onSearch(e)},children:f["searchTable.form.search"]}),l(c,{icon:l(h,{}),onClick:()=>{b.resetFields(),n.onSearch({})},children:f["searchTable.form.reset"]})]})]})}export{f as default};
