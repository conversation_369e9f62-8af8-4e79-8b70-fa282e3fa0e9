import{ap as e,aK as a,u as t,r,o,p as n,j as i,b as l,aq as s,B as c,af as m,Q as d,i as p,O as f,s as h,l as u,k}from"./index.bbeb3af6.js";import{I as b,U as g}from"./index.66b4456e.js";import{C as S}from"./index.cf9faf12.js";import"./index.1e740865.js";const v={"en-US":{"form.name.label":"Name","form.name.required":"Please enter the name","form.name.placeholder":"Please enter the name","form.attachment.label":"Attachment","form.attachment.required":"Please upload the attachment","form.attachment.support.tips":"Only zip can be uploaded","form.attachment.support-error.tips":"Unacceptable file type, please re-upload the specified file type!","form.patch.label":"Download from server","form.patch.placeholder":"Please select the corresponding resource file","form.confirm.title":"Update confirmation","form.confirm.content":"Do you want to confirm the update?","form.updated.fail":"Update failed","form.updated.success":"Update success","form.operations.back":"Back","form.operations.submit":"Submit","form.validation.bothEmpty.error":"Selected resource and attachment cannot be empty at the same time"},"zh-CN":{"form.name.label":"名称","form.name.required":"请输入名称","form.name.placeholder":"请输入名称","form.attachment.label":"附件","form.attachment.required":"请上传附件","form.attachment.support.tips":"仅支持zip上传","form.attachment.support-error.tips":"不接受的文件类型，请重新上传指定文件类型！","form.patch.label":"从服务器下载","form.patch.placeholder":"请选择相应的资源文件","form.confirm.title":"更新确认","form.confirm.content":"是否确认更新？","form.updated.fail":"更新失败","form.updated.success":"更新成功","form.operations.back":"返回","form.operations.submit":"提交","form.validation.bothEmpty.error":"选中资源和附件不能同时为空"}};var y="_container_1klic_1",z="_wrapper_1klic_7",_="_form_1klic_13";const{useForm:I}=m;function E(){const E=e(),x=a(),C=t(v),[R,w]=r.exports.useState(!1),[q]=o(n.KEY_AIS_SERVER_PARENT_HOST),[N]=o(n.KEY_AIS_SERVER_PARENT_API_SERVER),[A,U]=r.exports.useState([]),O=""===q||"localhost"===q||"127.0.0.1"===q,[P]=I(),j=async(e,a)=>{null!=e.chunkContent&&u("/api/v1/upload-tasks/"+e.id+"/chunks",{method:"post",data:{id:e.id,chunkIndex:e.chunkIndex,chunkSize:e.chunkSize,chunkContent:e.chunkContent,totalChunks:e.totalChunks,totalSize:e.totalSize},timeout:6e4}).then((e=>{const t=e&&e.data;t&&t.code,a&&a(t.data)})).catch((()=>{a&&a({state:"failed"})}))},F=async(e,a)=>{w(!0),(async(e,a)=>{u("/api/v1/upload-tasks",{method:"post",data:{packageName:e.name,packageVersion:e.version}}).then((e=>{const t=e&&e.data;t&&0===t.code?a&&a(t.data):p.error(C["form.updated.fail"])})).catch((()=>{p.error(C["form.updated.fail"])}))})(e,(t=>{const r=t.id,o=Math.ceil(e.totalSize/10485760);for(let n=0;n<o;n++){const t=n,i=10485760*n,l=Math.min(e.totalSize,i+10485760),s=a.slice(i,l),c=new FileReader;c.onloadend=function(){let a=c.result;a&&(a=String(a).replace(/^data:.+;base64,/,""),j({id:r,chunkIndex:t,chunkContent:a,chunkSize:l-i,totalChunks:o,totalSize:e.totalSize},(e=>{e&&e.state&&"finished"===e.state&&V({id:r,packageId:e.packageId})})).catch((()=>{p.error(C["form.updated.fail"]),w(!1)})))},c.readAsDataURL(s)}}))},V=async e=>{w(!0),u("/api/v1/tasks",{method:"post",data:{id:e.id,name:"update",packageId:e.packageId,patchName:e.patchName}}).then((e=>{const a=e&&e.data;(a||a.data)&&("failed"===a.data.state?p.error(C["form.updated.fail"]):(p.success(C["form.updated.success"]),E.push({pathname:"/main/store"})))})).catch((()=>{p.error(C["form.updated.fail"])})).finally((()=>{w(!1)}))};return r.exports.useEffect((()=>{if(x.state){w(!0);const a=x.state;P.setFieldsValue(a),w(!1),console.log(a),O||(e=a.id,u(`/api/v1/packages/${e}/patches`,{serverUrl:N}).then((e=>{const a=e&&e.data;a&&0===a.code&&(console.log(a.data),U(a.data))})))}else E.push({pathname:"/main/store"});var e}),[]),i("div",{className:y,children:l(S,{children:[i(s,{to:"/main/store",children:i(c,{loading:R,type:"primary",icon:i(b,{}),children:C["form.operations.back"]})}),i("div",{className:z,children:l(m,{form:P,className:_,children:[l(m.Item,{noStyle:!0,children:[i(m.Item,{label:C["form.name.label"],disabled:!0,required:!0,field:"name",rules:[{required:!0,message:C["form.name.required"]},{validator:(e,a)=>{/^[a-zA-Z0-9-_]{1,20}$/g.test(e)||a(C["form.name.placeholder"])}}],children:i(d,{placeholder:C["form.name.placeholder"]})}),i(m.Item,{label:C["form.attachment.label"],required:O,field:"attachment",rules:[{required:O,message:C["form.attachment.required"]}],children:i(g,{drag:!0,accept:".zip",autoUpload:!1,limit:1,onDrop:e=>{((e,a)=>{if(a&&e){const t=Array.isArray(a)?a:a.split(",").map((e=>e.trim())).filter((e=>e)),r=e.name.indexOf(".")>-1?e.name.split(".").pop():"";return t.some((a=>{const t=a&&a.toLowerCase(),o=(e.type||"").toLowerCase();if(t===o)return!0;if(new RegExp("/*").test(t)){const e=new RegExp("/.*$");return o.replace(e,"")===t.replace(e,"")}return!!new RegExp("..*").test(t)&&t===`.${r&&r.toLowerCase()}`}))}return!!e})(e.dataTransfer.files[0],["application/zip",".zip"])||p.info(C["form.attachment.support-error.tips"])},tip:C["form.attachment.support.tips"]})}),!O&&i(m.Item,{label:C["form.patch.label"],field:"patch",children:i(f,{placeholder:C["form.patch.placeholder"],allowClear:!0,children:A.map(((e,a)=>i(f.Option,{value:e,children:e},a)))})})]}),i(m.Item,{label:" ",children:i(h,{children:i(c,{loading:R,type:"primary",size:"large",onClick:async()=>{try{await P.validate();const e=P.getFields(),a={name:null,version:null,description:null,size:null,location:null,timestamp:null,chunkIndex:null,chunkContent:null,chunkSize:null,totalChunks:null,totalSize:null};if(e.attachment&&e.attachment.length>0){const t=e.attachment[0].originFile;e.name&&e.name.length>0?a.name=e.name:a.name=t.name.split(".")[0],e.version&&e.version.length>0?a.version=e.version:a.version=String(t.lastModified),a.totalSize=t.size,k.confirm({title:C["form.confirm.title"],content:C["form.confirm.content"],onOk:()=>{F(a,t)}})}else e.patch?k.confirm({title:C["form.confirm.title"],content:C["form.confirm.content"],onOk:()=>{V({packageId:e.id,patchName:e.patch})}}):p.error(C["form.validation.bothEmpty.error"])}catch(e){}},children:C["form.operations.submit"]})})})]})})]})})}export{E as default};
