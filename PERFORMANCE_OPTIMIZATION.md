# AIS-Server 安装部署性能优化

## 概述

针对 ais-cell 等大量碎片文件的安装部署场景，我们实现了一套全面的性能优化方案，显著提升安装速度并确保文件完整性。

## 优化特性

### 1. 统一高性能处理
- **默认使用高性能方案**：所有包都使用高性能安装部署方案
- **无需检测**：直接应用最优化的处理策略
- **适用所有场景**：无论是大文件还是碎片文件都能获得最佳性能

### 2. 高性能解压缩
- **专门优化的解压算法**：针对大量碎片文件场景
- **智能策略选择**：
  - 小文件优先：使用更多并发线程，批量处理
  - 平衡策略：大文件和小文件分别优化处理
- **并发解压**：支持多线程并发解压，充分利用CPU资源
- **进度监控**：实时显示解压进度和预计剩余时间

### 3. 高性能文件复制
- **并发复制**：多线程并发复制文件
- **大缓冲区**：使用1MB缓冲区提升I/O性能
- **批量处理**：批量处理文件操作，减少系统调用开销
- **智能重试**：失败文件自动重试机制

### 4. 优化的系统脚本
- **Windows robocopy 优化**：
  - 使用8线程多线程复制 (`/MT:8`)
  - 无缓冲I/O模式 (`/J`) 适合大文件
  - 减少重试次数和等待时间
- **Unix系统优化**：使用高性能复制库替代标准cp命令

## 配置参数

在 `config.yml` 中添加以下性能配置：

```yaml
# 解压缩性能配置
extract_threads: 0          # 0 = 使用所有 CPU 核心
extract_queue_size: 10      # 队列大小
extract_large_file: 104857600  # 100MB 阈值
extract_high_perf: true     # 启用高性能模式

# 文件复制性能配置
copy_buffer_size: 1048576   # 1MB 缓冲区
copy_workers: 4             # 4个并发工作线程
copy_batch_size: 100        # 批量处理100个文件
```

## 性能提升效果

### 预期性能提升
- **解压速度**：提升 3-5 倍（大量小文件场景）
- **复制速度**：提升 2-4 倍（并发复制）
- **整体安装时间**：减少 50-70%

### 适用场景
- ✅ ais-cell 等包含大量碎片文件的包
- ✅ node_modules 等前端依赖包
- ✅ 包含数千个小文件的应用包
- ✅ 网络存储或慢速磁盘环境

## 使用方法

### 1. 默认高性能处理（推荐）
系统默认对所有包使用高性能处理：

```go
// 所有包都使用高性能处理
task := &Task{Name: "ais-cell", Version: "1.0.0"}
err := task.ProcessPackage() // 默认使用高性能方案
```

### 2. 手动配置
可以通过配置文件调整性能参数：

```yaml
# 针对特定环境优化
extract_threads: 8          # 8核CPU使用8线程
copy_workers: 6             # 6个并发复制线程
copy_buffer_size: 2097152   # 2MB缓冲区（SSD环境）
```

### 3. 监控和日志
优化过程会输出详细的进度信息：

```
[高性能解压] ZIP文件分析完成: 总文件数=15000, 小文件=14500, 大文件=500
[高性能解压] 开始处理 500 个大文件
[高性能解压] 开始处理 14500 个小文件
[优化安装] 开始高性能复制: /src -> /dst
[优化安装] ais-cell 复制进度: 45.2% - 已处理: 6780/15000 文件, 预计剩余: 2m15s
```

## 故障恢复

### 自动回退机制
如果优化方案失败，系统会自动回退到标准方案：

1. **高性能解压失败** → 回退到优化标准库解压
2. **优化标准库失败** → 回退到标准解压
3. **高性能复制失败** → 回退到标准安装脚本

### 错误处理
- 详细的错误日志记录
- 失败文件列表和重试机制
- 进度状态保存和恢复

## 最佳实践

### 1. 硬件配置建议
- **CPU**：多核心处理器（4核以上）
- **内存**：8GB以上（大量小文件需要更多内存）
- **存储**：SSD硬盘（显著提升I/O性能）

### 2. 环境配置
- **Windows**：确保有足够的磁盘空间和权限
- **Linux/macOS**：确保文件系统支持并发操作

### 3. 监控建议
- 监控CPU使用率（应保持在80%以下）
- 监控内存使用情况
- 监控磁盘I/O性能

## 故障排除

### 常见问题

1. **内存不足**
   - 减少 `extract_threads` 和 `copy_workers`
   - 增加系统内存或使用虚拟内存

2. **磁盘I/O瓶颈**
   - 减少 `copy_buffer_size`
   - 降低并发线程数

3. **权限问题**
   - 确保有足够的文件系统权限
   - Windows需要管理员权限

### 性能调优
根据具体环境调整参数：

```yaml
# 高性能环境（SSD + 多核CPU）
extract_threads: 0
copy_workers: 8
copy_buffer_size: 2097152

# 低性能环境（HDD + 双核CPU）
extract_threads: 2
copy_workers: 2
copy_buffer_size: 524288
```

## 技术实现

### 核心组件
- `lib/extract_optimized.go`：高性能解压缩引擎
- `lib/copy_optimized.go`：高性能文件复制引擎
- `domain/package.go`：智能包管理和安装优化
- `domain/task_service.go`：任务处理和进度监控

### 算法特点
- **工作池模式**：使用goroutine池处理并发任务
- **流水线处理**：解压和复制操作流水线化
- **自适应调度**：根据文件大小和数量动态调整策略
- **内存优化**：复用缓冲区，减少GC压力

这套优化方案专门针对 ais-cell 等大量碎片文件的场景设计，在保证文件完整性的前提下，显著提升安装部署速度。
