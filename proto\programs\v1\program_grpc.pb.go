// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: programs/v1/program.proto

package programsv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PackageService_ListPrograms_FullMethodName   = "/programs.v1.PackageService/ListPrograms"
	PackageService_ExecuteProgram_FullMethodName = "/programs.v1.PackageService/ExecuteProgram"
	PackageService_DeleteProgram_FullMethodName  = "/programs.v1.PackageService/DeleteProgram"
)

// PackageServiceClient is the client API for PackageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PackageServiceClient interface {
	ListPrograms(ctx context.Context, in *ListProgramsRequest, opts ...grpc.CallOption) (*ListProgramsResponse, error)
	ExecuteProgram(ctx context.Context, in *ProgramCommandRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	DeleteProgram(ctx context.Context, in *Program, opts ...grpc.CallOption) (*CommonResponse, error)
}

type packageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPackageServiceClient(cc grpc.ClientConnInterface) PackageServiceClient {
	return &packageServiceClient{cc}
}

func (c *packageServiceClient) ListPrograms(ctx context.Context, in *ListProgramsRequest, opts ...grpc.CallOption) (*ListProgramsResponse, error) {
	out := new(ListProgramsResponse)
	err := c.cc.Invoke(ctx, PackageService_ListPrograms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) ExecuteProgram(ctx context.Context, in *ProgramCommandRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, PackageService_ExecuteProgram_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *packageServiceClient) DeleteProgram(ctx context.Context, in *Program, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, PackageService_DeleteProgram_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PackageServiceServer is the server API for PackageService service.
// All implementations should embed UnimplementedPackageServiceServer
// for forward compatibility
type PackageServiceServer interface {
	ListPrograms(context.Context, *ListProgramsRequest) (*ListProgramsResponse, error)
	ExecuteProgram(context.Context, *ProgramCommandRequest) (*CommonResponse, error)
	DeleteProgram(context.Context, *Program) (*CommonResponse, error)
}

// UnimplementedPackageServiceServer should be embedded to have forward compatible implementations.
type UnimplementedPackageServiceServer struct {
}

func (UnimplementedPackageServiceServer) ListPrograms(context.Context, *ListProgramsRequest) (*ListProgramsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPrograms not implemented")
}
func (UnimplementedPackageServiceServer) ExecuteProgram(context.Context, *ProgramCommandRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteProgram not implemented")
}
func (UnimplementedPackageServiceServer) DeleteProgram(context.Context, *Program) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProgram not implemented")
}

// UnsafePackageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PackageServiceServer will
// result in compilation errors.
type UnsafePackageServiceServer interface {
	mustEmbedUnimplementedPackageServiceServer()
}

func RegisterPackageServiceServer(s grpc.ServiceRegistrar, srv PackageServiceServer) {
	s.RegisterService(&PackageService_ServiceDesc, srv)
}

func _PackageService_ListPrograms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListProgramsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ListPrograms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_ListPrograms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ListPrograms(ctx, req.(*ListProgramsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_ExecuteProgram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProgramCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).ExecuteProgram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_ExecuteProgram_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).ExecuteProgram(ctx, req.(*ProgramCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PackageService_DeleteProgram_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Program)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PackageServiceServer).DeleteProgram(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PackageService_DeleteProgram_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PackageServiceServer).DeleteProgram(ctx, req.(*Program))
	}
	return interceptor(ctx, in, info, handler)
}

// PackageService_ServiceDesc is the grpc.ServiceDesc for PackageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PackageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "programs.v1.PackageService",
	HandlerType: (*PackageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPrograms",
			Handler:    _PackageService_ListPrograms_Handler,
		},
		{
			MethodName: "ExecuteProgram",
			Handler:    _PackageService_ExecuteProgram_Handler,
		},
		{
			MethodName: "DeleteProgram",
			Handler:    _PackageService_DeleteProgram_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "programs/v1/program.proto",
}
