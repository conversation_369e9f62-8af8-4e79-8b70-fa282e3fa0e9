package domain

import (
	"context"
	"fmt"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/config"
	"gitlab.jhonginfo.com/product/ais-server/lib"
	clientsv1 "gitlab.jhonginfo.com/product/ais-server/proto/clients/v1"
)

func (b *Backend) ListClients(_ context.Context, req *clientsv1.Client) (*clientsv1.ListClientsResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	do := ClientVO2PO(req)
	clients, err := do.List()
	if err != nil {
		return nil, err
	}
	return &clientsv1.ListClientsResponse{
		Code: 0,
		Data: ClientPOs2VOs(clients),
	}, nil
}

func (b *Backend) SaveClient(_ context.Context, req *clientsv1.Client) (*clientsv1.ClientResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	client := ClientVO2PO(req)
	parentMQTTServer := client.GetParentMQTTServer()
	if parentMQTTServer != "" {
		if !CheckServerConnection(parentMQTTServer) {
			return nil, fmt.Errorf("parent mqtt server is not connected")
		}
	}
	var err error
	if client.Id == "" {
		client.Id = lib.NewUUID()
	}
	old := &Client{Id: client.Id}
	err = old.GetById()
	if err == nil {
		client = MergeClient(client, old)
	} else {
		client.CreatedAt = time.Now()
	}
	currentId, getIdErr := config.GetId()
	if getIdErr != nil {
		return nil, getIdErr
	}
	if client.Id != *currentId {
		client.IsLocal = 0
	}
	client.UpdatedAt = time.Now()
	err = client.Save()
	if err != nil {
		return nil, err
	}
	return &clientsv1.ClientResponse{
		Code: 0,
		Data: client.ToVO(),
	}, nil
}

func (b *Backend) DeleteClient(_ context.Context, req *clientsv1.DeleteClientRequest) (*clientsv1.ClientResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	do := &Client{
		Id: req.Id,
	}
	err := do.DeleteByID()
	if err != nil {
		return nil, err
	}
	return &clientsv1.ClientResponse{
		Code: 0,
	}, nil
}

func (b *Backend) GetCurrent(context.Context, *clientsv1.GetCurrentRequest) (*clientsv1.ClientResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	emptyResponse := &clientsv1.ClientResponse{
		Code: 0,
		Data: nil,
	}
	po, err := GetAndSaveCurrent()
	if err != nil {
		return emptyResponse, nil
	}
	return &clientsv1.ClientResponse{
		Code: 0,
		Data: po.ToVO(),
	}, nil
}

func (b *Backend) SaveCurrent(_ context.Context, req *clientsv1.Client) (*clientsv1.ClientResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	client := ClientVO2PO(req)
	parentMQTTServer := client.GetParentMQTTServer()
	if parentMQTTServer != "" {
		if !CheckServerConnection(parentMQTTServer) {
			return nil, fmt.Errorf("parent mqtt server is not connected")
		}
	}
	current, err := GetCurrentClient()
	if err != nil {
		return nil, err
	}
	needInterrupt := current.GetParentMQTTServer() != parentMQTTServer
	client = MergeClient(client, current)
	err = client.Update()
	if err != nil {
		return nil, err
	}
	if needInterrupt {
		err = current.Reconnect()
		if err != nil {
			return nil, err
		}
	}
	return &clientsv1.ClientResponse{
		Code: 0,
		Data: client.ToVO(),
	}, nil
}
