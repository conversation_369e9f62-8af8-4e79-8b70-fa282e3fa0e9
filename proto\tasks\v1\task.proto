syntax = "proto3";

package tasks.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// These annotations are used when generating the OpenAPI file.
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {version: "1.0"};
  external_docs: {
    url: "http://gitlab.jhonginfo.com/product/ais-server";
    description: "AIS Server";
  }
  schemes: HTTPS;
};

service TaskService {
  rpc ExecuteTask(Task) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/tasks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Execute a task"
      description: "Execute a task on the server"
      tags: "Tasks"
    };
  }

  rpc GetTask(GetTaskRequest) returns (TaskResponse) {
    option (google.api.http) = {
      get: "/api/v1/tasks/{id}"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get a task"
      description: "Get a task from the server"
      tags: "Tasks"
    };
  }

  rpc CreateTaskForUploadPackage(UploadRequest) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/upload-package-tasks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create a upload package task"
      description: "Create a upload package task on the server"
      tags: "Tasks"
    };
  }

  rpc ExecuteTaskForUploadPackage(UploadRequest) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/upload-package-tasks/{id}/chunks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Execute a upload package task"
      description: "Execute a upload package task on the server"
      tags: "Tasks"
    };
  }

  rpc CreateTaskForUpload(UploadRequest) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/upload-tasks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create a upload task"
      description: "Create a upload task on the server"
      tags: "Tasks"
    };
  }

  rpc ExecuteTaskForUpload(UploadRequest) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/upload-tasks/{id}/chunks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Execute a upload task"
      description: "Execute a upload task on the server"
      tags: "Tasks"
    };
  }

  rpc CreateTaskForLocalImport(LocalImportRequest) returns (TaskResponse) {
    option (google.api.http) = {
      post: "/api/v1/local-import-tasks"
      body: "*"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create a local import task"
      description: "Create a local import task to import local files directly"
      tags: "Tasks"
    };
  }

  rpc BrowseLocalFiles(BrowseFilesRequest) returns (BrowseFilesResponse) {
    option (google.api.http) = {
      get: "/api/v1/browse-files"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Browse local files"
      description: "Browse local file system to select files"
      tags: "Files"
    };
  }

  rpc GetSystemDrives(GetDrivesRequest) returns (GetDrivesResponse) {
    option (google.api.http) = {
      get: "/api/v1/system-drives"
      response_body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get system drives"
      description: "Get available system drives/volumes"
      tags: "Files"
    };
  }
}

message TaskResponse {
  int32 code = 1;
  string message = 2;
  Task data = 3;
}

message GetTaskRequest {
  int32 id = 1;
}

message Task {
  int32 id = 1;
  string name = 2;
  string type = 3;
  string content = 4;
  string state = 5;
  int32 package_id = 6;
  string patch_name = 7;
  string progress = 8;
  string created_at = 9;
  string updated_at = 10;
}

message UploadRequest {
  int32 id = 1;
  string package_name = 2;
  string package_version = 3;
  int32 chunk_index = 4;
  int32 chunk_size = 5;
  bytes chunk_content = 6;
  int32 total_chunks = 7;
  int32 total_size = 8;
}

message LocalImportRequest {
  string package_name = 1;
  string package_version = 2;
  string local_path = 3;
}

message BrowseFilesRequest {
  string path = 1;
}

message BrowseFilesResponse {
  int32 code = 1;
  string message = 2;
  string current_path = 3;
  repeated FileInfo files = 4;
}

message FileInfo {
  string name = 1;
  string path = 2;
  bool is_dir = 3;
  int64 size = 4;
  string mod_time = 5;
  bool readable = 6;
}

message GetDrivesRequest {
}

message GetDrivesResponse {
  int32 code = 1;
  string message = 2;
  repeated DriveInfo drives = 3;
}

message DriveInfo {
  string letter = 1;
  string path = 2;
  string label = 3;
  string drive_type = 4;
  bool available = 5;
}