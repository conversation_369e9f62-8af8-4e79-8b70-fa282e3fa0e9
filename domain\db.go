package domain

import (
	"fmt"
	"log"

	"github.com/glebarez/sqlite"
	"gitlab.jhonginfo.com/product/ais-server/config"
	"gorm.io/gorm"
)

type SQLiteConnection struct {
	db *gorm.DB // SQLITE数据库连接对象
}

var instance *SQLiteConnection // 全局变量，存放SQLiteConnection实例

func GetDB() (*gorm.DB, error) {
	config := config.Get()
	db, err := gorm.Open(sqlite.Open(config.DatabaseFile), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect database: %v", err)
		return nil, err
	}
	return db, nil
}

func GetSQLiteConnectionInstance() (*SQLiteConnection, error) {
	// 如果instance为空，创建一个新的SQLiteConnection实例
	if instance == nil {
		db, err := GetDB()
		if err != nil {
			log.Fatalf("Failed to get database connection: %v", err)
			return nil, err
		}
		instance = &SQLiteConnection{db: db}
	} else {
		// 如果instance不为空，检查数据库连接是否已经关闭
		cdb, err := instance.db.DB()
		if err != nil {
			log.Fatalf("Failed to get database connection: %v", err)
			return nil, err
		}
		if err := cdb.Ping(); err != nil {
			instance.db, err = GetDB()
			if err != nil {
				log.Fatalf("Failed to get database connection: %v", err)
				return nil, err
			}
		}
	}
	return instance, nil
}

func GetDBInstance() (*gorm.DB, error) {
	conn, err := GetSQLiteConnectionInstance()
	if err != nil {
		return nil, err
	}
	db := conn.db
	return db, nil
}

func InitializeDB() error {
	conn, err := GetSQLiteConnectionInstance()
	if err != nil {
		return err
	}
	// 自动创建和更新表结构
	err = conn.db.AutoMigrate(&Client{}, &Log{}, &Package{}, &Task{})
	if err != nil {
		return err
	}
	// 关闭数据库连接
	db, err := conn.db.DB()
	if err != nil {
		return err
	}
	defer db.Close()
	return nil
}

func Add(model interface{}) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	return db.Create(model).Error
}

func Save(model interface{}) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	return db.Save(model).Error
}

func Count(model interface{}) (int64, error) {
	db, err := GetDBInstance()
	if err != nil {
		return 0, err
	}
	var count int64
	err = db.Model(model).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func FindOneByID(id any, model interface{}) error {
	if id == nil {
		return fmt.Errorf("id is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	return db.First(model, "id = ?", id).Error
}

func FindAll(model interface{}) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Find(model).Error
	if err != nil {
		return err
	}
	return nil
}

func DeleteByID(id any, model interface{}) error {
	if id == nil {
		return fmt.Errorf("id is empty")
	}
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Where("id = ?", id).Delete(model).Error
	if err != nil {
		return err
	}
	return nil
}

func DeleteAll(model interface{}) error {
	db, err := GetDBInstance()
	if err != nil {
		return err
	}
	err = db.Where("1 = 1").Delete(model).Error
	if err != nil {
		return err
	}
	return nil
}
