package domain

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitlab.jhonginfo.com/product/ais-server/lib"
	packagesv1 "gitlab.jhonginfo.com/product/ais-server/proto/packages/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (b *Backend) ListPackages(_ context.Context, req *packagesv1.ListPackagesRequest) (*packagesv1.ListPackagesResponse, error) {
	var pkgs []*Package
	var data *packagesv1.ListPackagesResponseData
	var total int64
	var err error

	page := int(req.Page)
	size := int(req.Size)

	id := req.GetId()
	name := req.GetName()
	version := req.GetVersion()
	if id != 0 {
		pkg := &Package{
			Id: id,
		}
		err := pkg.GetById()
		if err != nil {
			total = 0
			pkgs = []*Package{}
		} else {
			total = 1
			pkgs = append(pkgs, pkg)
		}
	} else if name != "" {
		do := &Package{
			Name:    name,
			Version: version,
		}
		total, err = do.CountByNameAndVersion()
		if err != nil {
			return nil, err
		}
		pkgs, err = do.ListByNameAndVersion()
		if err != nil {
			pkgs = []*Package{}
		}
	} else {
		do := &Package{}
		total, err = do.Count()
		if err != nil {
			return nil, err
		}
		pkgs, err = do.List()
		if err != nil {
			pkgs = []*Package{}
		}
	}
	if page > 0 && size > 0 {
		data = &packagesv1.ListPackagesResponseData{
			Total:   int32(total),
			Page:    int32(page),
			Size:    int32(size),
			Content: PackagePOs2PackageVOs(pkgs),
		}
	} else {
		data = &packagesv1.ListPackagesResponseData{
			Content: PackagePOs2PackageVOs(pkgs),
		}
	}
	return &packagesv1.ListPackagesResponse{
		Code: 0,
		Data: data,
	}, nil
}

func (b *Backend) ListPatchesByPackageId(_ context.Context, req *packagesv1.Package) (*packagesv1.ListPatchesResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkgId := req.GetId()
	pkg, err := GetPackageById(pkgId)
	if err != nil {
		return nil, err
	}
	patchesDir, err := pkg.GetPatchesDir()
	if err != nil {
		return nil, err
	}
	files, err := lib.ListFiles(patchesDir)
	if err != nil {
		return nil, err
	}
	filenames := make([]string, 0)
	for _, file := range files {
		filepaths := strings.Split(file, string(filepath.Separator))
		filename := filepaths[len(filepaths)-1]
		filenames = append(filenames, filename)
	}
	return &packagesv1.ListPatchesResponse{
		Code: 0,
		Data: filenames,
	}, nil
}

func (b *Backend) SavePackage(_ context.Context, req *packagesv1.Package) (*packagesv1.PackageResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	var po *Package
	oldPackage := &Package{
		Name:    req.GetName(),
		Version: req.GetVersion(),
	}
	err := oldPackage.GetByUniqueIndex()
	if err != nil {
		po = PackageVO2PackagePO(req)
		if po.Type == "" {
			po.Type = PackageTypes.FILE
		}
		if po.State == "" {
			po.State = PackageStates.Unknown
		}
	} else {
		po = PackageVO2PackagePO(req)
		po.Merge(oldPackage)
	}
	po.UpdatedAt = time.Now()
	err = po.Save()
	if err != nil {
		return nil, err
	}
	return &packagesv1.PackageResponse{
		Code: 0,
		Data: po.ToVO(),
	}, nil
}

func (b *Backend) UploadPackage(ctx context.Context, req *packagesv1.UploadPackageRequest) (*packagesv1.PackageResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkgId := req.GetId()
	pkg, err := GetPackageById(pkgId)
	if err != nil {
		return nil, err
	}

	location, err := pkg.GetLocation()
	if err != nil {
		return nil, err
	}

	chunkIndex := req.GetChunkIndex()
	// chunkSize := req.GetChunkSize()
	totalChunks := req.GetTotalChunks()
	chunkContent := req.GetChunkContent()
	merged := req.GetMerged()
	if merged == 1 {
		if _, err := os.Stat(*location); !os.IsNotExist(err) {
			err = os.Remove(*location)
			if err != nil {
				return nil, err
			} else {
				slog.Debug("Remove " + *location)
			}
		}
		fullFile, err := os.OpenFile(*location, os.O_CREATE|os.O_WRONLY, 0600)
		if err != nil {
			return nil, err
		}
		defer fullFile.Close()
		for i := 0; i < int(totalChunks); i++ {
			tmpFilename := fmt.Sprintf("%s_%d", *location, i)
			tmpFile, err := os.OpenFile(tmpFilename, os.O_RDONLY, 0600)
			if err != nil {
				return nil, err
			}
			_, err = io.Copy(fullFile, tmpFile)
			if err != nil {
				return nil, err
			}
			tmpFile.Close()
			err = os.Remove(tmpFilename)
			if err != nil {
				return nil, err
			}
			slog.Debug(fmt.Sprintf("Write temp file %s to %s", tmpFilename, *location))
		}
		// 判断location文件是否存在
		if _, err := os.Stat(*location); os.IsNotExist(err) {
			return nil, fmt.Errorf("package file not exists")
		}
		if !UnzipPackage(pkg) {
			return nil, fmt.Errorf("failed to unzip package")
		}
	} else {
		tmpFilename := fmt.Sprintf("%s_%d", *location, chunkIndex)
		file, err := os.OpenFile(tmpFilename, os.O_CREATE|os.O_WRONLY, 0600)
		if err != nil {
			return nil, err
		}
		writer := bufio.NewWriter(file)
		if _, err := writer.Write(chunkContent); err != nil {
			return nil, err
		}
		writer.Flush()
		file.Close()
		slog.Debug(fmt.Sprintf("Write chunk[%d] to %s, total %d", chunkIndex, tmpFilename, totalChunks))
	}
	return &packagesv1.PackageResponse{
		Code: 0,
		Data: pkg.ToVO(),
	}, nil
}

func (b *Backend) DownloadPackage(_ context.Context, req *packagesv1.DownloadPackageRequest) (*packagesv1.PackageResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkg, err := GetPackageByName(req.Name)
	if err != nil {
		return nil, fmt.Errorf("package(%s) not found", req.Name)
	}
	if !DownloadPackage(pkg) {
		return nil, fmt.Errorf("failed to download package")
	}
	return &packagesv1.PackageResponse{
		Code: 0,
		Data: pkg.ToVO(),
	}, nil
}

func (b *Backend) GetPackageFile(req *packagesv1.Package, srv packagesv1.PackageService_GetPackageFileServer) error {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkg := &Package{
		Name:    req.GetName(),
		Version: req.GetVersion(),
	}
	err := pkg.GetByUniqueIndex()
	if err != nil {
		return status.Errorf(codes.Internal, "failed to get package by unique index: %v", err)
	}
	if pkg.Uri == "" {
		return status.Errorf(codes.NotFound, "package URI is empty")
	}
	file, err := os.Open(pkg.Uri)
	if err != nil {
		return err
	}
	defer file.Close()

	buffer := make([]byte, 1024*1024) // 1MB 缓冲区
	for {
		n, err := file.Read(buffer)
		if err != nil {
			if err == io.EOF {
				err = nil
			}
			return err
		}
		if n == 0 {
			return nil
		}
		if err := srv.Send(&packagesv1.FileResponse{Data: buffer[:n]}); err != nil {
			return err
		}
	}
}

func (b *Backend) DeletePackage(_ context.Context, req *packagesv1.Package) (*packagesv1.PackageResponse, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkg := &Package{
		Id: req.GetId(),
	}
	err := pkg.GetById()
	if err != nil {
		return nil, err
	}
	err = pkg.Delete()
	if err != nil {
		slog.Error(fmt.Sprintf("Delete package(%s) error: %s", pkg.Name, err.Error()))
		return nil, err
	}
	return &packagesv1.PackageResponse{
		Code: 0,
	}, nil
}

func (b *Backend) GetPatchFile(req *packagesv1.Patch, srv packagesv1.PackageService_GetPatchFileServer) error {
	b.mu.RLock()
	defer b.mu.RUnlock()

	pkgId := req.GetId()
	pkg, err := GetPackageById(pkgId)
	if err != nil {
		return err
	}
	patchesDir, err := pkg.GetPatchesDir()
	if err != nil {
		return err
	}
	patchName := req.GetName()
	patchFile := filepath.Join(patchesDir, patchName)
	file, err := os.Open(patchFile)
	if err != nil {
		return err
	}
	defer file.Close()
	buffer := make([]byte, 1024*1024) // 1MB 缓冲区
	for {
		n, err := file.Read(buffer)
		if err != nil {
			if err == io.EOF {
				err = nil
			}
			return err
		}
		if n == 0 {
			return nil
		}
		if err := srv.Send(&packagesv1.FileResponse{Data: buffer[:n]}); err != nil {
			return err
		}
	}
}
