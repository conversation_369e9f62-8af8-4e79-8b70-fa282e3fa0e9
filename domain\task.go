package domain

import "time"

type Task struct {
	Id              int32     `json:"id" gorm:"primary_key"`
	Name            string    `json:"name"`
	Type            string    `json:"type"`
	Content         string    `json:"content"`
	Progress        string    `json:"progress"`
	ExtractProgress string    `json:"extract_progress"`
	State           string    `json:"state"`
	PackageId       int32     `json:"package_id"`
	ErrorLog        string    `json:"error_log" gorm:"type:text"`
	CreatedAt       time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP;type:datetime"`
}

var TaskNames = struct {
	Health    string
	Backup    string
	Restore   string
	Start     string
	Stop      string
	Download  string
	Install   string
	Uninstall string
	Upload    string
	Update    string
	Upgrade   string
	Export    string
}{
	Health:    "health",
	Backup:    "backup",
	Restore:   "restore",
	Start:     "start",
	Stop:      "stop",
	Download:  "download",
	Install:   "install",
	Uninstall: "uninstall",
	Upload:    "upload",
	Update:    "update",
	Upgrade:   "upgrade",
	Export:    "export",
}

var TaskTypes = struct {
	Command string
	File    string
}{
	Command: "command",
	File:    "file",
}

var TaskStates = struct {
	Running  string
	Finished string
	Failed   string
}{
	Running:  "running",
	Finished: "finished",
	Failed:   "failed",
}

func (t *Task) Merge(src *Task) {
	if src.Name != "" {
		t.Name = src.Name
	}
	if src.Type != "" {
		t.Type = src.Type
	}
	if src.Content != "" {
		t.Content = src.Content
	}
	if src.Progress != "" {
		t.Progress = src.Progress
	}
	if src.ExtractProgress != "" {
		t.ExtractProgress = src.ExtractProgress
	}
	if src.State != "" {
		t.State = src.State
	}
	if src.PackageId != 0 {
		t.PackageId = src.PackageId
	}
	if src.ErrorLog != "" {
		t.ErrorLog = src.ErrorLog
	}
}
