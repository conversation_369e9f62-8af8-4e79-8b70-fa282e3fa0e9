import{R as e,r,e as t,j as o,_ as n,b as c}from"./index.6227d37e.js";function a(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,o)}return t}function s(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?a(Object(t),!0).forEach((function(r){n(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function i(e,n){var c=r.exports.useContext(t).prefixCls,a=void 0===c?"arco":c,i=e.spin,l=e.className,p=s(s({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(l?l+" ":"").concat(a,"-icon ").concat(a,"-icon-check")});return i&&(p.className="".concat(p.className," ").concat(a,"-icon-loading")),delete p.spin,delete p.isIcon,o("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...p,children:o("path",{d:"M41.678 11.05 19.05 33.678 6.322 20.95"})})}var l=e.forwardRef(i);l.defaultProps={isIcon:!0},l.displayName="IconCheck";var p=l;function f(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,o)}return t}function u(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?f(Object(t),!0).forEach((function(r){n(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):f(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}function O(e,n){var a=r.exports.useContext(t).prefixCls,s=void 0===a?"arco":a,i=e.spin,l=e.className,p=u(u({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(l?l+" ":"").concat(s,"-icon ").concat(s,"-icon-exclamation")});return i&&(p.className="".concat(p.className," ").concat(s,"-icon-loading")),delete p.spin,delete p.isIcon,c("svg",{fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48",...p,children:[o("path",{d:"M23 9H25V30H23z"}),o("path",{fill:"currentColor",stroke:"none",d:"M23 9H25V30H23z"}),o("path",{d:"M23 37H25V39H23z"}),o("path",{fill:"currentColor",stroke:"none",d:"M23 37H25V39H23z"})]})}var d=e.forwardRef(O);d.defaultProps={isIcon:!0},d.displayName="IconExclamation";var b=d;export{p as I,b as a};
